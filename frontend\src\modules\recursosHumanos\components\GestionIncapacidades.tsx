import React, { useState } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import {
  faHeart,
  faPlus,
  faSearch,
  faEdit,
  faTrash,
  faEye,
  faDownload,
  faCalendarAlt,
  faUserMd,
  faFileAlt,
  faExclamationTriangle,
  faCheckCircle,
  faTimesCircle,
  faClock,
  faMoneyBillWave,
  faPercent,
  faFilter
} from '@fortawesome/free-solid-svg-icons';
import { Button } from '../../../components/ui/Button';
import { Input } from '../../../components/ui/Input';
import { toast } from 'react-hot-toast';
import { useAuth } from '../../../services/authService';
import { Incapacidad, IncapacidadFormData } from '../../../types/recursosHumanos';

// Schema de validación
const incapacidadSchema = z.object({
  empleado_id: z.number().min(1, 'Debe seleccionar un empleado'),
  tipo_incapacidad: z.enum(['Enfermedad General', 'Accidente Trabajo', 'Enfermedad Profesional', 'Maternidad', 'Paternidad']),
  fecha_inicio: z.string().min(1, 'Fecha de inicio requerida'),
  fecha_fin: z.string().min(1, 'Fecha de fin requerida'),
  entidad_emisora: z.enum(['EPS', 'ARL', 'Medicina Laboral']),
  numero_incapacidad: z.string().min(1, 'Número de incapacidad requerido'),
  diagnostico: z.string().min(1, 'Diagnóstico requerido'),
  porcentaje_pago: z.number().min(0).max(100, 'Porcentaje debe estar entre 0 y 100'),
  observaciones: z.string().optional(),
  archivo_soporte: z.string().optional(),
});

// Datos mock
const mockIncapacidades: Incapacidad[] = [
  {
    id: 1,
    hospital_id: 1,
    empleado_id: 1,
    empleado_nombre: 'Carlos Andrés Rodríguez',
    tipo_incapacidad: 'Enfermedad General',
    fecha_inicio: '2024-01-15',
    fecha_fin: '2024-01-20',
    dias_incapacidad: 5,
    entidad_emisora: 'EPS',
    numero_incapacidad: 'EPS-2024-001',
    diagnostico: 'Gripe común',
    porcentaje_pago: 66.67,
    valor_dia: 166667,
    valor_total: 833335,
    estado: 'Finalizada',
    observaciones: 'Incapacidad por gripe estacional',
    created_at: '2024-01-15T08:00:00Z',
    updated_at: '2024-01-20T17:00:00Z'
  },
  {
    id: 2,
    hospital_id: 1,
    empleado_id: 2,
    empleado_nombre: 'María Alejandra López',
    tipo_incapacidad: 'Accidente Trabajo',
    fecha_inicio: '2024-01-18',
    fecha_fin: '2024-02-02',
    dias_incapacidad: 15,
    entidad_emisora: 'ARL',
    numero_incapacidad: 'ARL-2024-005',
    diagnostico: 'Fractura de muñeca derecha',
    porcentaje_pago: 100,
    valor_dia: 133333,
    valor_total: 2000000,
    estado: 'Activa',
    observaciones: 'Accidente en área de trabajo',
    archivo_soporte: 'soporte_arl_005.pdf',
    created_at: '2024-01-18T10:30:00Z',
    updated_at: '2024-01-18T10:30:00Z'
  },
  {
    id: 3,
    hospital_id: 1,
    empleado_id: 3,
    empleado_nombre: 'Ana María González',
    tipo_incapacidad: 'Maternidad',
    fecha_inicio: '2024-02-01',
    fecha_fin: '2024-05-30',
    dias_incapacidad: 119,
    entidad_emisora: 'EPS',
    numero_incapacidad: 'EPS-MAT-2024-003',
    diagnostico: 'Licencia de maternidad',
    porcentaje_pago: 100,
    valor_dia: 200000,
    valor_total: 23800000,
    estado: 'Activa',
    observaciones: 'Licencia de maternidad completa',
    created_at: '2024-02-01T09:00:00Z',
    updated_at: '2024-02-01T09:00:00Z'
  }
];

const mockEmpleados = [
  { id: 1, nombre: 'Carlos Andrés Rodríguez' },
  { id: 2, nombre: 'María Alejandra López' },
  { id: 3, nombre: 'Ana María González' },
  { id: 4, nombre: 'Luis Fernando Martínez' },
  { id: 5, nombre: 'Patricia Hernández' }
];

const GestionIncapacidades: React.FC = () => {
  const [incapacidades, setIncapacidades] = useState<Incapacidad[]>(mockIncapacidades);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [editingIncapacidad, setEditingIncapacidad] = useState<Incapacidad | null>(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [filtroEstado, setFiltroEstado] = useState<string>('todos');
  const [filtroTipo, setFiltroTipo] = useState<string>('todos');
  const { user } = useAuth();

  const {
    register,
    handleSubmit,
    formState: { errors },
    reset,
    setValue,
    watch
  } = useForm<IncapacidadFormData>({
    resolver: zodResolver(incapacidadSchema),
    defaultValues: {
      hospital_id: user?.hospital_id || 1,
      porcentaje_pago: 66.67
    }
  });

  const fechaInicio = watch('fecha_inicio');
  const fechaFin = watch('fecha_fin');

  // Calcular días automáticamente
  React.useEffect(() => {
    if (fechaInicio && fechaFin) {
      const inicio = new Date(fechaInicio);
      const fin = new Date(fechaFin);
      const diffTime = Math.abs(fin.getTime() - inicio.getTime());
      const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24)) + 1;
      // Los días se calculan automáticamente en el backend
    }
  }, [fechaInicio, fechaFin]);

  const onSubmit = async (data: IncapacidadFormData) => {
    try {
      const inicio = new Date(data.fecha_inicio);
      const fin = new Date(data.fecha_fin);
      const diasIncapacidad = Math.ceil((fin.getTime() - inicio.getTime()) / (1000 * 60 * 60 * 24)) + 1;
      
      // Calcular valores (simulado)
      const salarioBase = 5000000; // Obtener del empleado
      const valorDia = salarioBase / 30;
      const valorTotal = valorDia * diasIncapacidad * (data.porcentaje_pago / 100);

      if (editingIncapacidad) {
        // Actualizar incapacidad existente
        const updatedIncapacidad: Incapacidad = {
          ...editingIncapacidad,
          ...data,
          dias_incapacidad: diasIncapacidad,
          valor_dia: valorDia,
          valor_total: valorTotal,
          updated_at: new Date().toISOString()
        };
        
        setIncapacidades(prev => 
          prev.map(inc => inc.id === editingIncapacidad.id ? updatedIncapacidad : inc)
        );
        toast.success('Incapacidad actualizada exitosamente');
      } else {
        // Crear nueva incapacidad
        const newIncapacidad: Incapacidad = {
          id: Math.max(...incapacidades.map(i => i.id)) + 1,
          ...data,
          empleado_nombre: mockEmpleados.find(e => e.id === data.empleado_id)?.nombre || '',
          dias_incapacidad: diasIncapacidad,
          valor_dia: valorDia,
          valor_total: valorTotal,
          estado: 'Activa',
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        };
        
        setIncapacidades(prev => [...prev, newIncapacidad]);
        toast.success('Incapacidad registrada exitosamente');
      }
      
      setIsModalOpen(false);
      setEditingIncapacidad(null);
      reset();
    } catch (error) {
      toast.error('Error al procesar la incapacidad');
    }
  };

  const handleEdit = (incapacidad: Incapacidad) => {
    setEditingIncapacidad(incapacidad);
    setValue('empleado_id', incapacidad.empleado_id);
    setValue('tipo_incapacidad', incapacidad.tipo_incapacidad);
    setValue('fecha_inicio', incapacidad.fecha_inicio);
    setValue('fecha_fin', incapacidad.fecha_fin);
    setValue('entidad_emisora', incapacidad.entidad_emisora);
    setValue('numero_incapacidad', incapacidad.numero_incapacidad);
    setValue('diagnostico', incapacidad.diagnostico);
    setValue('porcentaje_pago', incapacidad.porcentaje_pago);
    setValue('observaciones', incapacidad.observaciones || '');
    setValue('archivo_soporte', incapacidad.archivo_soporte || '');
    setIsModalOpen(true);
  };

  const handleDelete = (id: number) => {
    if (window.confirm('¿Está seguro de eliminar esta incapacidad?')) {
      setIncapacidades(prev => prev.filter(inc => inc.id !== id));
      toast.success('Incapacidad eliminada exitosamente');
    }
  };

  const filteredIncapacidades = incapacidades.filter(incapacidad => {
    const matchesSearch = incapacidad.empleado_nombre?.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         incapacidad.numero_incapacidad.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         incapacidad.diagnostico.toLowerCase().includes(searchTerm.toLowerCase());
    
    const matchesEstado = filtroEstado === 'todos' || incapacidad.estado === filtroEstado;
    const matchesTipo = filtroTipo === 'todos' || incapacidad.tipo_incapacidad === filtroTipo;
    
    return matchesSearch && matchesEstado && matchesTipo;
  });

  const getEstadoColor = (estado: string) => {
    switch (estado) {
      case 'Activa':
        return 'bg-success/20 text-success';
      case 'Finalizada':
        return 'bg-info/20 text-info';
      case 'Cancelada':
        return 'bg-error/20 text-error';
      default:
        return 'bg-secondary/20 text-secondary';
    }
  };

  const getEstadoIcon = (estado: string) => {
    switch (estado) {
      case 'Activa':
        return faCheckCircle;
      case 'Finalizada':
        return faTimesCircle;
      case 'Cancelada':
        return faExclamationTriangle;
      default:
        return faClock;
    }
  };

  const getTipoColor = (tipo: string) => {
    switch (tipo) {
      case 'Enfermedad General':
        return 'text-blue-600';
      case 'Accidente Trabajo':
        return 'text-red-600';
      case 'Enfermedad Profesional':
        return 'text-orange-600';
      case 'Maternidad':
        return 'text-pink-600';
      case 'Paternidad':
        return 'text-purple-600';
      default:
        return 'text-gray-600';
    }
  };

  const formatCurrency = (value: number) => {
    return new Intl.NumberFormat('es-CO', {
      style: 'currency',
      currency: 'COP',
      minimumFractionDigits: 0
    }).format(value);
  };

  const totalIncapacidades = incapacidades.length;
  const incapacidadesActivas = incapacidades.filter(i => i.estado === 'Activa').length;
  const totalValor = incapacidades.reduce((sum, i) => sum + i.valor_total, 0);
  const totalDias = incapacidades.reduce((sum, i) => sum + i.dias_incapacidad, 0);

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="bg-card p-6 rounded-lg shadow-lg">
        <div className="flex justify-between items-center">
          <div>
            <h2 className="text-2xl font-bold text-primary flex items-center">
              <FontAwesomeIcon icon={faHeart} className="mr-2" />
              Gestión de Incapacidades
            </h2>
            <p className="text-muted mt-1">
              Administración integral de incapacidades médicas y laborales
            </p>
          </div>
          <Button onClick={() => setIsModalOpen(true)}>
            <FontAwesomeIcon icon={faPlus} className="mr-2" />
            Nueva Incapacidad
          </Button>
        </div>
      </div>

      {/* Estadísticas */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <div className="bg-card p-6 rounded-lg shadow">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-muted">Total Incapacidades</p>
              <p className="text-2xl font-bold text-primary">{totalIncapacidades}</p>
            </div>
            <FontAwesomeIcon icon={faFileAlt} className="text-primary text-2xl" />
          </div>
        </div>

        <div className="bg-card p-6 rounded-lg shadow">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-muted">Activas</p>
              <p className="text-2xl font-bold text-success">{incapacidadesActivas}</p>
            </div>
            <FontAwesomeIcon icon={faCheckCircle} className="text-success text-2xl" />
          </div>
        </div>

        <div className="bg-card p-6 rounded-lg shadow">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-muted">Total Días</p>
              <p className="text-2xl font-bold text-warning">{totalDias}</p>
            </div>
            <FontAwesomeIcon icon={faCalendarAlt} className="text-warning text-2xl" />
          </div>
        </div>

        <div className="bg-card p-6 rounded-lg shadow">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-muted">Valor Total</p>
              <p className="text-xl font-bold text-info">{formatCurrency(totalValor)}</p>
            </div>
            <FontAwesomeIcon icon={faMoneyBillWave} className="text-info text-2xl" />
          </div>
        </div>
      </div>

      {/* Filtros */}
      <div className="bg-card p-6 rounded-lg shadow-lg">
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <div>
            <label className="block text-sm font-medium text-secondary mb-1">
              Buscar
            </label>
            <div className="relative">
              <FontAwesomeIcon icon={faSearch} className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted" />
              <Input
                type="text"
                placeholder="Empleado, número, diagnóstico..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10"
              />
            </div>
          </div>

          <div>
            <label className="block text-sm font-medium text-secondary mb-1">
              Estado
            </label>
            <select
              value={filtroEstado}
              onChange={(e) => setFiltroEstado(e.target.value)}
              className="w-full p-3 rounded-lg bg-secondary border border-color text-primary focus:ring-2 focus:ring-primary"
            >
              <option value="todos">Todos los estados</option>
              <option value="Activa">Activa</option>
              <option value="Finalizada">Finalizada</option>
              <option value="Cancelada">Cancelada</option>
            </select>
          </div>

          <div>
            <label className="block text-sm font-medium text-secondary mb-1">
              Tipo
            </label>
            <select
              value={filtroTipo}
              onChange={(e) => setFiltroTipo(e.target.value)}
              className="w-full p-3 rounded-lg bg-secondary border border-color text-primary focus:ring-2 focus:ring-primary"
            >
              <option value="todos">Todos los tipos</option>
              <option value="Enfermedad General">Enfermedad General</option>
              <option value="Accidente Trabajo">Accidente Trabajo</option>
              <option value="Enfermedad Profesional">Enfermedad Profesional</option>
              <option value="Maternidad">Maternidad</option>
              <option value="Paternidad">Paternidad</option>
            </select>
          </div>

          <div className="flex items-end">
            <Button variant="outline" className="w-full">
              <FontAwesomeIcon icon={faFilter} className="mr-2" />
              Limpiar Filtros
            </Button>
          </div>
        </div>
      </div>

      {/* Tabla de Incapacidades */}
      <div className="bg-card rounded-lg shadow-lg overflow-hidden">
        <div className="overflow-x-auto">
          <table className="w-full">
            <thead className="bg-secondary/50">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-secondary uppercase tracking-wider">
                  Empleado
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-secondary uppercase tracking-wider">
                  Tipo
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-secondary uppercase tracking-wider">
                  Período
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-secondary uppercase tracking-wider">
                  Días
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-secondary uppercase tracking-wider">
                  Entidad
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-secondary uppercase tracking-wider">
                  Valor
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-secondary uppercase tracking-wider">
                  Estado
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-secondary uppercase tracking-wider">
                  Acciones
                </th>
              </tr>
            </thead>
            <tbody className="bg-card divide-y divide-color">
              {filteredIncapacidades.map((incapacidad) => (
                <tr key={incapacidad.id} className="hover:bg-secondary/20 transition-colors">
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div>
                      <div className="text-sm font-medium text-primary">
                        {incapacidad.empleado_nombre}
                      </div>
                      <div className="text-sm text-muted">
                        {incapacidad.numero_incapacidad}
                      </div>
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <span className={`text-sm font-medium ${getTipoColor(incapacidad.tipo_incapacidad)}`}>
                      {incapacidad.tipo_incapacidad}
                    </span>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-primary">
                    <div>
                      <div>{new Date(incapacidad.fecha_inicio).toLocaleDateString()}</div>
                      <div className="text-muted">
                        al {new Date(incapacidad.fecha_fin).toLocaleDateString()}
                      </div>
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="text-sm font-medium text-primary">
                      {incapacidad.dias_incapacidad}
                    </div>
                    <div className="text-sm text-muted">
                      {incapacidad.porcentaje_pago}% pago
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-primary">
                    {incapacidad.entidad_emisora}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="text-sm font-medium text-primary">
                      {formatCurrency(incapacidad.valor_total)}
                    </div>
                    <div className="text-sm text-muted">
                      {formatCurrency(incapacidad.valor_dia)}/día
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getEstadoColor(incapacidad.estado)}`}>
                      <FontAwesomeIcon icon={getEstadoIcon(incapacidad.estado)} className="mr-1" />
                      {incapacidad.estado}
                    </span>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                    <div className="flex space-x-2">
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => handleEdit(incapacidad)}
                        className="text-blue-600 border-blue-600 hover:bg-blue-50"
                      >
                        <FontAwesomeIcon icon={faEdit} />
                      </Button>
                      <Button
                        variant="outline"
                        size="sm"
                        className="text-green-600 border-green-600 hover:bg-green-50"
                      >
                        <FontAwesomeIcon icon={faEye} />
                      </Button>
                      {incapacidad.archivo_soporte && (
                        <Button
                          variant="outline"
                          size="sm"
                          className="text-purple-600 border-purple-600 hover:bg-purple-50"
                        >
                          <FontAwesomeIcon icon={faDownload} />
                        </Button>
                      )}
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => handleDelete(incapacidad.id)}
                        className="text-red-600 border-red-600 hover:bg-red-50"
                      >
                        <FontAwesomeIcon icon={faTrash} />
                      </Button>
                    </div>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>
    </div>
  );
};

export default GestionIncapacidades;
