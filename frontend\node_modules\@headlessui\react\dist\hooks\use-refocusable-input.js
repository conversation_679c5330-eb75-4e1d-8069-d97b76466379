import{useRef as r}from"react";import{useEvent as u}from'./use-event.js';import{useEventListener as o}from'./use-event-listener.js';function i(e){let n=r({value:"",selectionStart:null,selectionEnd:null});return o(e,"blur",l=>{let t=l.target;t instanceof HTMLInputElement&&(n.current={value:t.value,selectionStart:t.selectionStart,selectionEnd:t.selectionEnd})}),u(()=>{if(document.activeElement!==e&&e instanceof HTMLInputElement&&e.isConnected){if(e.focus({preventScroll:!0}),e.value!==n.current.value)e.setSelectionRange(e.value.length,e.value.length);else{let{selectionStart:l,selectionEnd:t}=n.current;l!==null&&t!==null&&e.setSelectionRange(l,t)}n.current={value:"",selectionStart:null,selectionEnd:null}}})}export{i as useRefocusableInput};
