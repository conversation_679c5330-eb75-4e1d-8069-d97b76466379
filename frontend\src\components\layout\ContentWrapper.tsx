import { ReactNode } from 'react';

interface ContentWrapperProps {
  children: ReactNode;
  className?: string;
  maxWidth?: 'sm' | 'md' | 'lg' | 'xl' | '2xl' | 'full';
  padding?: 'none' | 'sm' | 'md' | 'lg' | 'xl';
}

export const ContentWrapper = ({
  children,
  className = '',
  maxWidth = 'full',
  padding = 'lg'
}: ContentWrapperProps) => {
  const maxWidthClasses = {
    sm: 'max-w-sm',
    md: 'max-w-md',
    lg: 'max-w-4xl',
    xl: 'max-w-6xl',
    '2xl': 'max-w-7xl',
    full: 'max-w-full'
  };

  const paddingClasses = {
    none: '',
    sm: 'p-3',
    md: 'p-4',
    lg: 'p-6',
    xl: 'p-8'
  };

  return (
    <div className={`container-responsive ${maxWidthClasses[maxWidth]} ${paddingClasses[padding]} ${className}`}>
      {children}
    </div>
  );
};

interface PageHeaderProps {
  title: string;
  subtitle?: string;
  icon?: ReactNode;
  actions?: ReactNode;
  className?: string;
}

export const PageHeader = ({
  title,
  subtitle,
  icon,
  actions,
  className = ''
}: PageHeaderProps) => {
  return (
    <div className={`flex flex-col sm:flex-row sm:items-center sm:justify-between mb-8 ${className}`}>
      <div className="flex items-center space-x-4 mb-4 sm:mb-0">
        {icon && (
          <div className="w-12 h-12 rounded-xl bg-gradient-to-br from-blue-500/20 to-purple-600/20 flex items-center justify-center">
            {icon}
          </div>
        )}
        <div>
          <h1 className="text-3xl font-bold text-adaptive">{title}</h1>
          {subtitle && (
            <p className="text-adaptive-muted mt-1">{subtitle}</p>
          )}
        </div>
      </div>
      {actions && (
        <div className="flex items-center space-x-3">
          {actions}
        </div>
      )}
    </div>
  );
};

interface StatsGridProps {
  children: ReactNode;
  columns?: 1 | 2 | 3 | 4;
  className?: string;
}

export const StatsGrid = ({
  children,
  columns = 4,
  className = ''
}: StatsGridProps) => {
  const gridClasses = {
    1: 'grid-cols-1',
    2: 'grid-cols-1 md:grid-cols-2',
    3: 'grid-cols-1 md:grid-cols-3',
    4: 'grid-cols-1 sm:grid-cols-2 lg:grid-cols-4'
  };

  return (
    <div className={`grid ${gridClasses[columns]} gap-6 mb-8 ${className}`}>
      {children}
    </div>
  );
};

interface FiltersContainerProps {
  children: ReactNode;
  className?: string;
}

export const FiltersContainer = ({
  children,
  className = ''
}: FiltersContainerProps) => {
  return (
    <div className={`glass-card p-6 rounded-xl shadow-lg mb-8 ${className}`}>
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        {children}
      </div>
    </div>
  );
};

interface TableContainerProps {
  children: ReactNode;
  className?: string;
}

export const TableContainer = ({
  children,
  className = ''
}: TableContainerProps) => {
  return (
    <div className={`glass-card rounded-xl shadow-lg overflow-hidden ${className}`}>
      <div className="table-container">
        {children}
      </div>
    </div>
  );
};

// Componentes compuestos
ContentWrapper.Header = PageHeader;
ContentWrapper.StatsGrid = StatsGrid;
ContentWrapper.Filters = FiltersContainer;
ContentWrapper.Table = TableContainer;
