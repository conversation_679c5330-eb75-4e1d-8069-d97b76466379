import { create } from 'zustand';
import { subscribeWithSelector } from 'zustand/middleware';
import { AmbulanceLocationUpdate, EmergencyCall, PatientAlert } from '../services/websocket.service';

// Tipos para el estado de ambulancias
export interface Ambulance {
  id: string;
  code: string;
  type: 'basic' | 'advanced' | 'transport';
  status: 'available' | 'en_route' | 'on_scene' | 'returning' | 'offline' | 'maintenance';
  location: {
    latitude: number;
    longitude: number;
    address?: string;
    lastUpdate: string;
  };
  crew: {
    driver: string;
    paramedic?: string;
    doctor?: string;
  };
  equipment: {
    gps: boolean;
    defibrillator: boolean;
    oxygen: boolean;
    medications: boolean;
  };
  currentService?: {
    emergencyId: string;
    patientId?: string;
    startTime: string;
    estimatedArrival?: string;
  };
  metrics: {
    totalServices: number;
    averageResponseTime: number;
    lastMaintenance: string;
    mileage: number;
  };
}

export interface EmergencyService {
  id: string;
  priority: 'low' | 'medium' | 'high' | 'critical';
  type: 'medical' | 'trauma' | 'cardiac' | 'respiratory' | 'other';
  status: 'pending' | 'assigned' | 'en_route' | 'on_scene' | 'completed' | 'cancelled';
  location: {
    latitude: number;
    longitude: number;
    address: string;
  };
  patient?: {
    id: string;
    name: string;
    age: number;
    condition: string;
  };
  assignedAmbulanceId?: string;
  createdAt: string;
  assignedAt?: string;
  arrivedAt?: string;
  completedAt?: string;
  description: string;
  notes?: string;
}

export interface AmbulanceMetrics {
  totalAmbulances: number;
  availableAmbulances: number;
  activeServices: number;
  averageResponseTime: number;
  completedServicesToday: number;
  pendingEmergencies: number;
}

interface AmbulanceState {
  // Estado de ambulancias
  ambulances: Ambulance[];
  selectedAmbulanceId: string | null;
  
  // Estado de servicios de emergencia
  emergencyServices: EmergencyService[];
  selectedServiceId: string | null;
  
  // Estado de alertas
  alerts: PatientAlert[];
  unreadAlertsCount: number;
  
  // Estado de métricas
  metrics: AmbulanceMetrics;
  
  // Estado de UI
  isLoading: boolean;
  error: string | null;
  mapCenter: { latitude: number; longitude: number };
  mapZoom: number;
  showAllAmbulances: boolean;
  filterStatus: string[];
  
  // Acciones
  setAmbulances: (ambulances: Ambulance[]) => void;
  updateAmbulanceLocation: (update: AmbulanceLocationUpdate) => void;
  updateAmbulanceStatus: (ambulanceId: string, status: Ambulance['status']) => void;
  selectAmbulance: (ambulanceId: string | null) => void;
  
  addEmergencyService: (service: EmergencyService) => void;
  updateEmergencyService: (serviceId: string, updates: Partial<EmergencyService>) => void;
  assignAmbulanceToService: (ambulanceId: string, serviceId: string) => void;
  selectService: (serviceId: string | null) => void;
  
  addAlert: (alert: PatientAlert) => void;
  acknowledgeAlert: (alertId: string) => void;
  clearAlerts: () => void;
  
  updateMetrics: (metrics: Partial<AmbulanceMetrics>) => void;
  
  setLoading: (loading: boolean) => void;
  setError: (error: string | null) => void;
  setMapCenter: (center: { latitude: number; longitude: number }) => void;
  setMapZoom: (zoom: number) => void;
  toggleShowAllAmbulances: () => void;
  setFilterStatus: (statuses: string[]) => void;
  
  // Selectores computados
  getAvailableAmbulances: () => Ambulance[];
  getAmbulanceById: (id: string) => Ambulance | undefined;
  getServiceById: (id: string) => EmergencyService | undefined;
  getPendingServices: () => EmergencyService[];
  getActiveServices: () => EmergencyService[];
  getCriticalAlerts: () => PatientAlert[];
}

export const useAmbulanceStore = create<AmbulanceState>()(
  subscribeWithSelector((set, get) => ({
    // Estado inicial
    ambulances: [],
    selectedAmbulanceId: null,
    emergencyServices: [],
    selectedServiceId: null,
    alerts: [],
    unreadAlertsCount: 0,
    metrics: {
      totalAmbulances: 0,
      availableAmbulances: 0,
      activeServices: 0,
      averageResponseTime: 0,
      completedServicesToday: 0,
      pendingEmergencies: 0
    },
    isLoading: false,
    error: null,
    mapCenter: { latitude: 6.2442, longitude: -75.5812 }, // Medellín por defecto
    mapZoom: 12,
    showAllAmbulances: true,
    filterStatus: [],

    // Acciones para ambulancias
    setAmbulances: (ambulances) => set({ ambulances }),

    updateAmbulanceLocation: (update) => set((state) => ({
      ambulances: state.ambulances.map(ambulance =>
        ambulance.id === update.ambulanceId
          ? {
              ...ambulance,
              location: {
                ...ambulance.location,
                latitude: update.latitude,
                longitude: update.longitude,
                lastUpdate: update.timestamp
              },
              status: update.status
            }
          : ambulance
      )
    })),

    updateAmbulanceStatus: (ambulanceId, status) => set((state) => ({
      ambulances: state.ambulances.map(ambulance =>
        ambulance.id === ambulanceId
          ? { ...ambulance, status }
          : ambulance
      )
    })),

    selectAmbulance: (ambulanceId) => set({ selectedAmbulanceId: ambulanceId }),

    // Acciones para servicios de emergencia
    addEmergencyService: (service) => set((state) => ({
      emergencyServices: [...state.emergencyServices, service]
    })),

    updateEmergencyService: (serviceId, updates) => set((state) => ({
      emergencyServices: state.emergencyServices.map(service =>
        service.id === serviceId
          ? { ...service, ...updates }
          : service
      )
    })),

    assignAmbulanceToService: (ambulanceId, serviceId) => set((state) => {
      const now = new Date().toISOString();
      
      return {
        ambulances: state.ambulances.map(ambulance =>
          ambulance.id === ambulanceId
            ? {
                ...ambulance,
                status: 'en_route' as const,
                currentService: {
                  emergencyId: serviceId,
                  startTime: now
                }
              }
            : ambulance
        ),
        emergencyServices: state.emergencyServices.map(service =>
          service.id === serviceId
            ? {
                ...service,
                status: 'assigned' as const,
                assignedAmbulanceId: ambulanceId,
                assignedAt: now
              }
            : service
        )
      };
    }),

    selectService: (serviceId) => set({ selectedServiceId: serviceId }),

    // Acciones para alertas
    addAlert: (alert) => set((state) => ({
      alerts: [alert, ...state.alerts],
      unreadAlertsCount: state.unreadAlertsCount + 1
    })),

    acknowledgeAlert: (alertId) => set((state) => ({
      alerts: state.alerts.map(alert =>
        alert.id === alertId
          ? { ...alert, acknowledged: true }
          : alert
      ),
      unreadAlertsCount: Math.max(0, state.unreadAlertsCount - 1)
    })),

    clearAlerts: () => set({ alerts: [], unreadAlertsCount: 0 }),

    // Acciones para métricas
    updateMetrics: (metrics) => set((state) => ({
      metrics: { ...state.metrics, ...metrics }
    })),

    // Acciones de UI
    setLoading: (loading) => set({ isLoading: loading }),
    setError: (error) => set({ error }),
    setMapCenter: (center) => set({ mapCenter: center }),
    setMapZoom: (zoom) => set({ mapZoom: zoom }),
    toggleShowAllAmbulances: () => set((state) => ({ 
      showAllAmbulances: !state.showAllAmbulances 
    })),
    setFilterStatus: (statuses) => set({ filterStatus: statuses }),

    // Selectores computados
    getAvailableAmbulances: () => {
      const state = get();
      return state.ambulances.filter(ambulance => 
        ambulance.status === 'available'
      );
    },

    getAmbulanceById: (id) => {
      const state = get();
      return state.ambulances.find(ambulance => ambulance.id === id);
    },

    getServiceById: (id) => {
      const state = get();
      return state.emergencyServices.find(service => service.id === id);
    },

    getPendingServices: () => {
      const state = get();
      return state.emergencyServices.filter(service => 
        service.status === 'pending'
      );
    },

    getActiveServices: () => {
      const state = get();
      return state.emergencyServices.filter(service => 
        ['assigned', 'en_route', 'on_scene'].includes(service.status)
      );
    },

    getCriticalAlerts: () => {
      const state = get();
      return state.alerts.filter(alert => 
        alert.severity === 'critical' && !alert.acknowledged
      );
    }
  }))
);

// Hook para suscribirse a cambios específicos
export const useAmbulanceMetrics = () => useAmbulanceStore(state => state.metrics);
export const useSelectedAmbulance = () => {
  const selectedId = useAmbulanceStore(state => state.selectedAmbulanceId);
  const getAmbulanceById = useAmbulanceStore(state => state.getAmbulanceById);
  return selectedId ? getAmbulanceById(selectedId) : null;
};

export const useAvailableAmbulances = () => useAmbulanceStore(state => state.getAvailableAmbulances());
export const usePendingServices = () => useAmbulanceStore(state => state.getPendingServices());
export const useCriticalAlerts = () => useAmbulanceStore(state => state.getCriticalAlerts());

export default useAmbulanceStore;
