import { useState } from "react";
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { quirofanosService } from "../../services/quirofanosService";
import { Quirofano } from "../../types";
import { Button } from "../../components/ui/Button";
import { Input } from "../../components/ui/Input";
import { Select } from "../../components/ui/Select";
import { useNavigate } from "react-router-dom";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import { faEdit, faTrash, faSave, faTimes, faEye, faChartLine, faCalendarPlus } from "@fortawesome/free-solid-svg-icons";

export const Quirofanos = () => {
  const queryClient = useQueryClient();
  const navigate = useNavigate();
  const [isEditing, setIsEditing] = useState(false);
  const [formData, setFormData] = useState<Partial<Quirofano>>({
    numero: "",
    estado: "Disponible",
    ubicacion: "",
  });

  // Obtener lista de quirófanos
  const { data: quirofanos = [], isLoading, isError } = useQuery({
    queryKey: ["quirofanos"],
    queryFn: () => quirofanosService.getAll(),
  });

  // Mutación para crear quirófano
  const createMutation = useMutation({
    mutationFn: (data: Omit<Quirofano, "id" | "created_at">) => {
      return quirofanosService.create(data);
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["quirofanos"] });
      resetForm();
    },
  });

  // Mutación para actualizar quirófano
  const updateMutation = useMutation({
    mutationFn: ({ id, data }: { id: string; data: Partial<Quirofano> }) => {
      return quirofanosService.update(id, data);
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["quirofanos"] });
      resetForm();
    },
  });

  // Mutación para eliminar quirófano
  const deleteMutation = useMutation({
    mutationFn: (id: string) => {
      return quirofanosService.delete(id);
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["quirofanos"] });
    },
  });

  // Manejar cambios en los campos del formulario
  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    setFormData((prev) => ({ ...prev, [name]: value }));
  };

  // Manejar envío del formulario
  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (isEditing && formData.id) {
      updateMutation.mutate({ id: formData.id, data: formData });
    } else {
      createMutation.mutate(formData as Omit<Quirofano, "id" | "created_at">);
    }
  };

  // Iniciar edición de quirófano
  const handleEdit = (quirofano: Quirofano) => {
    setFormData(quirofano);
    setIsEditing(true);
  };

  // Eliminar quirófano
  const handleDelete = (id: string) => {
    if (window.confirm("¿Está seguro de eliminar este quirófano?")) {
      deleteMutation.mutate(id);
    }
  };

  // Resetear formulario
  const resetForm = () => {
    setFormData({
      numero: "",
      estado: "Disponible",
      ubicacion: "",
    });
    setIsEditing(false);
  };

  // Obtener color según estado
  const getEstadoColor = (estado: string) => {
    switch (estado) {
      case "Disponible":
        return "text-green-500";
      case "Ocupado":
        return "text-yellow-500";
      case "En_Mantenimiento":
        return "text-red-500";
      default:
        return "";
    }
  };

  // Obtener texto legible del estado
  const getEstadoText = (estado: string) => {
    switch (estado) {
      case "En_Mantenimiento":
        return "En Mantenimiento";
      default:
        return estado;
    }
  };

  return (
    <div>
        <div className="flex justify-between items-center mb-6">
          <h1 className="text-2xl font-bold text-white">Quirófanos</h1>
          <div className="flex space-x-2">
            <Button variant="outline" onClick={() => navigate("/quirofanos/estadisticas")}>
              <FontAwesomeIcon icon={faChartLine} className="mr-2" />
              Estadísticas
            </Button>
            <Button variant="outline" onClick={() => navigate("/quirofanos/asignacion")}>
              <FontAwesomeIcon icon={faCalendarPlus} className="mr-2" />
              Asignaciones
            </Button>
          </div>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <div className="md:col-span-2">
            <div className="bg-gray-800 rounded-lg shadow-lg overflow-hidden">
              {isLoading ? (
                <p className="text-white text-center py-4">Cargando quirófanos...</p>
              ) : isError ? (
                <p className="text-red-500 text-center py-4">
                  Error al cargar los quirófanos
                </p>
              ) : quirofanos && quirofanos.length > 0 ? (
                <table className="min-w-full divide-y divide-gray-700">
                  <thead className="bg-gray-700">
                    <tr>
                      <th className="px-4 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">
                        Número
                      </th>
                      <th className="px-4 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">
                        Estado
                      </th>
                      <th className="px-4 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">
                        Ubicación
                      </th>
                      <th className="px-4 py-3 text-right text-xs font-medium text-gray-300 uppercase tracking-wider">
                        Acciones
                      </th>
                    </tr>
                  </thead>
                  <tbody className="divide-y divide-gray-700">
                    {quirofanos.map((quirofano: Quirofano) => (
                      <tr key={quirofano.id} className="hover:bg-gray-800">
                        <td className="px-4 py-3 whitespace-nowrap text-sm text-white">
                          {quirofano.numero}
                        </td>
                        <td className="px-4 py-3 whitespace-nowrap text-sm">
                          <span className={getEstadoColor(quirofano.estado)}>
                            {getEstadoText(quirofano.estado)}
                          </span>
                        </td>
                        <td className="px-4 py-3 whitespace-nowrap text-sm text-white">
                          {quirofano.ubicacion || "-"}
                        </td>
                        <td className="px-4 py-3 whitespace-nowrap text-sm text-right">
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => navigate(`/quirofanos/${quirofano.id}`)}
                            className="mr-1"
                            title="Ver detalles"
                          >
                            <FontAwesomeIcon icon={faEye} />
                          </Button>
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => handleEdit(quirofano)}
                            className="mr-1"
                            title="Editar"
                          >
                            <FontAwesomeIcon icon={faEdit} />
                          </Button>
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => handleDelete(quirofano.id)}
                            title="Eliminar"
                          >
                            <FontAwesomeIcon icon={faTrash} />
                          </Button>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              ) : (
                <p className="text-white text-center py-4">
                  No se encontraron quirófanos.
                </p>
              )}
            </div>
          </div>

          <div>
            <div className="bg-gray-800 rounded-lg shadow-lg p-6">
              <h2 className="text-xl font-semibold text-white mb-4">
                {isEditing ? "Editar Quirófano" : "Nuevo Quirófano"}
              </h2>
              <form onSubmit={handleSubmit}>
                <div className="mb-4">
                  <label className="block text-sm font-medium text-white mb-1">
                    Número *
                  </label>
                  <Input
                    type="text"
                    name="numero"
                    value={formData.numero}
                    onChange={handleChange}
                    required
                  />
                </div>
                <div className="mb-4">
                  <label className="block text-sm font-medium text-white mb-1">
                    Estado *
                  </label>
                  <Select
                    name="estado"
                    value={formData.estado}
                    onChange={handleChange}
                    required
                  >
                    <option value="Disponible">Disponible</option>
                    <option value="Ocupado">Ocupado</option>
                    <option value="En_Mantenimiento">En Mantenimiento</option>
                  </Select>
                </div>
                <div className="mb-4">
                  <label className="block text-sm font-medium text-white mb-1">
                    Ubicación
                  </label>
                  <Input
                    type="text"
                    name="ubicacion"
                    value={formData.ubicacion}
                    onChange={handleChange}
                  />
                </div>
                <div className="flex justify-end">
                  {isEditing && (
                    <Button
                      type="button"
                      variant="secondary"
                      onClick={resetForm}
                      className="mr-2"
                    >
                      <FontAwesomeIcon icon={faTimes} className="mr-2" />
                      Cancelar
                    </Button>
                  )}
                  <Button
                    type="submit"
                    disabled={createMutation.isPending || updateMutation.isPending}
                  >
                    <FontAwesomeIcon icon={faSave} className="mr-2" />
                    {createMutation.isPending || updateMutation.isPending
                      ? "Guardando..."
                      : "Guardar"}
                  </Button>
                </div>
              </form>
            </div>
          </div>
        </div>
      </div>
  );
};

