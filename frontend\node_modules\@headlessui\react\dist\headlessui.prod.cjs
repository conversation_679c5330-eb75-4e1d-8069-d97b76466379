"use strict";var uu=Object.create;var Un=Object.defineProperty;var cu=Object.getOwnPropertyDescriptor;var fu=Object.getOwnPropertyNames;var du=Object.getPrototypeOf,pu=Object.prototype.hasOwnProperty;var mu=(e,n,t)=>n in e?Un(e,n,{enumerable:!0,configurable:!0,writable:!0,value:t}):e[n]=t;var Tu=(e,n)=>{for(var t in n)Un(e,t,{get:n[t],enumerable:!0})},ji=(e,n,t,o)=>{if(n&&typeof n=="object"||typeof n=="function")for(let r of fu(n))!pu.call(e,r)&&r!==t&&Un(e,r,{get:()=>n[r],enumerable:!(o=cu(n,r))||o.enumerable});return e};var ie=(e,n,t)=>(t=e!=null?uu(du(e)):{},ji(n||!e||!e.__esModule?Un(t,"default",{value:e,enumerable:!0}):t,e)),bu=e=>ji(Un({},"__esModule",{value:!0}),e);var Be=(e,n,t)=>(mu(e,typeof n!="symbol"?n+"":n,t),t),Ki=(e,n,t)=>{if(!n.has(e))throw TypeError("Cannot "+t)};var Ke=(e,n,t)=>(Ki(e,n,"read from private field"),t?t.call(e):n.get(e)),To=(e,n,t)=>{if(n.has(e))throw TypeError("Cannot add the same private member more than once");n instanceof WeakSet?n.add(e):n.set(e,t)},vr=(e,n,t,o)=>(Ki(e,n,"write to private field"),o?o.call(e,t):n.set(e,t),t);var om={};Tu(om,{Button:()=>Nr,Checkbox:()=>ju,CloseButton:()=>zu,Combobox:()=>Sf,ComboboxButton:()=>Kl,ComboboxInput:()=>zl,ComboboxLabel:()=>Xl,ComboboxOption:()=>ql,ComboboxOptions:()=>Yl,DataInteractive:()=>Of,Description:()=>xt,Dialog:()=>nd,DialogBackdrop:()=>ed,DialogDescription:()=>td,DialogPanel:()=>ca,DialogTitle:()=>fa,Disclosure:()=>md,DisclosureButton:()=>ba,DisclosurePanel:()=>ga,Field:()=>gd,Fieldset:()=>hd,FocusTrap:()=>vi,FocusTrapFeatures:()=>rr,Input:()=>xd,Label:()=>Xe,Legend:()=>Rd,Listbox:()=>$d,ListboxButton:()=>Oa,ListboxLabel:()=>La,ListboxOption:()=>Ia,ListboxOptions:()=>Da,ListboxSelectedOption:()=>Fa,Menu:()=>np,MenuButton:()=>wa,MenuHeading:()=>$a,MenuItem:()=>Ha,MenuItems:()=>_a,MenuSection:()=>ka,MenuSeparator:()=>Na,Popover:()=>yp,PopoverBackdrop:()=>Va,PopoverButton:()=>Ga,PopoverGroup:()=>Ka,PopoverOverlay:()=>Wa,PopoverPanel:()=>ja,Portal:()=>Qe,Radio:()=>Xa,RadioGroup:()=>Op,RadioGroupDescription:()=>qa,RadioGroupLabel:()=>Ya,RadioGroupOption:()=>za,Select:()=>Ip,Switch:()=>kp,SwitchDescription:()=>eu,SwitchGroup:()=>Qa,SwitchLabel:()=>Za,Tab:()=>Zp,TabGroup:()=>ru,TabList:()=>iu,TabPanel:()=>lu,TabPanels:()=>su,Textarea:()=>nm,Transition:()=>xi,TransitionChild:()=>fo,useClose:()=>Fo});module.exports=bu(om);var zi=ie(require("react"),1),Gn=typeof document!="undefined"?zi.default.useLayoutEffect:()=>{};var bo=require("react");function Er(e){let n=(0,bo.useRef)(null);return Gn(()=>{n.current=e},[e]),(0,bo.useCallback)((...t)=>{let o=n.current;return o==null?void 0:o(...t)},[])}var It=e=>{var n;return(n=e==null?void 0:e.ownerDocument)!==null&&n!==void 0?n:document},ht=e=>e&&"window"in e&&e.window===e?e:It(e).defaultView||window;function gu(e){var n;return typeof window=="undefined"||window.navigator==null?!1:((n=window.navigator.userAgentData)===null||n===void 0?void 0:n.brands.some(t=>e.test(t.brand)))||e.test(window.navigator.userAgent)}function yu(e){var n;return typeof window!="undefined"&&window.navigator!=null?e.test(((n=window.navigator.userAgentData)===null||n===void 0?void 0:n.platform)||window.navigator.platform):!1}function xr(){return yu(/^Mac/i)}function Pr(){return gu(/Android/i)}function Rr(e){return e.mozInputSource===0&&e.isTrusted?!0:Pr()&&e.pointerType?e.type==="click"&&e.buttons===1:e.detail===0&&!e.pointerType}var go=require("react");var Sr=class{isDefaultPrevented(){return this.nativeEvent.defaultPrevented}preventDefault(){this.defaultPrevented=!0,this.nativeEvent.preventDefault()}stopPropagation(){this.nativeEvent.stopPropagation(),this.isPropagationStopped=()=>!0}isPropagationStopped(){return!1}persist(){}constructor(n,t){this.nativeEvent=t,this.target=t.target,this.currentTarget=t.currentTarget,this.relatedTarget=t.relatedTarget,this.bubbles=t.bubbles,this.cancelable=t.cancelable,this.defaultPrevented=t.defaultPrevented,this.eventPhase=t.eventPhase,this.isTrusted=t.isTrusted,this.timeStamp=t.timeStamp,this.type=n}};function yo(e){let n=(0,go.useRef)({isFocused:!1,observer:null});Gn(()=>{let o=n.current;return()=>{o.observer&&(o.observer.disconnect(),o.observer=null)}},[]);let t=Er(o=>{e==null||e(o)});return(0,go.useCallback)(o=>{if(o.target instanceof HTMLButtonElement||o.target instanceof HTMLInputElement||o.target instanceof HTMLTextAreaElement||o.target instanceof HTMLSelectElement){n.current.isFocused=!0;let r=o.target,i=s=>{n.current.isFocused=!1,r.disabled&&t(new Sr("blur",s)),n.current.observer&&(n.current.observer.disconnect(),n.current.observer=null)};r.addEventListener("focusout",i,{once:!0}),n.current.observer=new MutationObserver(()=>{if(n.current.isFocused&&r.disabled){var s;(s=n.current.observer)===null||s===void 0||s.disconnect();let l=r===document.activeElement?null:document.activeElement;r.dispatchEvent(new FocusEvent("blur",{relatedTarget:l})),r.dispatchEvent(new FocusEvent("focusout",{bubbles:!0,relatedTarget:l}))}}),n.current.observer.observe(r,{attributes:!0,attributeFilter:["disabled"]})}},[t])}var Cr=require("react");function Ar(e){let{isDisabled:n,onFocus:t,onBlur:o,onFocusChange:r}=e,i=(0,Cr.useCallback)(a=>{if(a.target===a.currentTarget)return o&&o(a),r&&r(!1),!0},[o,r]),s=yo(i),l=(0,Cr.useCallback)(a=>{let u=It(a.target);a.target===a.currentTarget&&u.activeElement===a.target&&(t&&t(a),r&&r(!0),s(a))},[r,t,s]);return{focusProps:{onFocus:!n&&(t||r||o)?l:void 0,onBlur:!n&&(o||r)?i:void 0}}}var Ir=require("react");var Vn=null,Or=new Set,Wn=new Map,Xt=!1,Lr=!1,hu={Tab:!0,Escape:!0};function Fr(e,n){for(let t of Or)t(e,n)}function vu(e){return!(e.metaKey||!xr()&&e.altKey||e.ctrlKey||e.key==="Control"||e.key==="Shift"||e.key==="Meta")}function ho(e){Xt=!0,vu(e)&&(Vn="keyboard",Fr("keyboard",e))}function ze(e){Vn="pointer",(e.type==="mousedown"||e.type==="pointerdown")&&(Xt=!0,Fr("pointer",e))}function Xi(e){Rr(e)&&(Xt=!0,Vn="virtual")}function Yi(e){e.target===window||e.target===document||(!Xt&&!Lr&&(Vn="virtual",Fr("virtual",e)),Xt=!1,Lr=!1)}function qi(){Xt=!1,Lr=!0}function Dr(e){if(typeof window=="undefined"||Wn.get(ht(e)))return;let n=ht(e),t=It(e),o=n.HTMLElement.prototype.focus;n.HTMLElement.prototype.focus=function(){Xt=!0,o.apply(this,arguments)},t.addEventListener("keydown",ho,!0),t.addEventListener("keyup",ho,!0),t.addEventListener("click",Xi,!0),n.addEventListener("focus",Yi,!0),n.addEventListener("blur",qi,!1),typeof PointerEvent!="undefined"?(t.addEventListener("pointerdown",ze,!0),t.addEventListener("pointermove",ze,!0),t.addEventListener("pointerup",ze,!0)):(t.addEventListener("mousedown",ze,!0),t.addEventListener("mousemove",ze,!0),t.addEventListener("mouseup",ze,!0)),n.addEventListener("beforeunload",()=>{Ji(e)},{once:!0}),Wn.set(n,{focus:o})}var Ji=(e,n)=>{let t=ht(e),o=It(e);n&&o.removeEventListener("DOMContentLoaded",n),Wn.has(t)&&(t.HTMLElement.prototype.focus=Wn.get(t).focus,o.removeEventListener("keydown",ho,!0),o.removeEventListener("keyup",ho,!0),o.removeEventListener("click",Xi,!0),t.removeEventListener("focus",Yi,!0),t.removeEventListener("blur",qi,!1),typeof PointerEvent!="undefined"?(o.removeEventListener("pointerdown",ze,!0),o.removeEventListener("pointermove",ze,!0),o.removeEventListener("pointerup",ze,!0)):(o.removeEventListener("mousedown",ze,!0),o.removeEventListener("mousemove",ze,!0),o.removeEventListener("mouseup",ze,!0)),Wn.delete(t))};function Qi(e){let n=It(e),t;return n.readyState!=="loading"?Dr(e):(t=()=>{Dr(e)},n.addEventListener("DOMContentLoaded",t)),()=>Ji(e,t)}typeof document!="undefined"&&Qi();function vo(){return Vn!=="pointer"}var Eu=new Set(["checkbox","radio","range","color","file","image","button","submit","reset"]);function xu(e,n,t){var o;let r=typeof window!="undefined"?ht(t==null?void 0:t.target).HTMLInputElement:HTMLInputElement,i=typeof window!="undefined"?ht(t==null?void 0:t.target).HTMLTextAreaElement:HTMLTextAreaElement,s=typeof window!="undefined"?ht(t==null?void 0:t.target).HTMLElement:HTMLElement,l=typeof window!="undefined"?ht(t==null?void 0:t.target).KeyboardEvent:KeyboardEvent;return e=e||(t==null?void 0:t.target)instanceof r&&!Eu.has(t==null||(o=t.target)===null||o===void 0?void 0:o.type)||(t==null?void 0:t.target)instanceof i||(t==null?void 0:t.target)instanceof s&&(t==null?void 0:t.target.isContentEditable),!(e&&n==="keyboard"&&t instanceof l&&!hu[t.key])}function Mr(e,n,t){Dr(),(0,Ir.useEffect)(()=>{let o=(r,i)=>{xu(!!(t!=null&&t.isTextInput),r,i)&&e(vo())};return Or.add(o),()=>{Or.delete(o)}},n)}var jn=require("react");function wr(e){let{isDisabled:n,onBlurWithin:t,onFocusWithin:o,onFocusWithinChange:r}=e,i=(0,jn.useRef)({isFocusWithin:!1}),s=(0,jn.useCallback)(u=>{i.current.isFocusWithin&&!u.currentTarget.contains(u.relatedTarget)&&(i.current.isFocusWithin=!1,t&&t(u),r&&r(!1))},[t,r,i]),l=yo(s),a=(0,jn.useCallback)(u=>{!i.current.isFocusWithin&&document.activeElement===u.target&&(o&&o(u),r&&r(!0),i.current.isFocusWithin=!0,l(u))},[o,r,l]);return n?{focusWithinProps:{onFocus:void 0,onBlur:void 0}}:{focusWithinProps:{onFocus:a,onBlur:s}}}var vt=require("react"),Eo=!1,_r=0;function Hr(){Eo=!0,setTimeout(()=>{Eo=!1},50)}function Zi(e){e.pointerType==="touch"&&Hr()}function Pu(){if(typeof document!="undefined")return typeof PointerEvent!="undefined"?document.addEventListener("pointerup",Zi):document.addEventListener("touchend",Hr),_r++,()=>{_r--,!(_r>0)&&(typeof PointerEvent!="undefined"?document.removeEventListener("pointerup",Zi):document.removeEventListener("touchend",Hr))}}function fe(e){let{onHoverStart:n,onHoverChange:t,onHoverEnd:o,isDisabled:r}=e,[i,s]=(0,vt.useState)(!1),l=(0,vt.useRef)({isHovered:!1,ignoreEmulatedMouseEvents:!1,pointerType:"",target:null}).current;(0,vt.useEffect)(Pu,[]);let{hoverProps:a,triggerHoverEnd:u}=(0,vt.useMemo)(()=>{let c=(f,m)=>{if(l.pointerType=m,r||m==="touch"||l.isHovered||!f.currentTarget.contains(f.target))return;l.isHovered=!0;let T=f.currentTarget;l.target=T,n&&n({type:"hoverstart",target:T,pointerType:m}),t&&t(!0),s(!0)},d=(f,m)=>{if(l.pointerType="",l.target=null,m==="touch"||!l.isHovered)return;l.isHovered=!1;let T=f.currentTarget;o&&o({type:"hoverend",target:T,pointerType:m}),t&&t(!1),s(!1)},p={};return typeof PointerEvent!="undefined"?(p.onPointerEnter=f=>{Eo&&f.pointerType==="mouse"||c(f,f.pointerType)},p.onPointerLeave=f=>{!r&&f.currentTarget.contains(f.target)&&d(f,f.pointerType)}):(p.onTouchStart=()=>{l.ignoreEmulatedMouseEvents=!0},p.onMouseEnter=f=>{!l.ignoreEmulatedMouseEvents&&!Eo&&c(f,"mouse"),l.ignoreEmulatedMouseEvents=!1},p.onMouseLeave=f=>{!r&&f.currentTarget.contains(f.target)&&d(f,"mouse")}),{hoverProps:p,triggerHoverEnd:d}},[n,t,o,r,l]);return(0,vt.useEffect)(()=>{r&&u({currentTarget:l.target},l.pointerType)},[r]),{hoverProps:a,isHovered:i}}var Ft=require("react");function ce(e={}){let{autoFocus:n=!1,isTextInput:t,within:o}=e,r=(0,Ft.useRef)({isFocused:!1,isFocusVisible:n||vo()}),[i,s]=(0,Ft.useState)(!1),[l,a]=(0,Ft.useState)(()=>r.current.isFocused&&r.current.isFocusVisible),u=(0,Ft.useCallback)(()=>a(r.current.isFocused&&r.current.isFocusVisible),[]),c=(0,Ft.useCallback)(f=>{r.current.isFocused=f,s(f),u()},[u]);Mr(f=>{r.current.isFocusVisible=f,u()},[],{isTextInput:t});let{focusProps:d}=Ar({isDisabled:o,onFocusChange:c}),{focusWithinProps:p}=wr({isDisabled:!o,onFocusWithinChange:c});return{isFocused:i,isFocusVisible:l,focusProps:o?p:d}}var rs=require("react");var Ro=require("react");var kr=class{constructor(){Be(this,"current",this.detect());Be(this,"handoffState","pending");Be(this,"currentId",0)}set(n){this.current!==n&&(this.handoffState="pending",this.currentId=0,this.current=n)}reset(){this.set(this.detect())}nextId(){return++this.currentId}get isServer(){return this.current==="server"}get isClient(){return this.current==="client"}detect(){return typeof window=="undefined"||typeof document=="undefined"?"server":"client"}handoff(){this.handoffState==="pending"&&(this.handoffState="complete")}get isHandoffComplete(){return this.handoffState==="complete"}},et=new kr;function xe(e){var n,t;return et.isServer?null:e?"ownerDocument"in e?e.ownerDocument:"current"in e?(t=(n=e.current)==null?void 0:n.ownerDocument)!=null?t:document:null:document}var xo=require("react");function Et(e){typeof queueMicrotask=="function"?queueMicrotask(e):Promise.resolve().then(e).catch(n=>setTimeout(()=>{throw n}))}function he(){let e=[],n={addEventListener(t,o,r,i){return t.addEventListener(o,r,i),n.add(()=>t.removeEventListener(o,r,i))},requestAnimationFrame(...t){let o=requestAnimationFrame(...t);return n.add(()=>cancelAnimationFrame(o))},nextFrame(...t){return n.requestAnimationFrame(()=>n.requestAnimationFrame(...t))},setTimeout(...t){let o=setTimeout(...t);return n.add(()=>clearTimeout(o))},microTask(...t){let o={current:!0};return Et(()=>{o.current&&t[0]()}),n.add(()=>{o.current=!1})},style(t,o,r){let i=t.style.getPropertyValue(o);return Object.assign(t.style,{[o]:r}),this.add(()=>{Object.assign(t.style,{[o]:i})})},group(t){let o=he();return t(o),this.add(()=>o.dispose())},add(t){return e.includes(t)||e.push(t),()=>{let o=e.indexOf(t);if(o>=0)for(let r of e.splice(o,1))r()}},dispose(){for(let t of e.splice(0))t()}};return n}function Re(){let[e]=(0,xo.useState)(he);return(0,xo.useEffect)(()=>()=>e.dispose(),[e]),e}var ts=ie(require("react"),1);var es=require("react");var Po=require("react");var W=(e,n)=>{et.isServer?(0,Po.useEffect)(e,n):(0,Po.useLayoutEffect)(e,n)};function pe(e){let n=(0,es.useRef)(e);return W(()=>{n.current=e},[e]),n}var E=function(n){let t=pe(n);return ts.default.useCallback((...o)=>t.current(...o),[t])};function Ru(e){let n=e.width/2,t=e.height/2;return{top:e.clientY-t,right:e.clientX+n,bottom:e.clientY+t,left:e.clientX-n}}function Su(e,n){return!(!e||!n||e.right<n.left||e.left>n.right||e.bottom<n.top||e.top>n.bottom)}function Se({disabled:e=!1}={}){let n=(0,Ro.useRef)(null),[t,o]=(0,Ro.useState)(!1),r=Re(),i=E(()=>{n.current=null,o(!1),r.dispose()}),s=E(l=>{if(r.dispose(),n.current===null){n.current=l.currentTarget,o(!0);{let a=xe(l.currentTarget);r.addEventListener(a,"pointerup",i,!1),r.addEventListener(a,"pointermove",u=>{if(n.current){let c=Ru(u);o(Su(c,n.current.getBoundingClientRect()))}},!1),r.addEventListener(a,"pointercancel",i,!1)}}});return{pressed:t,pressProps:e?{}:{onPointerDown:s,onPointerUp:i,onClick:i}}}var ln=ie(require("react"),1),ns=(0,ln.createContext)(void 0);function Te(){return(0,ln.useContext)(ns)}function So({value:e,children:n}){return ln.default.createElement(ns.Provider,{value:e},n)}var Oe=ie(require("react"),1);function Kn(...e){return Array.from(new Set(e.flatMap(n=>typeof n=="string"?n.split(" "):[]))).filter(Boolean).join(" ")}function Y(e,n,...t){if(e in n){let r=n[e];return typeof r=="function"?r(...t):r}let o=new Error(`Tried to handle "${e}" but there is no handler defined. Only defined handlers are: ${Object.keys(n).map(r=>`"${r}"`).join(", ")}.`);throw Error.captureStackTrace&&Error.captureStackTrace(o,Y),o}function H(){let e=Au();return(0,Oe.useCallback)(n=>Cu({mergeRefs:e,...n}),[e])}function Cu({ourProps:e,theirProps:n,slot:t,defaultTag:o,features:r,visible:i=!0,name:s,mergeRefs:l}){l=l!=null?l:Ou;let a=os(n,e);if(i)return Co(a,t,o,s,l);let u=r!=null?r:0;if(u&2){let{static:c=!1,...d}=a;if(c)return Co(d,t,o,s,l)}if(u&1){let{unmount:c=!0,...d}=a;return Y(c?0:1,{[0](){return null},[1](){return Co({...d,hidden:!0,style:{display:"none"}},t,o,s,l)}})}return Co(a,t,o,s,l)}function Co(e,n={},t,o,r){let{as:i=t,children:s,refName:l="ref",...a}=$r(e,["unmount","static"]),u=e.ref!==void 0?{[l]:e.ref}:{},c=typeof s=="function"?s(n):s;"className"in a&&a.className&&typeof a.className=="function"&&(a.className=a.className(n)),a["aria-labelledby"]&&a["aria-labelledby"]===a.id&&(a["aria-labelledby"]=void 0);let d={};if(n){let p=!1,f=[];for(let[m,T]of Object.entries(n))typeof T=="boolean"&&(p=!0),T===!0&&f.push(m.replace(/([A-Z])/g,b=>`-${b.toLowerCase()}`));if(p){d["data-headlessui-state"]=f.join(" ");for(let m of f)d[`data-${m}`]=""}}if(i===Oe.Fragment&&(Object.keys(st(a)).length>0||Object.keys(st(d)).length>0))if(!(0,Oe.isValidElement)(c)||Array.isArray(c)&&c.length>1){if(Object.keys(st(a)).length>0)throw new Error(['Passing props on "Fragment"!',"",`The current component <${o} /> is rendering a "Fragment".`,"However we need to passthrough the following props:",Object.keys(st(a)).concat(Object.keys(st(d))).map(p=>`  - ${p}`).join(`
`),"","You can apply a few solutions:",['Add an `as="..."` prop, to ensure that we render an actual element instead of a "Fragment".',"Render a single element as the child so that we can forward the props onto that element."].map(p=>`  - ${p}`).join(`
`)].join(`
`))}else{let p=c.props,f=p==null?void 0:p.className,m=typeof f=="function"?(...g)=>Kn(f(...g),a.className):Kn(f,a.className),T=m?{className:m}:{},b=os(c.props,st($r(a,["ref"])));for(let g in d)g in b&&delete d[g];return(0,Oe.cloneElement)(c,Object.assign({},b,d,u,{ref:r(Lu(c),u.ref)},T))}return(0,Oe.createElement)(i,Object.assign({},$r(a,["ref"]),i!==Oe.Fragment&&u,i!==Oe.Fragment&&d),c)}function Au(){let e=(0,Oe.useRef)([]),n=(0,Oe.useCallback)(t=>{for(let o of e.current)o!=null&&(typeof o=="function"?o(t):o.current=t)},[]);return(...t)=>{if(!t.every(o=>o==null))return e.current=t,n}}function Ou(...e){return e.every(n=>n==null)?void 0:n=>{for(let t of e)t!=null&&(typeof t=="function"?t(n):t.current=n)}}function os(...e){var o;if(e.length===0)return{};if(e.length===1)return e[0];let n={},t={};for(let r of e)for(let i in r)i.startsWith("on")&&typeof r[i]=="function"?((o=t[i])!=null||(t[i]=[]),t[i].push(r[i])):n[i]=r[i];if(n.disabled||n["aria-disabled"])for(let r in t)/^(on(?:Click|Pointer|Mouse|Key)(?:Down|Up|Press)?)$/.test(r)&&(t[r]=[i=>{var s;return(s=i==null?void 0:i.preventDefault)==null?void 0:s.call(i)}]);for(let r in t)Object.assign(n,{[r](i,...s){let l=t[r];for(let a of l){if((i instanceof Event||(i==null?void 0:i.nativeEvent)instanceof Event)&&i.defaultPrevented)return;a(i,...s)}}});return n}function se(...e){var o;if(e.length===0)return{};if(e.length===1)return e[0];let n={},t={};for(let r of e)for(let i in r)i.startsWith("on")&&typeof r[i]=="function"?((o=t[i])!=null||(t[i]=[]),t[i].push(r[i])):n[i]=r[i];for(let r in t)Object.assign(n,{[r](...i){let s=t[r];for(let l of s)l==null||l(...i)}});return n}function M(e){var n;return Object.assign((0,Oe.forwardRef)(e),{displayName:(n=e.displayName)!=null?n:e.name})}function st(e){let n=Object.assign({},e);for(let t in n)n[t]===void 0&&delete n[t];return n}function $r(e,n=[]){let t=Object.assign({},e);for(let o of n)o in t&&delete t[o];return t}function Lu(e){return Oe.default.version.split(".")[0]>="19"?e.props.ref:e.ref}var Du="button";function Iu(e,n){var T;let t=Te(),{disabled:o=t||!1,autoFocus:r=!1,...i}=e,{isFocusVisible:s,focusProps:l}=ce({autoFocus:r}),{isHovered:a,hoverProps:u}=fe({isDisabled:o}),{pressed:c,pressProps:d}=Se({disabled:o}),p=se({ref:n,type:(T=i.type)!=null?T:"button",disabled:o||void 0,autoFocus:r},l,u,d),f=(0,rs.useMemo)(()=>({disabled:o,hover:a,focus:s,active:c,autofocus:r}),[o,a,s,c,r]);return H()({ourProps:p,theirProps:i,slot:f,defaultTag:Du,name:"Button"})}var Nr=M(Iu);var ct=ie(require("react"),1);var an=require("react");function lt(e,n,t){let[o,r]=(0,an.useState)(t),i=e!==void 0,s=(0,an.useRef)(i),l=(0,an.useRef)(!1),a=(0,an.useRef)(!1);return i&&!s.current&&!l.current?(l.current=!0,s.current=i,console.error("A component is changing from uncontrolled to controlled. This may be caused by the value changing from undefined to a defined value, which should not happen.")):!i&&s.current&&!a.current&&(a.current=!0,s.current=i,console.error("A component is changing from controlled to uncontrolled. This may be caused by the value changing from a defined value to undefined, which should not happen.")),[i?e:o,E(u=>(i||r(u),n==null?void 0:n(u)))]}var is=require("react");function at(e){let[n]=(0,is.useState)(e);return n}var Q=require("react");var Ce=ie(require("react"),1),as=require("react-dom");function Br(e={},n=null,t=[]){for(let[o,r]of Object.entries(e))ls(t,ss(n,o),r);return t}function ss(e,n){return e?e+"["+n+"]":n}function ls(e,n,t){if(Array.isArray(t))for(let[o,r]of t.entries())ls(e,ss(n,o.toString()),r);else t instanceof Date?e.push([n,t.toISOString()]):typeof t=="boolean"?e.push([n,t?"1":"0"]):typeof t=="string"?e.push([n,t]):typeof t=="number"?e.push([n,`${t}`]):t==null?e.push([n,""]):Br(t,n,e)}function Mt(e){var t,o;let n=(t=e==null?void 0:e.form)!=null?t:e.closest("form");if(n){for(let r of n.elements)if(r!==e&&(r.tagName==="INPUT"&&r.type==="submit"||r.tagName==="BUTTON"&&r.type==="submit"||r.nodeName==="INPUT"&&r.type==="image")){r.click();return}(o=n.requestSubmit)==null||o.call(n)}}var Fu="span";function Mu(e,n){var s;let{features:t=1,...o}=e,r={ref:n,"aria-hidden":(t&2)===2?!0:(s=o["aria-hidden"])!=null?s:void 0,hidden:(t&4)===4?!0:void 0,style:{position:"fixed",top:1,left:1,width:1,height:0,padding:0,margin:-1,overflow:"hidden",clip:"rect(0, 0, 0, 0)",whiteSpace:"nowrap",borderWidth:"0",...(t&4)===4&&(t&2)!==2&&{display:"none"}}};return H()({ourProps:r,theirProps:o,slot:{},defaultTag:Fu,name:"Hidden"})}var Fe=M(Mu);var us=(0,Ce.createContext)(null);function cs(e){let[n,t]=(0,Ce.useState)(null);return Ce.default.createElement(us.Provider,{value:{target:n}},e.children,Ce.default.createElement(Fe,{features:4,ref:t}))}function wu({children:e}){let n=(0,Ce.useContext)(us);if(!n)return Ce.default.createElement(Ce.default.Fragment,null,e);let{target:t}=n;return t?(0,as.createPortal)(Ce.default.createElement(Ce.default.Fragment,null,e),t):null}function ut({data:e,form:n,disabled:t,onReset:o,overrides:r}){let[i,s]=(0,Ce.useState)(null),l=Re();return(0,Ce.useEffect)(()=>{if(o&&i)return l.addEventListener(i,"reset",o)},[i,n,o]),Ce.default.createElement(wu,null,Ce.default.createElement(_u,{setForm:s,formId:n}),Br(e).map(([a,u])=>Ce.default.createElement(Fe,{features:4,...st({key:a,as:"input",type:"hidden",hidden:!0,readOnly:!0,form:n,disabled:t,name:a,value:u,...r})})))}function _u({setForm:e,formId:n}){return(0,Ce.useEffect)(()=>{if(n){let t=document.getElementById(n);t&&e(t)}},[e,n]),n?null:Ce.default.createElement(Fe,{features:4,as:"input",type:"hidden",hidden:!0,readOnly:!0,ref:t=>{if(!t)return;let o=t.closest("form");o&&e(o)}})}var un=ie(require("react"),1),fs=(0,un.createContext)(void 0);function De(){return(0,un.useContext)(fs)}function ds({id:e,children:n}){return un.default.createElement(fs.Provider,{value:e},n)}function Ie(e){let n=e.parentElement,t=null;for(;n&&!(n instanceof HTMLFieldSetElement);)n instanceof HTMLLegendElement&&(t=n),n=n.parentElement;let o=(n==null?void 0:n.getAttribute("disabled"))==="";return o&&Hu(t)?!1:o}function Hu(e){if(!e)return!1;let n=e.previousElementSibling;for(;n!==null;){if(n instanceof HTMLLegendElement)return!1;n=n.previousElementSibling}return!0}var Ue=ie(require("react"),1);var Oo=require("react");var ps=Symbol();function cn(e,n=!0){return Object.assign(e,{[ps]:n})}function K(...e){let n=(0,Oo.useRef)(e);(0,Oo.useEffect)(()=>{n.current=e},[e]);let t=E(o=>{for(let r of n.current)r!=null&&(typeof r=="function"?r(o):r.current=o)});return e.every(o=>o==null||(o==null?void 0:o[ps]))?void 0:t}var Lo=(0,Ue.createContext)(null);Lo.displayName="DescriptionContext";function ms(){let e=(0,Ue.useContext)(Lo);if(e===null){let n=new Error("You used a <Description /> component, but it is not inside a relevant parent.");throw Error.captureStackTrace&&Error.captureStackTrace(n,ms),n}return e}function we(){var e,n;return(n=(e=(0,Ue.useContext)(Lo))==null?void 0:e.value)!=null?n:void 0}function tt(){let[e,n]=(0,Ue.useState)([]);return[e.length>0?e.join(" "):void 0,(0,Ue.useMemo)(()=>function(o){let r=E(s=>(n(l=>[...l,s]),()=>n(l=>{let a=l.slice(),u=a.indexOf(s);return u!==-1&&a.splice(u,1),a}))),i=(0,Ue.useMemo)(()=>({register:r,slot:o.slot,name:o.name,props:o.props,value:o.value}),[r,o.slot,o.name,o.props,o.value]);return Ue.default.createElement(Lo.Provider,{value:i},o.children)},[n])]}var ku="p";function $u(e,n){let t=(0,Q.useId)(),o=Te(),{id:r=`headlessui-description-${t}`,...i}=e,s=ms(),l=K(n);W(()=>s.register(r),[r,s.register]);let a=o||!1,u=(0,Ue.useMemo)(()=>({...s.slot,disabled:a}),[s.slot,a]),c={ref:l,...s.props,id:r};return H()({ourProps:c,theirProps:i,slot:u,defaultTag:ku,name:s.name||"Description"})}var Nu=M($u),xt=Object.assign(Nu,{});var Ge=ie(require("react"),1);var Do=(0,Ge.createContext)(null);Do.displayName="LabelContext";function Io(){let e=(0,Ge.useContext)(Do);if(e===null){let n=new Error("You used a <Label /> component, but it is not inside a relevant parent.");throw Error.captureStackTrace&&Error.captureStackTrace(n,Io),n}return e}function Le(e){var t,o,r;let n=(o=(t=(0,Ge.useContext)(Do))==null?void 0:t.value)!=null?o:void 0;return((r=e==null?void 0:e.length)!=null?r:0)>0?[n,...e].filter(Boolean).join(" "):n}function _e({inherit:e=!1}={}){let n=Le(),[t,o]=(0,Ge.useState)([]),r=e?[n,...t].filter(Boolean):t;return[r.length>0?r.join(" "):void 0,(0,Ge.useMemo)(()=>function(s){let l=E(u=>(o(c=>[...c,u]),()=>o(c=>{let d=c.slice(),p=d.indexOf(u);return p!==-1&&d.splice(p,1),d}))),a=(0,Ge.useMemo)(()=>({register:l,slot:s.slot,name:s.name,props:s.props,value:s.value}),[l,s.slot,s.name,s.props,s.value]);return Ge.default.createElement(Do.Provider,{value:a},s.children)},[o])]}var Bu="label";function Uu(e,n){var b;let t=(0,Q.useId)(),o=Io(),r=De(),i=Te(),{id:s=`headlessui-label-${t}`,htmlFor:l=r!=null?r:(b=o.props)==null?void 0:b.htmlFor,passive:a=!1,...u}=e,c=K(n);W(()=>o.register(s),[s,o.register]);let d=E(g=>{let h=g.currentTarget;if(h instanceof HTMLLabelElement&&g.preventDefault(),o.props&&"onClick"in o.props&&typeof o.props.onClick=="function"&&o.props.onClick(g),h instanceof HTMLLabelElement){let y=document.getElementById(h.htmlFor);if(y){let x=y.getAttribute("disabled");if(x==="true"||x==="")return;let v=y.getAttribute("aria-disabled");if(v==="true"||v==="")return;(y instanceof HTMLInputElement&&(y.type==="radio"||y.type==="checkbox")||y.role==="radio"||y.role==="checkbox"||y.role==="switch")&&y.click(),y.focus({preventScroll:!0})}}}),p=i||!1,f=(0,Ge.useMemo)(()=>({...o.slot,disabled:p}),[o.slot,p]),m={ref:c,...o.props,id:s,htmlFor:l,onClick:d};return a&&("onClick"in m&&(delete m.htmlFor,delete m.onClick),"onClick"in u&&delete u.onClick),H()({ourProps:m,theirProps:u,slot:f,defaultTag:l?Bu:"div",name:o.name||"Label"})}var Gu=M(Uu),Xe=Object.assign(Gu,{});var Wu="span";function Vu(e,n){let t=(0,Q.useId)(),o=De(),r=Te(),{id:i=o||`headlessui-checkbox-${t}`,disabled:s=r||!1,autoFocus:l=!1,checked:a,defaultChecked:u,onChange:c,name:d,value:p,form:f,indeterminate:m=!1,tabIndex:T=0,...b}=e,g=at(u),[h,y]=lt(a,c,g!=null?g:!1),x=Le(),v=we(),A=Re(),[D,F]=(0,ct.useState)(!1),L=E(()=>{F(!0),y==null||y(!h),A.nextFrame(()=>{F(!1)})}),S=E(k=>{if(Ie(k.currentTarget))return k.preventDefault();k.preventDefault(),L()}),O=E(k=>{k.key===" "?(k.preventDefault(),L()):k.key==="Enter"&&Mt(k.currentTarget)}),C=E(k=>k.preventDefault()),{isFocusVisible:N,focusProps:j}=ce({autoFocus:l}),{isHovered:_,hoverProps:$}=fe({isDisabled:s}),{pressed:G,pressProps:ne}=Se({disabled:s}),J=se({ref:n,id:i,role:"checkbox","aria-checked":m?"mixed":h?"true":"false","aria-labelledby":x,"aria-describedby":v,"aria-disabled":s?!0:void 0,indeterminate:m?"true":void 0,tabIndex:s?void 0:T,onKeyUp:s?void 0:O,onKeyPress:s?void 0:C,onClick:s?void 0:S},j,$,ne),R=(0,ct.useMemo)(()=>({checked:h,disabled:s,hover:_,focus:N,active:G,indeterminate:m,changing:D,autofocus:l}),[h,m,s,_,N,G,D,l]),P=(0,ct.useCallback)(()=>{if(g!==void 0)return y==null?void 0:y(g)},[y,g]),w=H();return ct.default.createElement(ct.default.Fragment,null,d!=null&&ct.default.createElement(ut,{disabled:s,data:{[d]:p||"on"},overrides:{type:"checkbox",checked:h},form:f,onReset:P}),w({ourProps:J,theirProps:b,slot:R,defaultTag:Wu,name:"Checkbox"}))}var ju=M(Vu);var bs=ie(require("react"),1);var fn=ie(require("react"),1),Ts=(0,fn.createContext)(()=>{});function Fo(){return(0,fn.useContext)(Ts)}function dn({value:e,children:n}){return fn.default.createElement(Ts.Provider,{value:e},n)}function Ku(e,n){let t=Fo();return bs.default.createElement(Nr,{ref:n,...se({onClick:t},e)})}var zu=M(Ku);var wt=ie(require("react"),1),Cs=require("react-dom");function Yt(e,n,t){var s;let o=(s=t.initialDeps)!=null?s:[],r;function i(){var l,a,u,c;let d;t.key&&((l=t.debug)!=null&&l.call(t))&&(d=Date.now());let p=e();if(!(p.length!==o.length||p.some((T,b)=>o[b]!==T)))return r;o=p;let m;if(t.key&&((a=t.debug)!=null&&a.call(t))&&(m=Date.now()),r=n(...p),t.key&&((u=t.debug)!=null&&u.call(t))){let T=Math.round((Date.now()-d)*100)/100,b=Math.round((Date.now()-m)*100)/100,g=b/16,h=(y,x)=>{for(y=String(y);y.length<x;)y=" "+y;return y};console.info(`%c\u23F1 ${h(b,5)} /${h(T,5)} ms`,`
            font-size: .6rem;
            font-weight: bold;
            color: hsl(${Math.max(0,Math.min(120-120*g,120))}deg 100% 31%);`,t==null?void 0:t.key)}return(c=t==null?void 0:t.onChange)==null||c.call(t,r),r}return i.updateDeps=l=>{o=l},i}function Ur(e,n){if(e===void 0)throw new Error(`Unexpected undefined${n?`: ${n}`:""}`);return e}var gs=(e,n)=>Math.abs(e-n)<1,ys=(e,n,t)=>{let o;return function(...r){e.clearTimeout(o),o=e.setTimeout(()=>n.apply(this,r),t)}};var Xu=e=>e,Yu=e=>{let n=Math.max(e.startIndex-e.overscan,0),t=Math.min(e.endIndex+e.overscan,e.count-1),o=[];for(let r=n;r<=t;r++)o.push(r);return o},Es=(e,n)=>{let t=e.scrollElement;if(!t)return;let o=e.targetWindow;if(!o)return;let r=s=>{let{width:l,height:a}=s;n({width:Math.round(l),height:Math.round(a)})};if(r(t.getBoundingClientRect()),!o.ResizeObserver)return()=>{};let i=new o.ResizeObserver(s=>{let l=()=>{let a=s[0];if(a!=null&&a.borderBoxSize){let u=a.borderBoxSize[0];if(u){r({width:u.inlineSize,height:u.blockSize});return}}r(t.getBoundingClientRect())};e.options.useAnimationFrameWithResizeObserver?requestAnimationFrame(l):l()});return i.observe(t,{box:"border-box"}),()=>{i.unobserve(t)}},hs={passive:!0};var vs=typeof window=="undefined"?!0:"onscrollend"in window,xs=(e,n)=>{let t=e.scrollElement;if(!t)return;let o=e.targetWindow;if(!o)return;let r=0,i=e.options.useScrollendEvent&&vs?()=>{}:ys(o,()=>{n(r,!1)},e.options.isScrollingResetDelay),s=c=>()=>{let{horizontal:d,isRtl:p}=e.options;r=d?t.scrollLeft*(p&&-1||1):t.scrollTop,i(),n(r,c)},l=s(!0),a=s(!1);a(),t.addEventListener("scroll",l,hs);let u=e.options.useScrollendEvent&&vs;return u&&t.addEventListener("scrollend",a,hs),()=>{t.removeEventListener("scroll",l),u&&t.removeEventListener("scrollend",a)}};var qu=(e,n,t)=>{if(n!=null&&n.borderBoxSize){let o=n.borderBoxSize[0];if(o)return Math.round(o[t.options.horizontal?"inlineSize":"blockSize"])}return Math.round(e.getBoundingClientRect()[t.options.horizontal?"width":"height"])};var Ps=(e,{adjustments:n=0,behavior:t},o)=>{var r,i;let s=e+n;(i=(r=o.scrollElement)==null?void 0:r.scrollTo)==null||i.call(r,{[o.options.horizontal?"left":"top"]:s,behavior:t})},Mo=class{constructor(n){this.unsubs=[],this.scrollElement=null,this.targetWindow=null,this.isScrolling=!1,this.scrollToIndexTimeoutId=null,this.measurementsCache=[],this.itemSizeCache=new Map,this.pendingMeasuredCacheIndexes=[],this.scrollRect=null,this.scrollOffset=null,this.scrollDirection=null,this.scrollAdjustments=0,this.elementsCache=new Map,this.observer=(()=>{let t=null,o=()=>t||(!this.targetWindow||!this.targetWindow.ResizeObserver?null:t=new this.targetWindow.ResizeObserver(r=>{r.forEach(i=>{let s=()=>{this._measureElement(i.target,i)};this.options.useAnimationFrameWithResizeObserver?requestAnimationFrame(s):s()})}));return{disconnect:()=>{var r;(r=o())==null||r.disconnect(),t=null},observe:r=>{var i;return(i=o())==null?void 0:i.observe(r,{box:"border-box"})},unobserve:r=>{var i;return(i=o())==null?void 0:i.unobserve(r)}}})(),this.range=null,this.setOptions=t=>{Object.entries(t).forEach(([o,r])=>{typeof r=="undefined"&&delete t[o]}),this.options={debug:!1,initialOffset:0,overscan:1,paddingStart:0,paddingEnd:0,scrollPaddingStart:0,scrollPaddingEnd:0,horizontal:!1,getItemKey:Xu,rangeExtractor:Yu,onChange:()=>{},measureElement:qu,initialRect:{width:0,height:0},scrollMargin:0,gap:0,indexAttribute:"data-index",initialMeasurementsCache:[],lanes:1,isScrollingResetDelay:150,enabled:!0,isRtl:!1,useScrollendEvent:!1,useAnimationFrameWithResizeObserver:!1,...t}},this.notify=t=>{var o,r;(r=(o=this.options).onChange)==null||r.call(o,this,t)},this.maybeNotify=Yt(()=>(this.calculateRange(),[this.isScrolling,this.range?this.range.startIndex:null,this.range?this.range.endIndex:null]),t=>{this.notify(t)},{key:!1,debug:()=>this.options.debug,initialDeps:[this.isScrolling,this.range?this.range.startIndex:null,this.range?this.range.endIndex:null]}),this.cleanup=()=>{this.unsubs.filter(Boolean).forEach(t=>t()),this.unsubs=[],this.observer.disconnect(),this.scrollElement=null,this.targetWindow=null},this._didMount=()=>()=>{this.cleanup()},this._willUpdate=()=>{var r;var t;let o=this.options.enabled?this.options.getScrollElement():null;if(this.scrollElement!==o){if(this.cleanup(),!o){this.maybeNotify();return}this.scrollElement=o,this.scrollElement&&"ownerDocument"in this.scrollElement?this.targetWindow=this.scrollElement.ownerDocument.defaultView:this.targetWindow=(r=(t=this.scrollElement)==null?void 0:t.window)!=null?r:null,this.elementsCache.forEach(i=>{this.observer.observe(i)}),this._scrollToOffset(this.getScrollOffset(),{adjustments:void 0,behavior:void 0}),this.unsubs.push(this.options.observeElementRect(this,i=>{this.scrollRect=i,this.maybeNotify()})),this.unsubs.push(this.options.observeElementOffset(this,(i,s)=>{this.scrollAdjustments=0,this.scrollDirection=s?this.getScrollOffset()<i?"forward":"backward":null,this.scrollOffset=i,this.isScrolling=s,this.maybeNotify()}))}},this.getSize=()=>{var t;return this.options.enabled?(this.scrollRect=(t=this.scrollRect)!=null?t:this.options.initialRect,this.scrollRect[this.options.horizontal?"width":"height"]):(this.scrollRect=null,0)},this.getScrollOffset=()=>{var t;return this.options.enabled?(this.scrollOffset=(t=this.scrollOffset)!=null?t:typeof this.options.initialOffset=="function"?this.options.initialOffset():this.options.initialOffset,this.scrollOffset):(this.scrollOffset=null,0)},this.getFurthestMeasurement=(t,o)=>{let r=new Map,i=new Map;for(let s=o-1;s>=0;s--){let l=t[s];if(r.has(l.lane))continue;let a=i.get(l.lane);if(a==null||l.end>a.end?i.set(l.lane,l):l.end<a.end&&r.set(l.lane,!0),r.size===this.options.lanes)break}return i.size===this.options.lanes?Array.from(i.values()).sort((s,l)=>s.end===l.end?s.index-l.index:s.end-l.end)[0]:void 0},this.getMeasurementOptions=Yt(()=>[this.options.count,this.options.paddingStart,this.options.scrollMargin,this.options.getItemKey,this.options.enabled],(t,o,r,i,s)=>(this.pendingMeasuredCacheIndexes=[],{count:t,paddingStart:o,scrollMargin:r,getItemKey:i,enabled:s}),{key:!1}),this.getMeasurements=Yt(()=>[this.getMeasurementOptions(),this.itemSizeCache],({count:t,paddingStart:o,scrollMargin:r,getItemKey:i,enabled:s},l)=>{if(!s)return this.measurementsCache=[],this.itemSizeCache.clear(),[];this.measurementsCache.length===0&&(this.measurementsCache=this.options.initialMeasurementsCache,this.measurementsCache.forEach(c=>{this.itemSizeCache.set(c.key,c.size)}));let a=this.pendingMeasuredCacheIndexes.length>0?Math.min(...this.pendingMeasuredCacheIndexes):0;this.pendingMeasuredCacheIndexes=[];let u=this.measurementsCache.slice(0,a);for(let c=a;c<t;c++){let d=i(c),p=this.options.lanes===1?u[c-1]:this.getFurthestMeasurement(u,c),f=p?p.end+this.options.gap:o+r,m=l.get(d),T=typeof m=="number"?m:this.options.estimateSize(c),b=f+T,g=p?p.lane:c%this.options.lanes;u[c]={index:c,start:f,size:T,end:b,key:d,lane:g}}return this.measurementsCache=u,u},{key:!1,debug:()=>this.options.debug}),this.calculateRange=Yt(()=>[this.getMeasurements(),this.getSize(),this.getScrollOffset(),this.options.lanes],(t,o,r,i)=>this.range=t.length>0&&o>0?Ju({measurements:t,outerSize:o,scrollOffset:r,lanes:i}):null,{key:!1,debug:()=>this.options.debug}),this.getVirtualIndexes=Yt(()=>{let t=null,o=null,r=this.calculateRange();return r&&(t=r.startIndex,o=r.endIndex),this.maybeNotify.updateDeps([this.isScrolling,t,o]),[this.options.rangeExtractor,this.options.overscan,this.options.count,t,o]},(t,o,r,i,s)=>i===null||s===null?[]:t({startIndex:i,endIndex:s,overscan:o,count:r}),{key:!1,debug:()=>this.options.debug}),this.indexFromElement=t=>{let o=this.options.indexAttribute,r=t.getAttribute(o);return r?parseInt(r,10):(console.warn(`Missing attribute name '${o}={index}' on measured element.`),-1)},this._measureElement=(t,o)=>{let r=this.indexFromElement(t),i=this.measurementsCache[r];if(!i)return;let s=i.key,l=this.elementsCache.get(s);l!==t&&(l&&this.observer.unobserve(l),this.observer.observe(t),this.elementsCache.set(s,t)),t.isConnected&&this.resizeItem(r,this.options.measureElement(t,o,this))},this.resizeItem=(t,o)=>{var l;let r=this.measurementsCache[t];if(!r)return;let i=(l=this.itemSizeCache.get(r.key))!=null?l:r.size,s=o-i;s!==0&&((this.shouldAdjustScrollPositionOnItemSizeChange!==void 0?this.shouldAdjustScrollPositionOnItemSizeChange(r,s,this):r.start<this.getScrollOffset()+this.scrollAdjustments)&&this._scrollToOffset(this.getScrollOffset(),{adjustments:this.scrollAdjustments+=s,behavior:void 0}),this.pendingMeasuredCacheIndexes.push(r.index),this.itemSizeCache=new Map(this.itemSizeCache.set(r.key,o)),this.notify(!1))},this.measureElement=t=>{if(!t){this.elementsCache.forEach((o,r)=>{o.isConnected||(this.observer.unobserve(o),this.elementsCache.delete(r))});return}this._measureElement(t,void 0)},this.getVirtualItems=Yt(()=>[this.getVirtualIndexes(),this.getMeasurements()],(t,o)=>{let r=[];for(let i=0,s=t.length;i<s;i++){let l=t[i],a=o[l];r.push(a)}return r},{key:!1,debug:()=>this.options.debug}),this.getVirtualItemForOffset=t=>{let o=this.getMeasurements();if(o.length!==0)return Ur(o[Rs(0,o.length-1,r=>Ur(o[r]).start,t)])},this.getOffsetForAlignment=(t,o,r=0)=>{let i=this.getSize(),s=this.getScrollOffset();o==="auto"&&(o=t>=s+i?"end":"start"),o==="center"?t+=(r-i)/2:o==="end"&&(t-=i);let l=this.options.horizontal?"scrollWidth":"scrollHeight",u=(this.scrollElement?"document"in this.scrollElement?this.scrollElement.document.documentElement[l]:this.scrollElement[l]:0)-i;return Math.max(Math.min(u,t),0)},this.getOffsetForIndex=(t,o="auto")=>{t=Math.max(0,Math.min(t,this.options.count-1));let r=this.measurementsCache[t];if(!r)return;let i=this.getSize(),s=this.getScrollOffset();if(o==="auto")if(r.end>=s+i-this.options.scrollPaddingEnd)o="end";else if(r.start<=s+this.options.scrollPaddingStart)o="start";else return[s,o];let l=o==="end"?r.end+this.options.scrollPaddingEnd:r.start-this.options.scrollPaddingStart;return[this.getOffsetForAlignment(l,o,r.size),o]},this.isDynamicMode=()=>this.elementsCache.size>0,this.cancelScrollToIndex=()=>{this.scrollToIndexTimeoutId!==null&&this.targetWindow&&(this.targetWindow.clearTimeout(this.scrollToIndexTimeoutId),this.scrollToIndexTimeoutId=null)},this.scrollToOffset=(t,{align:o="start",behavior:r}={})=>{this.cancelScrollToIndex(),r==="smooth"&&this.isDynamicMode()&&console.warn("The `smooth` scroll behavior is not fully supported with dynamic size."),this._scrollToOffset(this.getOffsetForAlignment(t,o),{adjustments:void 0,behavior:r})},this.scrollToIndex=(t,{align:o="auto",behavior:r}={})=>{t=Math.max(0,Math.min(t,this.options.count-1)),this.cancelScrollToIndex(),r==="smooth"&&this.isDynamicMode()&&console.warn("The `smooth` scroll behavior is not fully supported with dynamic size.");let i=this.getOffsetForIndex(t,o);if(!i)return;let[s,l]=i;this._scrollToOffset(s,{adjustments:void 0,behavior:r}),r!=="smooth"&&this.isDynamicMode()&&this.targetWindow&&(this.scrollToIndexTimeoutId=this.targetWindow.setTimeout(()=>{if(this.scrollToIndexTimeoutId=null,this.elementsCache.has(this.options.getItemKey(t))){let u=this.getOffsetForIndex(t,l);if(!u)return;let[c]=u;gs(c,this.getScrollOffset())||this.scrollToIndex(t,{align:l,behavior:r})}else this.scrollToIndex(t,{align:l,behavior:r})}))},this.scrollBy=(t,{behavior:o}={})=>{this.cancelScrollToIndex(),o==="smooth"&&this.isDynamicMode()&&console.warn("The `smooth` scroll behavior is not fully supported with dynamic size."),this._scrollToOffset(this.getScrollOffset()+t,{adjustments:void 0,behavior:o})},this.getTotalSize=()=>{var i;var t;let o=this.getMeasurements(),r;if(o.length===0)r=this.options.paddingStart;else if(this.options.lanes===1)r=(i=(t=o[o.length-1])==null?void 0:t.end)!=null?i:0;else{let s=Array(this.options.lanes).fill(null),l=o.length-1;for(;l>=0&&s.some(a=>a===null);){let a=o[l];s[a.lane]===null&&(s[a.lane]=a.end),l--}r=Math.max(...s.filter(a=>a!==null))}return Math.max(r-this.options.scrollMargin+this.options.paddingEnd,0)},this._scrollToOffset=(t,{adjustments:o,behavior:r})=>{this.options.scrollToFn(t,{behavior:r,adjustments:o},this)},this.measure=()=>{this.itemSizeCache=new Map,this.notify(!1)},this.setOptions(n)}},Rs=(e,n,t,o)=>{for(;e<=n;){let r=(e+n)/2|0,i=t(r);if(i<o)e=r+1;else if(i>o)n=r-1;else return r}return e>0?e-1:0};function Ju({measurements:e,outerSize:n,scrollOffset:t,lanes:o}){let r=e.length-1,i=a=>e[a].start;if(e.length<=o)return{startIndex:0,endIndex:r};let s=Rs(0,r,i,t),l=s;if(o===1)for(;l<r&&e[l].end<t+n;)l++;else if(o>1){let a=Array(o).fill(0);for(;l<r&&a.some(c=>c<t+n);){let c=e[l];a[c.lane]=c.end,l++}let u=Array(o).fill(t+n);for(;s>=0&&u.some(c=>c>=t);){let c=e[s];u[c.lane]=c.start,s--}s=Math.max(0,s-s%o),l=Math.min(r,l+(o-1-l%o))}return{startIndex:s,endIndex:l}}var Ss=typeof document!="undefined"?wt.useLayoutEffect:wt.useEffect;function Qu(e){let n=wt.useReducer(()=>({}),{})[1],t={...e,onChange:(r,i)=>{var s;i?(0,Cs.flushSync)(n):n(),(s=e.onChange)==null||s.call(e,r,i)}},[o]=wt.useState(()=>new Mo(t));return o.setOptions(t),Ss(()=>o._didMount(),[]),Ss(()=>o._willUpdate()),o}function As(e){return Qu({observeElementRect:Es,observeElementOffset:xs,scrollToFn:Ps,...e})}var q=ie(require("react"),1),on=require("react-dom");var Os=require("react");function Zu(e,n){return e!==null&&n!==null&&typeof e=="object"&&typeof n=="object"&&"id"in e&&"id"in n?e.id===n.id:e===n}function pn(e=Zu){return(0,Os.useCallback)((n,t)=>{if(typeof e=="string"){let o=e;return(n==null?void 0:n[o])===(t==null?void 0:t[o])}return e(n,t)},[e])}var wo=require("react");function ec(e){if(e===null)return{width:0,height:0};let{width:n,height:t}=e.getBoundingClientRect();return{width:n,height:t}}function Pt(e,n=!1){let[t,o]=(0,wo.useReducer)(()=>({}),{}),r=(0,wo.useMemo)(()=>ec(e),[e,t]);return W(()=>{if(!e)return;let i=new ResizeObserver(o);return i.observe(e),()=>{i.disconnect()}},[e]),n?{width:`${r.width}px`,height:`${r.height}px`}:r}var Ds=require("react");var mn=class extends Map{constructor(t){super();this.factory=t}get(t){let o=super.get(t);return o===void 0&&(o=this.factory(t),this.set(t,o)),o}};function _o(e,n){let t=e(),o=new Set;return{getSnapshot(){return t},subscribe(r){return o.add(r),()=>o.delete(r)},dispatch(r,...i){let s=n[r].call(t,...i);s&&(t=s,o.forEach(l=>l()))}}}var Ls=require("react");function Ho(e){return(0,Ls.useSyncExternalStore)(e.subscribe,e.getSnapshot,e.getSnapshot)}var tc=new mn(()=>_o(()=>[],{ADD(e){return this.includes(e)?this:[...this,e]},REMOVE(e){let n=this.indexOf(e);if(n===-1)return this;let t=this.slice();return t.splice(n,1),t}}));function nt(e,n){let t=tc.get(n),o=(0,Ds.useId)(),r=Ho(t);if(W(()=>{if(e)return t.dispatch("ADD",o),()=>t.dispatch("REMOVE",o)},[t,e]),!e)return!1;let i=r.indexOf(o),s=r.length;return i===-1&&(i=s,s+=1),i===s-1}var Gr=new Map,zn=new Map;function Is(e){var t;let n=(t=zn.get(e))!=null?t:0;return zn.set(e,n+1),n!==0?()=>Fs(e):(Gr.set(e,{"aria-hidden":e.getAttribute("aria-hidden"),inert:e.inert}),e.setAttribute("aria-hidden","true"),e.inert=!0,()=>Fs(e))}function Fs(e){var o;let n=(o=zn.get(e))!=null?o:1;if(n===1?zn.delete(e):zn.set(e,n-1),n!==1)return;let t=Gr.get(e);t&&(t["aria-hidden"]===null?e.removeAttribute("aria-hidden"):e.setAttribute("aria-hidden",t["aria-hidden"]),e.inert=t.inert,Gr.delete(e))}function _t(e,{allowed:n,disallowed:t}={}){let o=nt(e,"inert-others");W(()=>{var s,l;if(!o)return;let r=he();for(let a of(s=t==null?void 0:t())!=null?s:[])a&&r.add(Is(a));let i=(l=n==null?void 0:n())!=null?l:[];for(let a of i){if(!a)continue;let u=xe(a);if(!u)continue;let c=a.parentElement;for(;c&&c!==u.body;){for(let d of c.children)i.some(p=>d.contains(p))||r.add(Is(d));c=c.parentElement}}return r.dispose},[o,n,t])}var Ms=require("react");function ft(e,n,t){let o=pe(r=>{let i=r.getBoundingClientRect();i.x===0&&i.y===0&&i.width===0&&i.height===0&&t()});(0,Ms.useEffect)(()=>{if(!e)return;let r=n===null?null:n instanceof HTMLElement?n:n.current;if(!r)return;let i=he();if(typeof ResizeObserver!="undefined"){let s=new ResizeObserver(()=>o.current(r));s.observe(r),i.add(()=>s.disconnect())}if(typeof IntersectionObserver!="undefined"){let s=new IntersectionObserver(()=>o.current(r));s.observe(r),i.add(()=>s.disconnect())}return()=>i.dispose()},[n,o,e])}var Yn=require("react");var Xn=["[contentEditable=true]","[tabindex]","a[href]","area[href]","button:not([disabled])","iframe","input:not([disabled])","select:not([disabled])","textarea:not([disabled])"].map(e=>`${e}:not([tabindex='-1'])`).join(","),nc=["[data-autofocus]"].map(e=>`${e}:not([tabindex='-1'])`).join(",");function Tn(e=document.body){return e==null?[]:Array.from(e.querySelectorAll(Xn)).sort((n,t)=>Math.sign((n.tabIndex||Number.MAX_SAFE_INTEGER)-(t.tabIndex||Number.MAX_SAFE_INTEGER)))}function oc(e=document.body){return e==null?[]:Array.from(e.querySelectorAll(nc)).sort((n,t)=>Math.sign((n.tabIndex||Number.MAX_SAFE_INTEGER)-(t.tabIndex||Number.MAX_SAFE_INTEGER)))}function Rt(e,n=0){var t;return e===((t=xe(e))==null?void 0:t.body)?!1:Y(n,{[0](){return e.matches(Xn)},[1](){let o=e;for(;o!==null;){if(o.matches(Xn))return!0;o=o.parentElement}return!1}})}function Wr(e){let n=xe(e);he().nextFrame(()=>{n&&!Rt(n.activeElement,0)&&ot(e)})}typeof window!="undefined"&&typeof document!="undefined"&&(document.addEventListener("keydown",e=>{e.metaKey||e.altKey||e.ctrlKey||(document.documentElement.dataset.headlessuiFocusVisible="")},!0),document.addEventListener("click",e=>{e.detail===1?delete document.documentElement.dataset.headlessuiFocusVisible:e.detail===0&&(document.documentElement.dataset.headlessuiFocusVisible="")},!0));function ot(e){e==null||e.focus({preventScroll:!0})}var rc=["textarea","input"].join(",");function ic(e){var n,t;return(t=(n=e==null?void 0:e.matches)==null?void 0:n.call(e,rc))!=null?t:!1}function He(e,n=t=>t){return e.slice().sort((t,o)=>{let r=n(t),i=n(o);if(r===null||i===null)return 0;let s=r.compareDocumentPosition(i);return s&Node.DOCUMENT_POSITION_FOLLOWING?-1:s&Node.DOCUMENT_POSITION_PRECEDING?1:0})}function ko(e,n){return ye(Tn(),n,{relativeTo:e})}function ye(e,n,{sorted:t=!0,relativeTo:o=null,skipElements:r=[]}={}){let i=Array.isArray(e)?e.length>0?e[0].ownerDocument:document:e.ownerDocument,s=Array.isArray(e)?t?He(e):e:n&64?oc(e):Tn(e);r.length>0&&s.length>1&&(s=s.filter(f=>!r.some(m=>m!=null&&"current"in m?(m==null?void 0:m.current)===f:m===f))),o=o!=null?o:i.activeElement;let l=(()=>{if(n&5)return 1;if(n&10)return-1;throw new Error("Missing Focus.First, Focus.Previous, Focus.Next or Focus.Last")})(),a=(()=>{if(n&1)return 0;if(n&2)return Math.max(0,s.indexOf(o))-1;if(n&4)return Math.max(0,s.indexOf(o))+1;if(n&8)return s.length-1;throw new Error("Missing Focus.First, Focus.Previous, Focus.Next or Focus.Last")})(),u=n&32?{preventScroll:!0}:{},c=0,d=s.length,p;do{if(c>=d||c+d<=0)return 0;let f=a+c;if(n&16)f=(f+d)%d;else{if(f<0)return 3;if(f>=d)return 1}p=s[f],p==null||p.focus(u),c+=l}while(p!==i.activeElement);return n&6&&ic(p)&&p.select(),2}function Vr(){return/iPhone/gi.test(window.navigator.platform)||/Mac/gi.test(window.navigator.platform)&&window.navigator.maxTouchPoints>0}function sc(){return/Android/gi.test(window.navigator.userAgent)}function $o(){return Vr()||sc()}var ws=require("react");function bn(e,n,t,o){let r=pe(t);(0,ws.useEffect)(()=>{if(!e)return;function i(s){r.current(s)}return document.addEventListener(n,i,o),()=>document.removeEventListener(n,i,o)},[e,n,o])}var _s=require("react");function No(e,n,t,o){let r=pe(t);(0,_s.useEffect)(()=>{if(!e)return;function i(s){r.current(s)}return window.addEventListener(n,i,o),()=>window.removeEventListener(n,i,o)},[e,n,o])}var Hs=30;function dt(e,n,t){let o=nt(e,"outside-click"),r=pe(t),i=(0,Yn.useCallback)(function(u,c){if(u.defaultPrevented)return;let d=c(u);if(d===null||!d.getRootNode().contains(d)||!d.isConnected)return;let p=function f(m){return typeof m=="function"?f(m()):Array.isArray(m)||m instanceof Set?m:[m]}(n);for(let f of p)if(f!==null&&(f.contains(d)||u.composed&&u.composedPath().includes(f)))return;return!Rt(d,1)&&d.tabIndex!==-1&&u.preventDefault(),r.current(u,d)},[r,n]),s=(0,Yn.useRef)(null);bn(o,"pointerdown",a=>{var u,c;s.current=((c=(u=a.composedPath)==null?void 0:u.call(a))==null?void 0:c[0])||a.target},!0),bn(o,"mousedown",a=>{var u,c;s.current=((c=(u=a.composedPath)==null?void 0:u.call(a))==null?void 0:c[0])||a.target},!0),bn(o,"click",a=>{$o()||s.current&&(i(a,()=>s.current),s.current=null)},!0);let l=(0,Yn.useRef)({x:0,y:0});bn(o,"touchstart",a=>{l.current.x=a.touches[0].clientX,l.current.y=a.touches[0].clientY},!0),bn(o,"touchend",a=>{let u={x:a.changedTouches[0].clientX,y:a.changedTouches[0].clientY};if(!(Math.abs(u.x-l.current.x)>=Hs||Math.abs(u.y-l.current.y)>=Hs))return i(a,()=>a.target instanceof HTMLElement?a.target:null)},!0),No(o,"blur",a=>i(a,()=>window.document.activeElement instanceof HTMLIFrameElement?window.document.activeElement:null),!0)}var ks=require("react");function Pe(...e){return(0,ks.useMemo)(()=>xe(...e),[...e])}var Ns=require("react");var $s=require("react");function Ht(e,n,t,o){let r=pe(t);(0,$s.useEffect)(()=>{e=e!=null?e:window;function i(s){r.current(s)}return e.addEventListener(n,i,o),()=>e.removeEventListener(n,i,o)},[e,n,o])}function Kr(e){let n=(0,Ns.useRef)({value:"",selectionStart:null,selectionEnd:null});return Ht(e,"blur",t=>{let o=t.target;o instanceof HTMLInputElement&&(n.current={value:o.value,selectionStart:o.selectionStart,selectionEnd:o.selectionEnd})}),E(()=>{if(document.activeElement!==e&&e instanceof HTMLInputElement&&e.isConnected){if(e.focus({preventScroll:!0}),e.value!==n.current.value)e.setSelectionRange(e.value.length,e.value.length);else{let{selectionStart:t,selectionEnd:o}=n.current;t!==null&&o!==null&&e.setSelectionRange(t,o)}n.current={value:"",selectionStart:null,selectionEnd:null}}})}var Bs=require("react");function $e(e,n){return(0,Bs.useMemo)(()=>{var o;if(e.type)return e.type;let t=(o=e.as)!=null?o:"button";if(typeof t=="string"&&t.toLowerCase()==="button"||(n==null?void 0:n.tagName)==="BUTTON"&&!n.hasAttribute("type"))return"button"},[e.type,e.as,n])}function Us(){let e;return{before({doc:n}){var r;let t=n.documentElement,o=(r=n.defaultView)!=null?r:window;e=Math.max(0,o.innerWidth-t.clientWidth)},after({doc:n,d:t}){let o=n.documentElement,r=Math.max(0,o.clientWidth-o.offsetWidth),i=Math.max(0,e-r);t.style(o,"paddingRight",`${i}px`)}}}function Gs(){return Vr()?{before({doc:e,d:n,meta:t}){function o(r){return t.containers.flatMap(i=>i()).some(i=>i.contains(r))}n.microTask(()=>{var s;if(window.getComputedStyle(e.documentElement).scrollBehavior!=="auto"){let l=he();l.style(e.documentElement,"scrollBehavior","auto"),n.add(()=>n.microTask(()=>l.dispose()))}let r=(s=window.scrollY)!=null?s:window.pageYOffset,i=null;n.addEventListener(e,"click",l=>{if(l.target instanceof HTMLElement)try{let a=l.target.closest("a");if(!a)return;let{hash:u}=new URL(a.href),c=e.querySelector(u);c&&!o(c)&&(i=c)}catch{}},!0),n.addEventListener(e,"touchstart",l=>{if(l.target instanceof HTMLElement)if(o(l.target)){let a=l.target;for(;a.parentElement&&o(a.parentElement);)a=a.parentElement;n.style(a,"overscrollBehavior","contain")}else n.style(l.target,"touchAction","none")}),n.addEventListener(e,"touchmove",l=>{if(l.target instanceof HTMLElement){if(l.target.tagName==="INPUT")return;if(o(l.target)){let a=l.target;for(;a.parentElement&&a.dataset.headlessuiPortal!==""&&!(a.scrollHeight>a.clientHeight||a.scrollWidth>a.clientWidth);)a=a.parentElement;a.dataset.headlessuiPortal===""&&l.preventDefault()}else l.preventDefault()}},{passive:!1}),n.add(()=>{var a;let l=(a=window.scrollY)!=null?a:window.pageYOffset;r!==l&&window.scrollTo(0,r),i&&i.isConnected&&(i.scrollIntoView({block:"nearest"}),i=null)})})}}:{}}function Ws(){return{before({doc:e,d:n}){n.style(e.documentElement,"overflow","hidden")}}}function lc(e){let n={};for(let t of e)Object.assign(n,t(n));return n}var kt=_o(()=>new Map,{PUSH(e,n){var o;let t=(o=this.get(e))!=null?o:{doc:e,count:0,d:he(),meta:new Set};return t.count++,t.meta.add(n),this.set(e,t),this},POP(e,n){let t=this.get(e);return t&&(t.count--,t.meta.delete(n)),this},SCROLL_PREVENT({doc:e,d:n,meta:t}){let o={doc:e,d:n,meta:lc(t)},r=[Gs(),Us(),Ws()];r.forEach(({before:i})=>i==null?void 0:i(o)),r.forEach(({after:i})=>i==null?void 0:i(o))},SCROLL_ALLOW({d:e}){e.dispose()},TEARDOWN({doc:e}){this.delete(e)}});kt.subscribe(()=>{let e=kt.getSnapshot(),n=new Map;for(let[t]of e)n.set(t,t.documentElement.style.overflow);for(let t of e.values()){let o=n.get(t.doc)==="hidden",r=t.count!==0;(r&&!o||!r&&o)&&kt.dispatch(t.count>0?"SCROLL_PREVENT":"SCROLL_ALLOW",t),t.count===0&&kt.dispatch("TEARDOWN",t)}});function Vs(e,n,t=()=>({containers:[]})){let o=Ho(kt),r=n?o.get(n):void 0,i=r?r.count>0:!1;return W(()=>{if(!(!n||!e))return kt.dispatch("PUSH",n,t),()=>kt.dispatch("POP",n,t)},[e,n]),i}function pt(e,n,t=()=>[document.body]){let o=nt(e,"scroll-lock");Vs(o,n,r=>{var i;return{containers:[...(i=r.containers)!=null?i:[],t]}})}var Ks=require("react");function js(e){return[e.screenX,e.screenY]}function gn(){let e=(0,Ks.useRef)([-1,-1]);return{wasMoved(n){let t=js(n);return e.current[0]===t[0]&&e.current[1]===t[1]?!1:(e.current=t,!0)},update(n){e.current=js(n)}}}var qn=require("react");var $t=require("react");function zs(e=0){let[n,t]=(0,$t.useState)(e),o=(0,$t.useCallback)(a=>t(a),[n]),r=(0,$t.useCallback)(a=>t(u=>u|a),[n]),i=(0,$t.useCallback)(a=>(n&a)===a,[n]),s=(0,$t.useCallback)(a=>t(u=>u&~a),[t]),l=(0,$t.useCallback)(a=>t(u=>u^a),[t]);return{flags:n,setFlag:o,addFlag:r,hasFlag:i,removeFlag:s,toggleFlag:l}}var Xs,Ys;typeof process!="undefined"&&typeof globalThis!="undefined"&&typeof Element!="undefined"&&((Xs=process==null?void 0:process.env)==null?void 0:Xs["NODE_ENV"])==="test"&&typeof((Ys=Element==null?void 0:Element.prototype)==null?void 0:Ys.getAnimations)=="undefined"&&(Element.prototype.getAnimations=function(){return console.warn(["Headless UI has polyfilled `Element.prototype.getAnimations` for your tests.","Please install a proper polyfill e.g. `jsdom-testing-mocks`, to silence these warnings.","","Example usage:","```js","import { mockAnimationsApi } from 'jsdom-testing-mocks'","mockAnimationsApi()","```"].join(`
`)),[]});function We(e){let n={};for(let t in e)e[t]===!0&&(n[`data-${t}`]="");return n}function Ve(e,n,t,o){let[r,i]=(0,qn.useState)(t),{hasFlag:s,addFlag:l,removeFlag:a}=zs(e&&r?3:0),u=(0,qn.useRef)(!1),c=(0,qn.useRef)(!1),d=Re();return W(()=>{var p;if(e){if(t&&i(!0),!n){t&&l(3);return}return(p=o==null?void 0:o.start)==null||p.call(o,t),ac(n,{inFlight:u,prepare(){c.current?c.current=!1:c.current=u.current,u.current=!0,!c.current&&(t?(l(3),a(4)):(l(4),a(2)))},run(){c.current?t?(a(3),l(4)):(a(4),l(3)):t?a(1):l(1)},done(){var f;c.current&&typeof n.getAnimations=="function"&&n.getAnimations().length>0||(u.current=!1,a(7),t||i(!1),(f=o==null?void 0:o.end)==null||f.call(o,t))}})}},[e,t,n,d]),e?[r,{closed:s(1),enter:s(2),leave:s(4),transition:s(2)||s(4)}]:[t,{closed:void 0,enter:void 0,leave:void 0,transition:void 0}]}function ac(e,{prepare:n,run:t,done:o,inFlight:r}){let i=he();return cc(e,{prepare:n,inFlight:r}),i.nextFrame(()=>{t(),i.requestAnimationFrame(()=>{i.add(uc(e,o))})}),i.dispose}function uc(e,n){var i,s;let t=he();if(!e)return t.dispose;let o=!1;t.add(()=>{o=!0});let r=(s=(i=e.getAnimations)==null?void 0:i.call(e).filter(l=>l instanceof CSSTransition))!=null?s:[];return r.length===0?(n(),t.dispose):(Promise.allSettled(r.map(l=>l.finished)).then(()=>{o||n()}),t.dispose)}function cc(e,{inFlight:n,prepare:t}){if(n!=null&&n.current){t();return}let o=e.style.transition;e.style.transition="none",t(),e.offsetHeight,e.style.transition=o}var Jn=require("react");function Bo(e,{container:n,accept:t,walk:o}){let r=(0,Jn.useRef)(t),i=(0,Jn.useRef)(o);(0,Jn.useEffect)(()=>{r.current=t,i.current=o},[t,o]),W(()=>{if(!n||!e)return;let s=xe(n);if(!s)return;let l=r.current,a=i.current,u=Object.assign(d=>l(d),{acceptNode:l}),c=s.createTreeWalker(n,NodeFilter.SHOW_ELEMENT,u,!1);for(;c.nextNode();)a(c.currentNode)},[n,e,r,i])}var Uo=require("react");function qt(e,n){let t=(0,Uo.useRef)([]),o=E(e);(0,Uo.useEffect)(()=>{let r=[...t.current];for(let[i,s]of n.entries())if(t.current[i]!==s){let l=o(n,r);return t.current=n,l}},[o,...n])}var Z=ie(require("react"),1),io=require("react");function qs(e){var n;return(e==null||(n=e.ownerDocument)==null?void 0:n.defaultView)||window}function Qn(e){return e instanceof Element||e instanceof qs(e).Element}function Js(){let e=navigator.userAgentData;return e&&Array.isArray(e.brands)?e.brands.map(n=>{let{brand:t,version:o}=n;return t+"/"+o}).join(" "):navigator.userAgent}var Nt=Math.min,ke=Math.max,eo=Math.round,to=Math.floor,St=e=>({x:e,y:e}),fc={left:"right",right:"left",bottom:"top",top:"bottom"},dc={start:"end",end:"start"};function zr(e,n,t){return ke(e,Nt(n,t))}function yn(e,n){return typeof e=="function"?e(n):e}function Ct(e){return e.split("-")[0]}function hn(e){return e.split("-")[1]}function Xr(e){return e==="x"?"y":"x"}function Yr(e){return e==="y"?"height":"width"}function vn(e){return["top","bottom"].includes(Ct(e))?"y":"x"}function qr(e){return Xr(vn(e))}function Qs(e,n,t){t===void 0&&(t=!1);let o=hn(e),r=qr(e),i=Yr(r),s=r==="x"?o===(t?"end":"start")?"right":"left":o==="start"?"bottom":"top";return n.reference[i]>n.floating[i]&&(s=Zn(s)),[s,Zn(s)]}function Zs(e){let n=Zn(e);return[Go(e),n,Go(n)]}function Go(e){return e.replace(/start|end/g,n=>dc[n])}function pc(e,n,t){let o=["left","right"],r=["right","left"],i=["top","bottom"],s=["bottom","top"];switch(e){case"top":case"bottom":return t?n?r:o:n?o:r;case"left":case"right":return n?i:s;default:return[]}}function el(e,n,t,o){let r=hn(e),i=pc(Ct(e),t==="start",o);return r&&(i=i.map(s=>s+"-"+r),n&&(i=i.concat(i.map(Go)))),i}function Zn(e){return e.replace(/left|right|bottom|top/g,n=>fc[n])}function mc(e){return{top:0,right:0,bottom:0,left:0,...e}}function tl(e){return typeof e!="number"?mc(e):{top:e,right:e,bottom:e,left:e}}function Jt(e){return{...e,top:e.y,left:e.x,right:e.x+e.width,bottom:e.y+e.height}}function nl(e,n,t){let{reference:o,floating:r}=e,i=vn(n),s=qr(n),l=Yr(s),a=Ct(n),u=i==="y",c=o.x+o.width/2-r.width/2,d=o.y+o.height/2-r.height/2,p=o[l]/2-r[l]/2,f;switch(a){case"top":f={x:c,y:o.y-r.height};break;case"bottom":f={x:c,y:o.y+o.height};break;case"right":f={x:o.x+o.width,y:d};break;case"left":f={x:o.x-r.width,y:d};break;default:f={x:o.x,y:o.y}}switch(hn(n)){case"start":f[s]-=p*(t&&u?-1:1);break;case"end":f[s]+=p*(t&&u?-1:1);break}return f}var ol=async(e,n,t)=>{let{placement:o="bottom",strategy:r="absolute",middleware:i=[],platform:s}=t,l=i.filter(Boolean),a=await(s.isRTL==null?void 0:s.isRTL(n)),u=await s.getElementRects({reference:e,floating:n,strategy:r}),{x:c,y:d}=nl(u,o,a),p=o,f={},m=0;for(let T=0;T<l.length;T++){let{name:b,fn:g}=l[T],{x:h,y,data:x,reset:v}=await g({x:c,y:d,initialPlacement:o,placement:p,strategy:r,middlewareData:f,rects:u,platform:s,elements:{reference:e,floating:n}});if(c=h!=null?h:c,d=y!=null?y:d,f={...f,[b]:{...f[b],...x}},v&&m<=50){m++,typeof v=="object"&&(v.placement&&(p=v.placement),v.rects&&(u=v.rects===!0?await s.getElementRects({reference:e,floating:n,strategy:r}):v.rects),{x:c,y:d}=nl(u,p,a)),T=-1;continue}}return{x:c,y:d,placement:p,strategy:r,middlewareData:f}};async function mt(e,n){var t;n===void 0&&(n={});let{x:o,y:r,platform:i,rects:s,elements:l,strategy:a}=e,{boundary:u="clippingAncestors",rootBoundary:c="viewport",elementContext:d="floating",altBoundary:p=!1,padding:f=0}=yn(n,e),m=tl(f),b=l[p?d==="floating"?"reference":"floating":d],g=Jt(await i.getClippingRect({element:(t=await(i.isElement==null?void 0:i.isElement(b)))==null||t?b:b.contextElement||await(i.getDocumentElement==null?void 0:i.getDocumentElement(l.floating)),boundary:u,rootBoundary:c,strategy:a})),h=d==="floating"?{...s.floating,x:o,y:r}:s.reference,y=await(i.getOffsetParent==null?void 0:i.getOffsetParent(l.floating)),x=await(i.isElement==null?void 0:i.isElement(y))?await(i.getScale==null?void 0:i.getScale(y))||{x:1,y:1}:{x:1,y:1},v=Jt(i.convertOffsetParentRelativeRectToViewportRelativeRect?await i.convertOffsetParentRelativeRectToViewportRelativeRect({rect:h,offsetParent:y,strategy:a}):h);return{top:(g.top-v.top+m.top)/x.y,bottom:(v.bottom-g.bottom+m.bottom)/x.y,left:(g.left-v.left+m.left)/x.x,right:(v.right-g.right+m.right)/x.x}}var Jr=function(e){return e===void 0&&(e={}),{name:"flip",options:e,async fn(n){var t,o;let{placement:r,middlewareData:i,rects:s,initialPlacement:l,platform:a,elements:u}=n,{mainAxis:c=!0,crossAxis:d=!0,fallbackPlacements:p,fallbackStrategy:f="bestFit",fallbackAxisSideDirection:m="none",flipAlignment:T=!0,...b}=yn(e,n);if((t=i.arrow)!=null&&t.alignmentOffset)return{};let g=Ct(r),h=Ct(l)===l,y=await(a.isRTL==null?void 0:a.isRTL(u.floating)),x=p||(h||!T?[Zn(l)]:Zs(l));!p&&m!=="none"&&x.push(...el(l,T,m,y));let v=[l,...x],A=await mt(n,b),D=[],F=((o=i.flip)==null?void 0:o.overflows)||[];if(c&&D.push(A[g]),d){let C=Qs(r,s,y);D.push(A[C[0]],A[C[1]])}if(F=[...F,{placement:r,overflows:D}],!D.every(C=>C<=0)){var L,S;let C=(((L=i.flip)==null?void 0:L.index)||0)+1,N=v[C];if(N)return{data:{index:C,overflows:F},reset:{placement:N}};let j=(S=F.filter(_=>_.overflows[0]<=0).sort((_,$)=>_.overflows[1]-$.overflows[1])[0])==null?void 0:S.placement;if(!j)switch(f){case"bestFit":{var O;let _=(O=F.map($=>[$.placement,$.overflows.filter(G=>G>0).reduce((G,ne)=>G+ne,0)]).sort(($,G)=>$[1]-G[1])[0])==null?void 0:O[0];_&&(j=_);break}case"initialPlacement":j=l;break}if(r!==j)return{reset:{placement:j}}}return{}}}};async function Tc(e,n){let{placement:t,platform:o,elements:r}=e,i=await(o.isRTL==null?void 0:o.isRTL(r.floating)),s=Ct(t),l=hn(t),a=vn(t)==="y",u=["left","top"].includes(s)?-1:1,c=i&&a?-1:1,d=yn(n,e),{mainAxis:p,crossAxis:f,alignmentAxis:m}=typeof d=="number"?{mainAxis:d,crossAxis:0,alignmentAxis:null}:{mainAxis:0,crossAxis:0,alignmentAxis:null,...d};return l&&typeof m=="number"&&(f=l==="end"?m*-1:m),a?{x:f*c,y:p*u}:{x:p*u,y:f*c}}var Qr=function(e){return e===void 0&&(e=0),{name:"offset",options:e,async fn(n){var t,o;let{x:r,y:i,placement:s,middlewareData:l}=n,a=await Tc(n,e);return s===((t=l.offset)==null?void 0:t.placement)&&(o=l.arrow)!=null&&o.alignmentOffset?{}:{x:r+a.x,y:i+a.y,data:{...a,placement:s}}}}},Zr=function(e){return e===void 0&&(e={}),{name:"shift",options:e,async fn(n){let{x:t,y:o,placement:r}=n,{mainAxis:i=!0,crossAxis:s=!1,limiter:l={fn:b=>{let{x:g,y:h}=b;return{x:g,y:h}}},...a}=yn(e,n),u={x:t,y:o},c=await mt(n,a),d=vn(Ct(r)),p=Xr(d),f=u[p],m=u[d];if(i){let b=p==="y"?"top":"left",g=p==="y"?"bottom":"right",h=f+c[b],y=f-c[g];f=zr(h,f,y)}if(s){let b=d==="y"?"top":"left",g=d==="y"?"bottom":"right",h=m+c[b],y=m-c[g];m=zr(h,m,y)}let T=l.fn({...n,[p]:f,[d]:m});return{...T,data:{x:T.x-t,y:T.y-o}}}}};var ei=function(e){return e===void 0&&(e={}),{name:"size",options:e,async fn(n){let{placement:t,rects:o,platform:r,elements:i}=n,{apply:s=()=>{},...l}=yn(e,n),a=await mt(n,l),u=Ct(t),c=hn(t),d=vn(t)==="y",{width:p,height:f}=o.floating,m,T;u==="top"||u==="bottom"?(m=u,T=c===(await(r.isRTL==null?void 0:r.isRTL(i.floating))?"start":"end")?"left":"right"):(T=u,m=c==="end"?"top":"bottom");let b=f-a[m],g=p-a[T],h=!n.middlewareData.shift,y=b,x=g;if(d){let A=p-a.left-a.right;x=c||h?Nt(g,A):A}else{let A=f-a.top-a.bottom;y=c||h?Nt(b,A):A}if(h&&!c){let A=ke(a.left,0),D=ke(a.right,0),F=ke(a.top,0),L=ke(a.bottom,0);d?x=p-2*(A!==0||D!==0?A+D:ke(a.left,a.right)):y=f-2*(F!==0||L!==0?F+L:ke(a.top,a.bottom))}await s({...n,availableWidth:x,availableHeight:y});let v=await r.getDimensions(i.floating);return p!==v.width||f!==v.height?{reset:{rects:!0}}:{}}}};function Ot(e){return il(e)?(e.nodeName||"").toLowerCase():"#document"}function Ne(e){var n;return(e==null||(n=e.ownerDocument)==null?void 0:n.defaultView)||window}function Tt(e){var n;return(n=(il(e)?e.ownerDocument:e.document)||window.document)==null?void 0:n.documentElement}function il(e){return e instanceof Node||e instanceof Ne(e).Node}function bt(e){return e instanceof Element||e instanceof Ne(e).Element}function rt(e){return e instanceof HTMLElement||e instanceof Ne(e).HTMLElement}function rl(e){return typeof ShadowRoot=="undefined"?!1:e instanceof ShadowRoot||e instanceof Ne(e).ShadowRoot}function En(e){let{overflow:n,overflowX:t,overflowY:o,display:r}=je(e);return/auto|scroll|overlay|hidden|clip/.test(n+o+t)&&!["inline","contents"].includes(r)}function sl(e){return["table","td","th"].includes(Ot(e))}function Wo(e){let n=Vo(),t=je(e);return t.transform!=="none"||t.perspective!=="none"||(t.containerType?t.containerType!=="normal":!1)||!n&&(t.backdropFilter?t.backdropFilter!=="none":!1)||!n&&(t.filter?t.filter!=="none":!1)||["transform","perspective","filter"].some(o=>(t.willChange||"").includes(o))||["paint","layout","strict","content"].some(o=>(t.contain||"").includes(o))}function ll(e){let n=Qt(e);for(;rt(n)&&!no(n);){if(Wo(n))return n;n=Qt(n)}return null}function Vo(){return typeof CSS=="undefined"||!CSS.supports?!1:CSS.supports("-webkit-backdrop-filter","none")}function no(e){return["html","body","#document"].includes(Ot(e))}function je(e){return Ne(e).getComputedStyle(e)}function oo(e){return bt(e)?{scrollLeft:e.scrollLeft,scrollTop:e.scrollTop}:{scrollLeft:e.pageXOffset,scrollTop:e.pageYOffset}}function Qt(e){if(Ot(e)==="html")return e;let n=e.assignedSlot||e.parentNode||rl(e)&&e.host||Tt(e);return rl(n)?n.host:n}function al(e){let n=Qt(e);return no(n)?e.ownerDocument?e.ownerDocument.body:e.body:rt(n)&&En(n)?n:al(n)}function At(e,n,t){var o;n===void 0&&(n=[]),t===void 0&&(t=!0);let r=al(e),i=r===((o=e.ownerDocument)==null?void 0:o.body),s=Ne(r);return i?n.concat(s,s.visualViewport||[],En(r)?r:[],s.frameElement&&t?At(s.frameElement):[]):n.concat(r,At(r,[],t))}function fl(e){let n=je(e),t=parseFloat(n.width)||0,o=parseFloat(n.height)||0,r=rt(e),i=r?e.offsetWidth:t,s=r?e.offsetHeight:o,l=eo(t)!==i||eo(o)!==s;return l&&(t=i,o=s),{width:t,height:o,$:l}}function ti(e){return bt(e)?e:e.contextElement}function xn(e){let n=ti(e);if(!rt(n))return St(1);let t=n.getBoundingClientRect(),{width:o,height:r,$:i}=fl(n),s=(i?eo(t.width):t.width)/o,l=(i?eo(t.height):t.height)/r;return(!s||!Number.isFinite(s))&&(s=1),(!l||!Number.isFinite(l))&&(l=1),{x:s,y:l}}var Ec=St(0);function dl(e){let n=Ne(e);return!Vo()||!n.visualViewport?Ec:{x:n.visualViewport.offsetLeft,y:n.visualViewport.offsetTop}}function xc(e,n,t){return n===void 0&&(n=!1),!t||n&&t!==Ne(e)?!1:n}function Zt(e,n,t,o){n===void 0&&(n=!1),t===void 0&&(t=!1);let r=e.getBoundingClientRect(),i=ti(e),s=St(1);n&&(o?bt(o)&&(s=xn(o)):s=xn(e));let l=xc(i,t,o)?dl(i):St(0),a=(r.left+l.x)/s.x,u=(r.top+l.y)/s.y,c=r.width/s.x,d=r.height/s.y;if(i){let p=Ne(i),f=o&&bt(o)?Ne(o):o,m=p.frameElement;for(;m&&o&&f!==p;){let T=xn(m),b=m.getBoundingClientRect(),g=je(m),h=b.left+(m.clientLeft+parseFloat(g.paddingLeft))*T.x,y=b.top+(m.clientTop+parseFloat(g.paddingTop))*T.y;a*=T.x,u*=T.y,c*=T.x,d*=T.y,a+=h,u+=y,m=Ne(m).frameElement}}return Jt({width:c,height:d,x:a,y:u})}function Pc(e){let{rect:n,offsetParent:t,strategy:o}=e,r=rt(t),i=Tt(t);if(t===i)return n;let s={scrollLeft:0,scrollTop:0},l=St(1),a=St(0);if((r||!r&&o!=="fixed")&&((Ot(t)!=="body"||En(i))&&(s=oo(t)),rt(t))){let u=Zt(t);l=xn(t),a.x=u.x+t.clientLeft,a.y=u.y+t.clientTop}return{width:n.width*l.x,height:n.height*l.y,x:n.x*l.x-s.scrollLeft*l.x+a.x,y:n.y*l.y-s.scrollTop*l.y+a.y}}function Rc(e){return Array.from(e.getClientRects())}function pl(e){return Zt(Tt(e)).left+oo(e).scrollLeft}function Sc(e){let n=Tt(e),t=oo(e),o=e.ownerDocument.body,r=ke(n.scrollWidth,n.clientWidth,o.scrollWidth,o.clientWidth),i=ke(n.scrollHeight,n.clientHeight,o.scrollHeight,o.clientHeight),s=-t.scrollLeft+pl(e),l=-t.scrollTop;return je(o).direction==="rtl"&&(s+=ke(n.clientWidth,o.clientWidth)-r),{width:r,height:i,x:s,y:l}}function Cc(e,n){let t=Ne(e),o=Tt(e),r=t.visualViewport,i=o.clientWidth,s=o.clientHeight,l=0,a=0;if(r){i=r.width,s=r.height;let u=Vo();(!u||u&&n==="fixed")&&(l=r.offsetLeft,a=r.offsetTop)}return{width:i,height:s,x:l,y:a}}function Ac(e,n){let t=Zt(e,!0,n==="fixed"),o=t.top+e.clientTop,r=t.left+e.clientLeft,i=rt(e)?xn(e):St(1),s=e.clientWidth*i.x,l=e.clientHeight*i.y,a=r*i.x,u=o*i.y;return{width:s,height:l,x:a,y:u}}function ul(e,n,t){let o;if(n==="viewport")o=Cc(e,t);else if(n==="document")o=Sc(Tt(e));else if(bt(n))o=Ac(n,t);else{let r=dl(e);o={...n,x:n.x-r.x,y:n.y-r.y}}return Jt(o)}function ml(e,n){let t=Qt(e);return t===n||!bt(t)||no(t)?!1:je(t).position==="fixed"||ml(t,n)}function Oc(e,n){let t=n.get(e);if(t)return t;let o=At(e,[],!1).filter(l=>bt(l)&&Ot(l)!=="body"),r=null,i=je(e).position==="fixed",s=i?Qt(e):e;for(;bt(s)&&!no(s);){let l=je(s),a=Wo(s);!a&&l.position==="fixed"&&(r=null),(i?!a&&!r:!a&&l.position==="static"&&!!r&&["absolute","fixed"].includes(r.position)||En(s)&&!a&&ml(e,s))?o=o.filter(c=>c!==s):r=l,s=Qt(s)}return n.set(e,o),o}function Lc(e){let{element:n,boundary:t,rootBoundary:o,strategy:r}=e,s=[...t==="clippingAncestors"?Oc(n,this._c):[].concat(t),o],l=s[0],a=s.reduce((u,c)=>{let d=ul(n,c,r);return u.top=ke(d.top,u.top),u.right=Nt(d.right,u.right),u.bottom=Nt(d.bottom,u.bottom),u.left=ke(d.left,u.left),u},ul(n,l,r));return{width:a.right-a.left,height:a.bottom-a.top,x:a.left,y:a.top}}function Dc(e){return fl(e)}function Ic(e,n,t){let o=rt(n),r=Tt(n),i=t==="fixed",s=Zt(e,!0,i,n),l={scrollLeft:0,scrollTop:0},a=St(0);if(o||!o&&!i)if((Ot(n)!=="body"||En(r))&&(l=oo(n)),o){let u=Zt(n,!0,i,n);a.x=u.x+n.clientLeft,a.y=u.y+n.clientTop}else r&&(a.x=pl(r));return{x:s.left+l.scrollLeft-a.x,y:s.top+l.scrollTop-a.y,width:s.width,height:s.height}}function cl(e,n){return!rt(e)||je(e).position==="fixed"?null:n?n(e):e.offsetParent}function Tl(e,n){let t=Ne(e);if(!rt(e))return t;let o=cl(e,n);for(;o&&sl(o)&&je(o).position==="static";)o=cl(o,n);return o&&(Ot(o)==="html"||Ot(o)==="body"&&je(o).position==="static"&&!Wo(o))?t:o||ll(e)||t}var Fc=async function(e){let{reference:n,floating:t,strategy:o}=e,r=this.getOffsetParent||Tl,i=this.getDimensions;return{reference:Ic(n,await r(t),o),floating:{x:0,y:0,...await i(t)}}};function Mc(e){return je(e).direction==="rtl"}var jo={convertOffsetParentRelativeRectToViewportRelativeRect:Pc,getDocumentElement:Tt,getClippingRect:Lc,getOffsetParent:Tl,getElementRects:Fc,getClientRects:Rc,getDimensions:Dc,getScale:xn,isElement:bt,isRTL:Mc};function wc(e,n){let t=null,o,r=Tt(e);function i(){clearTimeout(o),t&&t.disconnect(),t=null}function s(l,a){l===void 0&&(l=!1),a===void 0&&(a=1),i();let{left:u,top:c,width:d,height:p}=e.getBoundingClientRect();if(l||n(),!d||!p)return;let f=to(c),m=to(r.clientWidth-(u+d)),T=to(r.clientHeight-(c+p)),b=to(u),h={rootMargin:-f+"px "+-m+"px "+-T+"px "+-b+"px",threshold:ke(0,Nt(1,a))||1},y=!0;function x(v){let A=v[0].intersectionRatio;if(A!==a){if(!y)return s();A?s(!1,A):o=setTimeout(()=>{s(!1,1e-7)},100)}y=!1}try{t=new IntersectionObserver(x,{...h,root:r.ownerDocument})}catch{t=new IntersectionObserver(x,h)}t.observe(e)}return s(!0),i}function Ko(e,n,t,o){o===void 0&&(o={});let{ancestorScroll:r=!0,ancestorResize:i=!0,elementResize:s=typeof ResizeObserver=="function",layoutShift:l=typeof IntersectionObserver=="function",animationFrame:a=!1}=o,u=ti(e),c=r||i?[...u?At(u):[],...At(n)]:[];c.forEach(g=>{r&&g.addEventListener("scroll",t,{passive:!0}),i&&g.addEventListener("resize",t)});let d=u&&l?wc(u,t):null,p=-1,f=null;s&&(f=new ResizeObserver(g=>{let[h]=g;h&&h.target===u&&f&&(f.unobserve(n),cancelAnimationFrame(p),p=requestAnimationFrame(()=>{f&&f.observe(n)})),t()}),u&&!a&&f.observe(u),f.observe(n));let m,T=a?Zt(e):null;a&&b();function b(){let g=Zt(e);T&&(g.x!==T.x||g.y!==T.y||g.width!==T.width||g.height!==T.height)&&t(),T=g,m=requestAnimationFrame(b)}return t(),()=>{c.forEach(g=>{r&&g.removeEventListener("scroll",t),i&&g.removeEventListener("resize",t)}),d&&d(),f&&f.disconnect(),f=null,a&&cancelAnimationFrame(m)}}var zo=(e,n,t)=>{let o=new Map,r={platform:jo,...t},i={...r.platform,_c:o};return ol(e,n,{...r,platform:i})};var Ae=ie(require("react"),1),qo=require("react"),yl=ie(require("react-dom"),1),Xo=typeof document!="undefined"?qo.useLayoutEffect:qo.useEffect;function Yo(e,n){if(e===n)return!0;if(typeof e!=typeof n)return!1;if(typeof e=="function"&&e.toString()===n.toString())return!0;let t,o,r;if(e&&n&&typeof e=="object"){if(Array.isArray(e)){if(t=e.length,t!==n.length)return!1;for(o=t;o--!==0;)if(!Yo(e[o],n[o]))return!1;return!0}if(r=Object.keys(e),t=r.length,t!==Object.keys(n).length)return!1;for(o=t;o--!==0;)if(!{}.hasOwnProperty.call(n,r[o]))return!1;for(o=t;o--!==0;){let i=r[o];if(!(i==="_owner"&&e.$$typeof)&&!Yo(e[i],n[i]))return!1}return!0}return e!==e&&n!==n}function hl(e){return typeof window=="undefined"?1:(e.ownerDocument.defaultView||window).devicePixelRatio||1}function bl(e,n){let t=hl(e);return Math.round(n*t)/t}function gl(e){let n=Ae.useRef(e);return Xo(()=>{n.current=e}),n}function vl(e){e===void 0&&(e={});let{placement:n="bottom",strategy:t="absolute",middleware:o=[],platform:r,elements:{reference:i,floating:s}={},transform:l=!0,whileElementsMounted:a,open:u}=e,[c,d]=Ae.useState({x:0,y:0,strategy:t,placement:n,middlewareData:{},isPositioned:!1}),[p,f]=Ae.useState(o);Yo(p,o)||f(o);let[m,T]=Ae.useState(null),[b,g]=Ae.useState(null),h=Ae.useCallback(G=>{G!==A.current&&(A.current=G,T(G))},[]),y=Ae.useCallback(G=>{G!==D.current&&(D.current=G,g(G))},[]),x=i||m,v=s||b,A=Ae.useRef(null),D=Ae.useRef(null),F=Ae.useRef(c),L=a!=null,S=gl(a),O=gl(r),C=Ae.useCallback(()=>{if(!A.current||!D.current)return;let G={placement:n,strategy:t,middleware:p};O.current&&(G.platform=O.current),zo(A.current,D.current,G).then(ne=>{let J={...ne,isPositioned:!0};N.current&&!Yo(F.current,J)&&(F.current=J,yl.flushSync(()=>{d(J)}))})},[p,n,t,O]);Xo(()=>{u===!1&&F.current.isPositioned&&(F.current.isPositioned=!1,d(G=>({...G,isPositioned:!1})))},[u]);let N=Ae.useRef(!1);Xo(()=>(N.current=!0,()=>{N.current=!1}),[]),Xo(()=>{if(x&&(A.current=x),v&&(D.current=v),x&&v){if(S.current)return S.current(x,v,C);C()}},[x,v,C,S,L]);let j=Ae.useMemo(()=>({reference:A,floating:D,setReference:h,setFloating:y}),[h,y]),_=Ae.useMemo(()=>({reference:x,floating:v}),[x,v]),$=Ae.useMemo(()=>{let G={position:t,left:0,top:0};if(!_.floating)return G;let ne=bl(_.floating,c.x),J=bl(_.floating,c.y);return l?{...G,transform:"translate("+ne+"px, "+J+"px)",...hl(_.floating)>=1.5&&{willChange:"transform"}}:{position:t,left:ne,top:J}},[t,l,_.floating,c.x,c.y]);return Ae.useMemo(()=>({...c,update:C,refs:j,elements:_,floatingStyles:$}),[c,C,j,_,$])}var ro=(e,n)=>({...Qr(e),options:[e,n]}),ni=(e,n)=>({...Zr(e),options:[e,n]});var oi=(e,n)=>({...Jr(e),options:[e,n]}),ri=(e,n)=>({...ei(e),options:[e,n]});var Pn=require("react-dom");var Cl={...Z},_c=Cl.useInsertionEffect,Hc=_c||(e=>e());function Al(e){let n=Z.useRef(()=>{});return Hc(()=>{n.current=e}),Z.useCallback(function(){for(var t=arguments.length,o=new Array(t),r=0;r<t;r++)o[r]=arguments[r];return n.current==null?void 0:n.current(...o)},[])}var kc="ArrowUp",$c="ArrowDown",Nc="ArrowLeft",Bc="ArrowRight";var si=typeof document!="undefined"?io.useLayoutEffect:io.useEffect;var Uc=[Nc,Bc],Gc=[kc,$c],jy=[...Uc,...Gc];var El=!1,Wc=0,xl=()=>"floating-ui-"+Math.random().toString(36).slice(2,6)+Wc++;function Vc(){let[e,n]=Z.useState(()=>El?xl():void 0);return si(()=>{e==null&&n(xl())},[]),Z.useEffect(()=>{El=!0},[]),e}var jc=Cl.useId,Kc=jc||Vc;function zc(){let e=new Map;return{emit(n,t){var o;(o=e.get(n))==null||o.forEach(r=>r(t))},on(n,t){e.set(n,[...e.get(n)||[],t])},off(n,t){var o;e.set(n,((o=e.get(n))==null?void 0:o.filter(r=>r!==t))||[])}}}var Xc=Z.createContext(null),Yc=Z.createContext(null),qc=()=>{var e;return((e=Z.useContext(Xc))==null?void 0:e.id)||null},Jc=()=>Z.useContext(Yc);function Qc(e){let{open:n=!1,onOpenChange:t,elements:o}=e,r=Kc(),i=Z.useRef({}),[s]=Z.useState(()=>zc()),l=qc()!=null,[a,u]=Z.useState(o.reference),c=Al((f,m,T)=>{i.current.openEvent=f?m:void 0,s.emit("openchange",{open:f,event:m,reason:T,nested:l}),t==null||t(f,m,T)}),d=Z.useMemo(()=>({setPositionReference:u}),[]),p=Z.useMemo(()=>({reference:a||o.reference||null,floating:o.floating||null,domReference:o.reference}),[a,o.reference,o.floating]);return Z.useMemo(()=>({dataRef:i,open:n,onOpenChange:c,elements:p,events:s,floatingId:r,refs:d}),[n,c,p,s,r,d])}function Ol(e){e===void 0&&(e={});let{nodeId:n}=e,t=Qc({...e,elements:{reference:null,floating:null,...e.elements}}),o=e.rootContext||t,r=o.elements,[i,s]=Z.useState(null),[l,a]=Z.useState(null),c=(r==null?void 0:r.reference)||i,d=Z.useRef(null),p=Jc();si(()=>{c&&(d.current=c)},[c]);let f=vl({...e,elements:{...r,...l&&{reference:l}}}),m=Z.useCallback(y=>{let x=Qn(y)?{getBoundingClientRect:()=>y.getBoundingClientRect(),contextElement:y}:y;a(x),f.refs.setReference(x)},[f.refs]),T=Z.useCallback(y=>{(Qn(y)||y===null)&&(d.current=y,s(y)),(Qn(f.refs.reference.current)||f.refs.reference.current===null||y!==null&&!Qn(y))&&f.refs.setReference(y)},[f.refs]),b=Z.useMemo(()=>({...f.refs,setReference:T,setPositionReference:m,domReference:d}),[f.refs,T,m]),g=Z.useMemo(()=>({...f.elements,domReference:c}),[f.elements,c]),h=Z.useMemo(()=>({...f,...o,refs:b,elements:g,nodeId:n}),[f,b,g,n,o]);return si(()=>{o.dataRef.current.floatingContext=h;let y=p==null?void 0:p.nodesRef.current.find(x=>x.id===n);y&&(y.context=h)}),Z.useMemo(()=>({...f,context:h,refs:b,elements:g}),[f,b,g,h])}var Pl="active",Rl="selected";function ii(e,n,t){let o=new Map,r=t==="item",i=e;if(r&&e){let{[Pl]:s,[Rl]:l,...a}=e;i=a}return{...t==="floating"&&{tabIndex:-1},...i,...n.map(s=>{let l=s?s[t]:null;return typeof l=="function"?e?l(e):null:l}).concat(e).reduce((s,l)=>(l&&Object.entries(l).forEach(a=>{let[u,c]=a;if(!(r&&[Pl,Rl].includes(u)))if(u.indexOf("on")===0){if(o.has(u)||o.set(u,[]),typeof c=="function"){var d;(d=o.get(u))==null||d.push(c),s[u]=function(){for(var p,f=arguments.length,m=new Array(f),T=0;T<f;T++)m[T]=arguments[T];return(p=o.get(u))==null?void 0:p.map(b=>b(...m)).find(b=>b!==void 0)}}}else s[u]=c}),s),{})}}function Ll(e){e===void 0&&(e=[]);let n=e,t=Z.useCallback(i=>ii(i,e,"reference"),n),o=Z.useCallback(i=>ii(i,e,"floating"),n),r=Z.useCallback(i=>ii(i,e,"item"),e.map(i=>i==null?void 0:i.item));return Z.useMemo(()=>({getReferenceProps:t,getFloatingProps:o,getItemProps:r}),[t,o,r])}function Sl(e,n){return{...e,rects:{...e.rects,floating:{...e.rects.floating,height:n}}}}var Dl=e=>({name:"inner",options:e,async fn(n){let{listRef:t,overflowRef:o,onFallbackChange:r,offset:i=0,index:s=0,minItemsVisible:l=4,referenceOverflowThreshold:a=0,scrollRef:u,...c}=e,{rects:d,elements:{floating:p}}=n,f=t.current[s];if(!f)return{};let m={...n,...await ro(-f.offsetTop-p.clientTop-d.reference.height/2-f.offsetHeight/2-i).fn(n)},T=(u==null?void 0:u.current)||p,b=await mt(Sl(m,T.scrollHeight),c),g=await mt(m,{...c,elementContext:"reference"}),h=Math.max(0,b.top),y=m.y+h,x=Math.max(0,T.scrollHeight-h-Math.max(0,b.bottom));return T.style.maxHeight=x+"px",T.scrollTop=h,r&&(T.offsetHeight<f.offsetHeight*Math.min(l,t.current.length-1)-1||g.top>=-a||g.bottom>=-a?(0,Pn.flushSync)(()=>r(!0)):(0,Pn.flushSync)(()=>r(!1))),o&&(o.current=await mt(Sl({...m,y},T.offsetHeight),c)),{y}}});function Il(e,n){let{open:t,elements:o}=e,{enabled:r=!0,overflowRef:i,scrollRef:s,onChange:l}=n,a=Al(l),u=Z.useRef(!1),c=Z.useRef(null),d=Z.useRef(null);return Z.useEffect(()=>{if(!r)return;function p(m){if(m.ctrlKey||!f||i.current==null)return;let T=m.deltaY,b=i.current.top>=-.5,g=i.current.bottom>=-.5,h=f.scrollHeight-f.clientHeight,y=T<0?-1:1,x=T<0?"max":"min";f.scrollHeight<=f.clientHeight||(!b&&T>0||!g&&T<0?(m.preventDefault(),(0,Pn.flushSync)(()=>{a(v=>v+Math[x](T,h*y))})):/firefox/i.test(Js())&&(f.scrollTop+=T))}let f=(s==null?void 0:s.current)||o.floating;if(t&&f)return f.addEventListener("wheel",p),requestAnimationFrame(()=>{c.current=f.scrollTop,i.current!=null&&(d.current={...i.current})}),()=>{c.current=null,d.current=null,f.removeEventListener("wheel",p)}},[r,t,o.floating,i,s,a]),Z.useMemo(()=>r?{floating:{onKeyDown(){u.current=!0},onWheel(){u.current=!1},onPointerMove(){u.current=!1},onScroll(){let p=(s==null?void 0:s.current)||o.floating;if(!(!i.current||!p||!u.current)){if(c.current!==null){let f=p.scrollTop-c.current;(i.current.bottom<-.5&&f<-1||i.current.top<-.5&&f>1)&&(0,Pn.flushSync)(()=>a(m=>m+f))}requestAnimationFrame(()=>{c.current=p.scrollTop})}}}}:{},[r,i,o.floating,s,a])}var ai=ie(require("react"),1),ve=require("react");var Rn=(0,ve.createContext)({styles:void 0,setReference:()=>{},setFloating:()=>{},getReferenceProps:()=>({}),getFloatingProps:()=>({}),slot:{}});Rn.displayName="FloatingContext";var ui=(0,ve.createContext)(null);ui.displayName="PlacementContext";function Bt(e){return(0,ve.useMemo)(()=>e?typeof e=="string"?{to:e}:e:null,[e])}function Ut(){return(0,ve.useContext)(Rn).setReference}function Jo(){return(0,ve.useContext)(Rn).getReferenceProps}function Gt(){let{getFloatingProps:e,slot:n}=(0,ve.useContext)(Rn);return(0,ve.useCallback)((...t)=>Object.assign({},e(...t),{"data-anchor":n.anchor}),[e,n])}function Wt(e=null){e===!1&&(e=null),typeof e=="string"&&(e={to:e});let n=(0,ve.useContext)(ui),t=(0,ve.useMemo)(()=>e,[JSON.stringify(e,(r,i)=>{var s;return(s=i==null?void 0:i.outerHTML)!=null?s:i})]);W(()=>{n==null||n(t!=null?t:null)},[n,t]);let o=(0,ve.useContext)(Rn);return(0,ve.useMemo)(()=>[o.setFloating,e?o.styles:{}],[o.setFloating,e,o.styles])}var Fl=4;function Vt({children:e,enabled:n=!0}){let[t,o]=(0,ve.useState)(null),[r,i]=(0,ve.useState)(0),s=(0,ve.useRef)(null),[l,a]=(0,ve.useState)(null);Zc(l);let u=n&&t!==null&&l!==null,{to:c="bottom",gap:d=0,offset:p=0,padding:f=0,inner:m}=ef(t,l),[T,b="center"]=c.split(" ");W(()=>{u&&i(0)},[u]);let{refs:g,floatingStyles:h,context:y}=Ol({open:u,placement:T==="selection"?b==="center"?"bottom":`bottom-${b}`:b==="center"?`${T}`:`${T}-${b}`,strategy:"absolute",transform:!1,middleware:[ro({mainAxis:T==="selection"?0:d,crossAxis:p}),ni({padding:f}),T!=="selection"&&oi({padding:f}),T==="selection"&&m?Dl({...m,padding:f,overflowRef:s,offset:r,minItemsVisible:Fl,referenceOverflowThreshold:f,onFallbackChange(O){var G,ne;if(!O)return;let C=y.elements.floating;if(!C)return;let N=parseFloat(getComputedStyle(C).scrollPaddingBottom)||0,j=Math.min(Fl,C.childElementCount),_=0,$=0;for(let J of(ne=(G=y.elements.floating)==null?void 0:G.childNodes)!=null?ne:[])if(J instanceof HTMLElement){let R=J.offsetTop,P=R+J.clientHeight+N,w=C.scrollTop,k=w+C.clientHeight;if(R>=w&&P<=k)j--;else{$=Math.max(0,Math.min(P,k)-Math.max(R,w)),_=J.clientHeight;break}}j>=1&&i(J=>{let R=_*j-$+N;return J>=R?J:R})}}):null,ri({padding:f,apply({availableWidth:O,availableHeight:C,elements:N}){Object.assign(N.floating.style,{overflow:"auto",maxWidth:`${O}px`,maxHeight:`min(var(--anchor-max-height, 100vh), ${C}px)`})}})].filter(Boolean),whileElementsMounted:Ko}),[x=T,v=b]=y.placement.split("-");T==="selection"&&(x="selection");let A=(0,ve.useMemo)(()=>({anchor:[x,v].filter(Boolean).join(" ")}),[x,v]),D=Il(y,{overflowRef:s,onChange:i}),{getReferenceProps:F,getFloatingProps:L}=Ll([D]),S=E(O=>{a(O),g.setFloating(O)});return ai.createElement(ui.Provider,{value:o},ai.createElement(Rn.Provider,{value:{setFloating:S,setReference:g.setReference,styles:h,getReferenceProps:F,getFloatingProps:L,slot:A}},e))}function Zc(e){W(()=>{if(!e)return;let n=new MutationObserver(()=>{let t=window.getComputedStyle(e).maxHeight,o=parseFloat(t);if(isNaN(o))return;let r=parseInt(t);isNaN(r)||o!==r&&(e.style.maxHeight=`${Math.ceil(o)}px`)});return n.observe(e,{attributes:!0,attributeFilter:["style"]}),()=>{n.disconnect()}},[e])}function ef(e,n){var i,s,l;let t=li((i=e==null?void 0:e.gap)!=null?i:"var(--anchor-gap, 0)",n),o=li((s=e==null?void 0:e.offset)!=null?s:"var(--anchor-offset, 0)",n),r=li((l=e==null?void 0:e.padding)!=null?l:"var(--anchor-padding, 0)",n);return{...e,gap:t,offset:o,padding:r}}function li(e,n,t=void 0){let o=Re(),r=E((a,u)=>{if(a==null)return[t,null];if(typeof a=="number")return[a,null];if(typeof a=="string"){if(!u)return[t,null];let c=Ml(a,u);return[c,d=>{let p=wl(a);{let f=p.map(m=>window.getComputedStyle(u).getPropertyValue(m));o.requestAnimationFrame(function m(){o.nextFrame(m);let T=!1;for(let[g,h]of p.entries()){let y=window.getComputedStyle(u).getPropertyValue(h);if(f[g]!==y){f[g]=y,T=!0;break}}if(!T)return;let b=Ml(a,u);c!==b&&(d(b),c=b)})}return o.dispose}]}return[t,null]}),i=(0,ve.useMemo)(()=>r(e,n)[0],[e,n]),[s=i,l]=(0,ve.useState)();return W(()=>{let[a,u]=r(e,n);if(l(a),!!u)return u(l)},[e,n]),s}function wl(e){let n=/var\((.*)\)/.exec(e);if(n){let t=n[1].indexOf(",");if(t===-1)return[n[1]];let o=n[1].slice(0,t).trim(),r=n[1].slice(t+1).trim();return r?[o,...wl(r)]:[o]}return[]}function Ml(e,n){let t=document.createElement("div");n.appendChild(t),t.style.setProperty("margin-top","0px","important"),t.style.setProperty("margin-top",e,"important");let o=parseFloat(window.getComputedStyle(t).marginTop)||0;return n.removeChild(t),o}var so=ie(require("react"),1);function _l({children:e,freeze:n}){let t=Sn(n,e);return so.default.createElement(so.default.Fragment,null,t)}function Sn(e,n){let[t,o]=(0,so.useState)(n);return!e&&t!==n&&o(n),e?t:n}var en=ie(require("react"),1),Qo=(0,en.createContext)(null);Qo.displayName="OpenClosedContext";function Me(){return(0,en.useContext)(Qo)}function Ye({value:e,children:n}){return en.default.createElement(Qo.Provider,{value:e},n)}function Cn({children:e}){return en.default.createElement(Qo.Provider,{value:null},e)}var kl=require("use-sync-external-store/with-selector");var gt,An,On,jt=class{constructor(n){To(this,gt,{});To(this,An,new mn(()=>new Set));To(this,On,new Set);vr(this,gt,n)}get state(){return Ke(this,gt)}subscribe(n,t){let o={selector:n,callback:t,current:n(Ke(this,gt))};return Ke(this,On).add(o),()=>{Ke(this,On).delete(o)}}on(n,t){return Ke(this,An).get(n).add(t),()=>{Ke(this,An).get(n).delete(t)}}send(n){vr(this,gt,this.reduce(Ke(this,gt),n));for(let t of Ke(this,On)){let o=t.selector(Ke(this,gt));fi(t.current,o)||(t.current=o,t.callback(o))}for(let t of Ke(this,An).get(n.type))t(Ke(this,gt),n)}};gt=new WeakMap,An=new WeakMap,On=new WeakMap;function fi(e,n){return Object.is(e,n)?!0:typeof e!="object"||e===null||typeof n!="object"||n===null?!1:Array.isArray(e)&&Array.isArray(n)?e.length!==n.length?!1:ci(e[Symbol.iterator](),n[Symbol.iterator]()):e instanceof Map&&n instanceof Map||e instanceof Set&&n instanceof Set?e.size!==n.size?!1:ci(e.entries(),n.entries()):Hl(e)&&Hl(n)?ci(Object.entries(e)[Symbol.iterator](),Object.entries(n)[Symbol.iterator]()):!1}function ci(e,n){do{let t=e.next(),o=n.next();if(t.done&&o.done)return!0;if(t.done||o.done||!Object.is(t.value,o.value))return!1}while(!0)}function Hl(e){if(Object.prototype.toString.call(e)!=="[object Object]")return!1;let n=Object.getPrototypeOf(e);return n===null||Object.getPrototypeOf(n)===null}function tn(e){let[n,t]=e(),o=he();return(...r)=>{n(...r),o.dispose(),o.microTask(t)}}function le(e,n,t=fi){return(0,kl.useSyncExternalStoreWithSelector)(E(o=>e.subscribe(tf,o)),E(()=>e.state),E(()=>e.state),E(n),t)}function tf(e){return e}function $l(e){function n(){document.readyState!=="loading"&&(e(),document.removeEventListener("DOMContentLoaded",n))}typeof window!="undefined"&&typeof document!="undefined"&&(document.addEventListener("DOMContentLoaded",n),n())}var qe=[];$l(()=>{function e(n){if(!(n.target instanceof HTMLElement)||n.target===document.body||qe[0]===n.target)return;let t=n.target;t=t.closest(Xn),qe.unshift(t!=null?t:n.target),qe=qe.filter(o=>o!=null&&o.isConnected),qe.splice(10)}window.addEventListener("click",e,{capture:!0}),window.addEventListener("mousedown",e,{capture:!0}),window.addEventListener("focus",e,{capture:!0}),document.body.addEventListener("click",e,{capture:!0}),document.body.addEventListener("mousedown",e,{capture:!0}),document.body.addEventListener("focus",e,{capture:!0})});function nf(e){throw new Error("Unexpected object: "+e)}function Je(e,n){let t=n.resolveItems();if(t.length<=0)return null;let o=n.resolveActiveIndex(),r=o!=null?o:-1;switch(e.focus){case 0:{for(let i=0;i<t.length;++i)if(!n.resolveDisabled(t[i],i,t))return i;return o}case 1:{r===-1&&(r=t.length);for(let i=r-1;i>=0;--i)if(!n.resolveDisabled(t[i],i,t))return i;return o}case 2:{for(let i=r+1;i<t.length;++i)if(!n.resolveDisabled(t[i],i,t))return i;return o}case 3:{for(let i=t.length-1;i>=0;--i)if(!n.resolveDisabled(t[i],i,t))return i;return o}case 4:{for(let i=0;i<t.length;++i)if(n.resolveId(t[i],i,t)===e.id)return i;return o}case 5:return null;default:nf(e)}}var be=ie(require("react"),1),Ul=require("react-dom");var Zo=require("react");function er(e){let n=E(e),t=(0,Zo.useRef)(!1);(0,Zo.useEffect)(()=>(t.current=!1,()=>{t.current=!0,Et(()=>{t.current&&n()})}),[n])}var nn=ie(require("react"),1);function of(){let e=typeof document=="undefined";return"useSyncExternalStore"in nn?(o=>o.useSyncExternalStore)(nn)(()=>()=>{},()=>!1,()=>!e):!1}function Lt(){let e=of(),[n,t]=nn.useState(et.isHandoffComplete);return n&&et.isHandoffComplete===!1&&t(!1),nn.useEffect(()=>{n!==!0&&t(!0)},[n]),nn.useEffect(()=>et.handoff(),[]),e?!1:n}var Ln=ie(require("react"),1),Nl=(0,Ln.createContext)(!1);function Bl(){return(0,Ln.useContext)(Nl)}function di(e){return Ln.default.createElement(Nl.Provider,{value:e.force},e.children)}function rf(e){let n=Bl(),t=(0,be.useContext)(Wl),[o,r]=(0,be.useState)(()=>{var l;if(!n&&t!==null)return(l=t.current)!=null?l:null;if(et.isServer)return null;let i=e==null?void 0:e.getElementById("headlessui-portal-root");if(i)return i;if(e===null)return null;let s=e.createElement("div");return s.setAttribute("id","headlessui-portal-root"),e.body.appendChild(s)});return(0,be.useEffect)(()=>{o!==null&&(e!=null&&e.body.contains(o)||e==null||e.body.appendChild(o))},[o,e]),(0,be.useEffect)(()=>{n||t!==null&&r(t.current)},[t,r,n]),o}var Gl=be.Fragment,sf=M(function(n,t){let{ownerDocument:o=null,...r}=n,i=(0,be.useRef)(null),s=K(cn(T=>{i.current=T}),t),l=Pe(i),a=o!=null?o:l,u=rf(a),[c]=(0,be.useState)(()=>{var T;return et.isServer?null:(T=a==null?void 0:a.createElement("div"))!=null?T:null}),d=(0,be.useContext)(pi),p=Lt();W(()=>{!u||!c||u.contains(c)||(c.setAttribute("data-headlessui-portal",""),u.appendChild(c))},[u,c]),W(()=>{if(c&&d)return d.register(c)},[d,c]),er(()=>{var T;!u||!c||(c instanceof Node&&u.contains(c)&&u.removeChild(c),u.childNodes.length<=0&&((T=u.parentElement)==null||T.removeChild(u)))});let f=H();return p?!u||!c?null:(0,Ul.createPortal)(f({ourProps:{ref:s},theirProps:r,slot:{},defaultTag:Gl,name:"Portal"}),c):null});function lf(e,n){let t=K(n),{enabled:o=!0,ownerDocument:r,...i}=e,s=H();return o?be.default.createElement(sf,{...i,ownerDocument:r,ref:t}):s({ourProps:{ref:t},theirProps:i,slot:{},defaultTag:Gl,name:"Portal"})}var af=be.Fragment,Wl=(0,be.createContext)(null);function uf(e,n){let{target:t,...o}=e,i={ref:K(n)},s=H();return be.default.createElement(Wl.Provider,{value:t},s({ourProps:i,theirProps:o,defaultTag:af,name:"Popover.Group"}))}var pi=(0,be.createContext)(null);function tr(){let e=(0,be.useContext)(pi),n=(0,be.useRef)([]),t=E(i=>(n.current.push(i),e&&e.register(i),()=>o(i))),o=E(i=>{let s=n.current.indexOf(i);s!==-1&&n.current.splice(s,1),e&&e.unregister(i)}),r=(0,be.useMemo)(()=>({register:t,unregister:o,portals:n}),[t,o,n]);return[n,(0,be.useMemo)(()=>function({children:s}){return be.default.createElement(pi.Provider,{value:r},s)},[r])]}var cf=M(lf),mi=M(uf),Qe=Object.assign(cf,{Group:mi});function Ti(e,n=t=>t){let t=e.activeOptionIndex!==null?e.options[e.activeOptionIndex]:null,o=n(e.options.slice()),r=o.length>0&&o[0].dataRef.current.order!==null?o.sort((s,l)=>s.dataRef.current.order-l.dataRef.current.order):He(o,s=>s.dataRef.current.domRef.current),i=t?r.indexOf(t):null;return i===-1&&(i=null),{options:r,activeOptionIndex:i}}var ff={[1](e){var n;return(n=e.dataRef.current)!=null&&n.disabled||e.comboboxState===1?e:{...e,activeOptionIndex:null,comboboxState:1,isTyping:!1,activationTrigger:2,__demoMode:!1}},[0](e){var n,t;if((n=e.dataRef.current)!=null&&n.disabled||e.comboboxState===0)return e;if((t=e.dataRef.current)!=null&&t.value){let o=e.dataRef.current.calculateIndex(e.dataRef.current.value);if(o!==-1)return{...e,activeOptionIndex:o,comboboxState:0,__demoMode:!1}}return{...e,comboboxState:0,__demoMode:!1}},[3](e,n){return e.isTyping===n.isTyping?e:{...e,isTyping:n.isTyping}},[2](e,n){var i,s,l,a;if((i=e.dataRef.current)!=null&&i.disabled||e.optionsElement&&!((s=e.dataRef.current)!=null&&s.optionsPropsRef.current.static)&&e.comboboxState===1)return e;if(e.virtual){let{options:u,disabled:c}=e.virtual,d=n.focus===4?n.idx:Je(n,{resolveItems:()=>u,resolveActiveIndex:()=>{var f,m;return(m=(f=e.activeOptionIndex)!=null?f:u.findIndex(T=>!c(T)))!=null?m:null},resolveDisabled:c,resolveId(){throw new Error("Function not implemented.")}}),p=(l=n.trigger)!=null?l:2;return e.activeOptionIndex===d&&e.activationTrigger===p?e:{...e,activeOptionIndex:d,activationTrigger:p,isTyping:!1,__demoMode:!1}}let t=Ti(e);if(t.activeOptionIndex===null){let u=t.options.findIndex(c=>!c.dataRef.current.disabled);u!==-1&&(t.activeOptionIndex=u)}let o=n.focus===4?n.idx:Je(n,{resolveItems:()=>t.options,resolveActiveIndex:()=>t.activeOptionIndex,resolveId:u=>u.id,resolveDisabled:u=>u.dataRef.current.disabled}),r=(a=n.trigger)!=null?a:2;return e.activeOptionIndex===o&&e.activationTrigger===r?e:{...e,...t,isTyping:!1,activeOptionIndex:o,activationTrigger:r,__demoMode:!1}},[4]:(e,n)=>{var i,s,l,a;if((i=e.dataRef.current)!=null&&i.virtual)return{...e,options:[...e.options,n.payload]};let t=n.payload,o=Ti(e,u=>(u.push(t),u));e.activeOptionIndex===null&&(l=(s=e.dataRef.current).isSelected)!=null&&l.call(s,n.payload.dataRef.current.value)&&(o.activeOptionIndex=o.options.indexOf(t));let r={...e,...o,activationTrigger:2};return(a=e.dataRef.current)!=null&&a.__demoMode&&e.dataRef.current.value===void 0&&(r.activeOptionIndex=0),r},[5]:(e,n)=>{var o;if((o=e.dataRef.current)!=null&&o.virtual)return{...e,options:e.options.filter(r=>r.id!==n.id)};let t=Ti(e,r=>{let i=r.findIndex(s=>s.id===n.id);return i!==-1&&r.splice(i,1),r});return{...e,...t,activationTrigger:2}},[6]:(e,n)=>e.defaultToFirstOption===n.value?e:{...e,defaultToFirstOption:n.value},[7]:(e,n)=>e.activationTrigger===n.trigger?e:{...e,activationTrigger:n.trigger},[8]:(e,n)=>{var o,r;if(e.virtual===null)return{...e,virtual:{options:n.options,disabled:(o=n.disabled)!=null?o:()=>!1}};if(e.virtual.options===n.options&&e.virtual.disabled===n.disabled)return e;let t=e.activeOptionIndex;if(e.activeOptionIndex!==null){let i=n.options.indexOf(e.virtual.options[e.activeOptionIndex]);i!==-1?t=i:t=null}return{...e,activeOptionIndex:t,virtual:{options:n.options,disabled:(r=n.disabled)!=null?r:()=>!1}}},[9]:(e,n)=>e.inputElement===n.element?e:{...e,inputElement:n.element},[10]:(e,n)=>e.buttonElement===n.element?e:{...e,buttonElement:n.element},[11]:(e,n)=>e.optionsElement===n.element?e:{...e,optionsElement:n.element}},Dn=class extends jt{constructor(){super(...arguments);Be(this,"actions",{onChange:t=>{let{onChange:o,compare:r,mode:i,value:s}=this.state.dataRef.current;return Y(i,{[0]:()=>o==null?void 0:o(t),[1]:()=>{let l=s.slice(),a=l.findIndex(u=>r(u,t));return a===-1?l.push(t):l.splice(a,1),o==null?void 0:o(l)}})},registerOption:(t,o)=>(this.send({type:4,payload:{id:t,dataRef:o}}),()=>{this.state.activeOptionIndex===this.state.dataRef.current.calculateIndex(o.current.value)&&this.send({type:6,value:!0}),this.send({type:5,id:t})}),goToOption:(t,o)=>(this.send({type:6,value:!1}),this.send({type:2,...t,trigger:o})),setIsTyping:t=>{this.send({type:3,isTyping:t})},closeCombobox:()=>{var t,o;this.send({type:1}),this.send({type:6,value:!1}),(o=(t=this.state.dataRef.current).onClose)==null||o.call(t)},openCombobox:()=>{this.send({type:0}),this.send({type:6,value:!0})},setActivationTrigger:t=>{this.send({type:7,trigger:t})},selectActiveOption:()=>{let t=this.selectors.activeOptionIndex(this.state);if(t!==null){if(this.actions.setIsTyping(!1),this.state.virtual)this.actions.onChange(this.state.virtual.options[t]);else{let{dataRef:o}=this.state.options[t];this.actions.onChange(o.current.value)}this.actions.goToOption({focus:4,idx:t})}},setInputElement:t=>{this.send({type:9,element:t})},setButtonElement:t=>{this.send({type:10,element:t})},setOptionsElement:t=>{this.send({type:11,element:t})}});Be(this,"selectors",{activeDescendantId:t=>{var r,i;let o=this.selectors.activeOptionIndex(t);if(o!==null)return t.virtual?(i=t.options.find(s=>!s.dataRef.current.disabled&&t.dataRef.current.compare(s.dataRef.current.value,t.virtual.options[o])))==null?void 0:i.id:(r=t.options[o])==null?void 0:r.id},activeOptionIndex:t=>{if(t.defaultToFirstOption&&t.activeOptionIndex===null&&(t.virtual?t.virtual.options.length>0:t.options.length>0)){if(t.virtual){let{options:r,disabled:i}=t.virtual,s=r.findIndex(l=>{var a;return!((a=i==null?void 0:i(l))!=null&&a)});if(s!==-1)return s}let o=t.options.findIndex(r=>!r.dataRef.current.disabled);if(o!==-1)return o}return t.activeOptionIndex},activeOption:t=>{var r,i;let o=this.selectors.activeOptionIndex(t);return o===null?null:t.virtual?t.virtual.options[o!=null?o:0]:(i=(r=t.options[o])==null?void 0:r.dataRef.current.value)!=null?i:null},isActive:(t,o,r)=>{var s;let i=this.selectors.activeOptionIndex(t);return i===null?!1:t.virtual?i===t.dataRef.current.calculateIndex(o):((s=t.options[i])==null?void 0:s.id)===r},shouldScrollIntoView:(t,o,r)=>!(t.virtual||t.__demoMode||t.comboboxState!==0||t.activationTrigger===0||!this.selectors.isActive(t,o,r))})}static new({virtual:t=null,__demoMode:o=!1}={}){var r;return new Dn({dataRef:{current:{}},comboboxState:o?0:1,isTyping:!1,options:[],virtual:t?{options:t.options,disabled:(r=t.disabled)!=null?r:()=>!1}:null,activeOptionIndex:null,activationTrigger:2,inputElement:null,buttonElement:null,optionsElement:null,__demoMode:o})}reduce(t,o){return Y(o.type,ff,t,o)}};var In=require("react");var bi=(0,In.createContext)(null);function Fn(e){let n=(0,In.useContext)(bi);if(n===null){let t=new Error(`<${e} /> is missing a parent <Combobox /> component.`);throw Error.captureStackTrace&&Error.captureStackTrace(t,gi),t}return n}function gi({virtual:e=null,__demoMode:n=!1}={}){return(0,In.useMemo)(()=>Dn.new({virtual:e,__demoMode:n}),[])}var lo=(0,q.createContext)(null);lo.displayName="ComboboxDataContext";function wn(e){let n=(0,q.useContext)(lo);if(n===null){let t=new Error(`<${e} /> is missing a parent <Combobox /> component.`);throw Error.captureStackTrace&&Error.captureStackTrace(t,wn),t}return n}var jl=(0,q.createContext)(null);function df(e){let n=Fn("VirtualProvider"),t=wn("VirtualProvider"),{options:o}=t.virtual,r=le(n,f=>f.optionsElement),[i,s]=(0,q.useMemo)(()=>{let f=r;if(!f)return[0,0];let m=window.getComputedStyle(f);return[parseFloat(m.paddingBlockStart||m.paddingTop),parseFloat(m.paddingBlockEnd||m.paddingBottom)]},[r]),l=As({enabled:o.length!==0,scrollPaddingStart:i,scrollPaddingEnd:s,count:o.length,estimateSize(){return 40},getScrollElement(){return n.state.optionsElement},overscan:12}),[a,u]=(0,q.useState)(0);W(()=>{u(f=>f+1)},[o]);let c=l.getVirtualItems(),d=le(n,f=>f.activationTrigger===0),p=le(n,n.selectors.activeOptionIndex);return c.length===0?null:q.default.createElement(jl.Provider,{value:l},q.default.createElement("div",{style:{position:"relative",width:"100%",height:`${l.getTotalSize()}px`},ref:f=>{f&&(d||p!==null&&o.length>p&&l.scrollToIndex(p))}},c.map(f=>{var m;return q.default.createElement(q.Fragment,{key:f.key},q.default.cloneElement((m=e.children)==null?void 0:m.call(e,{...e.slot,option:o[f.index]}),{key:`${a}-${f.key}`,"data-index":f.index,"aria-setsize":o.length,"aria-posinset":f.index+1,style:{position:"absolute",top:0,left:0,transform:`translateY(${f.start}px)`,overflowAnchor:"none"}}))})))}var pf=q.Fragment;function mf(e,n){let t=Te(),{value:o,defaultValue:r,onChange:i,form:s,name:l,by:a,invalid:u=!1,disabled:c=t||!1,onClose:d,__demoMode:p=!1,multiple:f=!1,immediate:m=!1,virtual:T=null,nullable:b,...g}=e,h=at(r),[y=f?[]:void 0,x]=lt(o,i,h),v=gi({virtual:T,__demoMode:p}),A=(0,q.useRef)({static:!1,hold:!1}),D=pn(a),F=E(U=>T?a===null?T.options.indexOf(U):T.options.findIndex(X=>D(X,U)):v.state.options.findIndex(X=>D(X.dataRef.current.value,U))),L=(0,q.useCallback)(U=>Y(C.mode,{[1]:()=>y.some(X=>D(X,U)),[0]:()=>D(y,U)}),[y]),S=le(v,U=>U.virtual),O=E(()=>d==null?void 0:d()),C=(0,q.useMemo)(()=>({__demoMode:p,immediate:m,optionsPropsRef:A,value:y,defaultValue:h,disabled:c,invalid:u,mode:f?1:0,virtual:T?S:null,onChange:x,isSelected:L,calculateIndex:F,compare:D,onClose:O}),[y,h,c,u,f,x,L,p,v,T,S,O]);W(()=>{var U;T&&v.send({type:8,options:T.options,disabled:(U=T.disabled)!=null?U:null})},[T,T==null?void 0:T.options,T==null?void 0:T.disabled]),W(()=>{v.state.dataRef.current=C},[C]);let[N,j,_,$]=le(v,U=>[U.comboboxState,U.buttonElement,U.inputElement,U.optionsElement]),G=N===0;dt(G,[j,_,$],()=>v.actions.closeCombobox());let ne=le(v,v.selectors.activeOptionIndex),J=le(v,v.selectors.activeOption),R=(0,q.useMemo)(()=>({open:N===0,disabled:c,invalid:u,activeIndex:ne,activeOption:J,value:y}),[C,c,y,u,J,N]),[P,w]=_e(),k=n===null?{}:{ref:n},z=(0,q.useCallback)(()=>{if(h!==void 0)return x==null?void 0:x(h)},[x,h]),I=H();return q.default.createElement(w,{value:P,props:{htmlFor:_==null?void 0:_.id},slot:{open:N===0,disabled:c}},q.default.createElement(Vt,null,q.default.createElement(lo.Provider,{value:C},q.default.createElement(bi.Provider,{value:v},q.default.createElement(Ye,{value:Y(N,{[0]:1,[1]:2})},l!=null&&q.default.createElement(ut,{disabled:c,data:y!=null?{[l]:y}:{},form:s,onReset:z}),I({ourProps:k,theirProps:g,slot:R,defaultTag:pf,name:"Combobox"}))))))}var Tf="input";function bf(e,n){var k,z;let t=Fn("Combobox.Input"),o=wn("Combobox.Input"),r=(0,Q.useId)(),i=De(),{id:s=i||`headlessui-combobox-input-${r}`,onChange:l,displayValue:a,disabled:u=o.disabled||!1,autoFocus:c=!1,type:d="text",...p}=e,[f]=le(t,I=>[I.inputElement]),m=(0,q.useRef)(null),T=K(m,n,Ut(),t.actions.setInputElement),b=Pe(f),[g,h]=le(t,I=>[I.comboboxState,I.isTyping]),y=Re(),x=E(()=>{t.actions.onChange(null),t.state.optionsElement&&(t.state.optionsElement.scrollTop=0),t.actions.goToOption({focus:5})}),v=(0,q.useMemo)(()=>{var I;return typeof a=="function"&&o.value!==void 0?(I=a(o.value))!=null?I:"":typeof o.value=="string"?o.value:""},[o.value,a]);qt(([I,U],[X,B])=>{if(t.state.isTyping)return;let ee=m.current;ee&&((B===0&&U===1||I!==X)&&(ee.value=I),requestAnimationFrame(()=>{if(t.state.isTyping||!ee||(b==null?void 0:b.activeElement)!==ee)return;let{selectionStart:ae,selectionEnd:it}=ee;Math.abs((it!=null?it:0)-(ae!=null?ae:0))===0&&ae===0&&ee.setSelectionRange(ee.value.length,ee.value.length)}))},[v,g,b,h]),qt(([I],[U])=>{if(I===0&&U===1){if(t.state.isTyping)return;let X=m.current;if(!X)return;let B=X.value,{selectionStart:ee,selectionEnd:ae,selectionDirection:it}=X;X.value="",X.value=B,it!==null?X.setSelectionRange(ee,ae,it):X.setSelectionRange(ee,ae)}},[g]);let A=(0,q.useRef)(!1),D=E(()=>{A.current=!0}),F=E(()=>{y.nextFrame(()=>{A.current=!1})}),L=E(I=>{switch(t.actions.setIsTyping(!0),I.key){case"Enter":if(t.state.comboboxState!==0||A.current)return;if(I.preventDefault(),I.stopPropagation(),t.selectors.activeOptionIndex(t.state)===null){t.actions.closeCombobox();return}t.actions.selectActiveOption(),o.mode===0&&t.actions.closeCombobox();break;case"ArrowDown":return I.preventDefault(),I.stopPropagation(),Y(t.state.comboboxState,{[0]:()=>t.actions.goToOption({focus:2}),[1]:()=>t.actions.openCombobox()});case"ArrowUp":return I.preventDefault(),I.stopPropagation(),Y(t.state.comboboxState,{[0]:()=>t.actions.goToOption({focus:1}),[1]:()=>{(0,on.flushSync)(()=>t.actions.openCombobox()),o.value||t.actions.goToOption({focus:3})}});case"Home":if(I.shiftKey)break;return I.preventDefault(),I.stopPropagation(),t.actions.goToOption({focus:0});case"PageUp":return I.preventDefault(),I.stopPropagation(),t.actions.goToOption({focus:0});case"End":if(I.shiftKey)break;return I.preventDefault(),I.stopPropagation(),t.actions.goToOption({focus:3});case"PageDown":return I.preventDefault(),I.stopPropagation(),t.actions.goToOption({focus:3});case"Escape":return t.state.comboboxState!==0?void 0:(I.preventDefault(),t.state.optionsElement&&!o.optionsPropsRef.current.static&&I.stopPropagation(),o.mode===0&&o.value===null&&x(),t.actions.closeCombobox());case"Tab":if(t.state.comboboxState!==0)return;o.mode===0&&t.state.activationTrigger!==1&&t.actions.selectActiveOption(),t.actions.closeCombobox();break}}),S=E(I=>{l==null||l(I),o.mode===0&&I.target.value===""&&x(),t.actions.openCombobox()}),O=E(I=>{var X,B,ee;let U=(X=I.relatedTarget)!=null?X:qe.find(ae=>ae!==I.currentTarget);if(!((B=t.state.optionsElement)!=null&&B.contains(U))&&!((ee=t.state.buttonElement)!=null&&ee.contains(U))&&t.state.comboboxState===0)return I.preventDefault(),o.mode===0&&o.value===null&&x(),t.actions.closeCombobox()}),C=E(I=>{var X,B,ee;let U=(X=I.relatedTarget)!=null?X:qe.find(ae=>ae!==I.currentTarget);(B=t.state.buttonElement)!=null&&B.contains(U)||(ee=t.state.optionsElement)!=null&&ee.contains(U)||o.disabled||o.immediate&&t.state.comboboxState!==0&&y.microTask(()=>{(0,on.flushSync)(()=>t.actions.openCombobox()),t.actions.setActivationTrigger(1)})}),N=Le(),j=we(),{isFocused:_,focusProps:$}=ce({autoFocus:c}),{isHovered:G,hoverProps:ne}=fe({isDisabled:u}),J=le(t,I=>I.optionsElement),R=(0,q.useMemo)(()=>({open:g===0,disabled:u,invalid:o.invalid,hover:G,focus:_,autofocus:c}),[o,G,_,c,u,o.invalid]),P=se({ref:T,id:s,role:"combobox",type:d,"aria-controls":J==null?void 0:J.id,"aria-expanded":g===0,"aria-activedescendant":le(t,t.selectors.activeDescendantId),"aria-labelledby":N,"aria-describedby":j,"aria-autocomplete":"list",defaultValue:(z=(k=e.defaultValue)!=null?k:o.defaultValue!==void 0?a==null?void 0:a(o.defaultValue):null)!=null?z:o.defaultValue,disabled:u||void 0,autoFocus:c,onCompositionStart:D,onCompositionEnd:F,onKeyDown:L,onChange:S,onFocus:C,onBlur:O},$,ne);return H()({ourProps:P,theirProps:p,slot:R,defaultTag:Tf,name:"Combobox.Input"})}var gf="button";function yf(e,n){let t=Fn("Combobox.Button"),o=wn("Combobox.Button"),[r,i]=(0,q.useState)(null),s=K(n,i,t.actions.setButtonElement),l=(0,Q.useId)(),{id:a=`headlessui-combobox-button-${l}`,disabled:u=o.disabled||!1,autoFocus:c=!1,...d}=e,p=le(t,C=>C.inputElement),f=Kr(p),m=E(C=>{switch(C.key){case" ":case"Enter":C.preventDefault(),C.stopPropagation(),t.state.comboboxState===1&&(0,on.flushSync)(()=>t.actions.openCombobox()),f();return;case"ArrowDown":C.preventDefault(),C.stopPropagation(),t.state.comboboxState===1&&((0,on.flushSync)(()=>t.actions.openCombobox()),t.state.dataRef.current.value||t.actions.goToOption({focus:0})),f();return;case"ArrowUp":C.preventDefault(),C.stopPropagation(),t.state.comboboxState===1&&((0,on.flushSync)(()=>t.actions.openCombobox()),t.state.dataRef.current.value||t.actions.goToOption({focus:3})),f();return;case"Escape":if(t.state.comboboxState!==0)return;C.preventDefault(),t.state.optionsElement&&!o.optionsPropsRef.current.static&&C.stopPropagation(),(0,on.flushSync)(()=>t.actions.closeCombobox()),f();return;default:return}}),T=E(C=>{C.preventDefault(),!Ie(C.currentTarget)&&(C.button===0&&(t.state.comboboxState===0?t.actions.closeCombobox():t.actions.openCombobox()),f())}),b=Le([a]),{isFocusVisible:g,focusProps:h}=ce({autoFocus:c}),{isHovered:y,hoverProps:x}=fe({isDisabled:u}),{pressed:v,pressProps:A}=Se({disabled:u}),[D,F]=le(t,C=>[C.comboboxState,C.optionsElement]),L=(0,q.useMemo)(()=>({open:D===0,active:v||D===0,disabled:u,invalid:o.invalid,value:o.value,hover:y,focus:g}),[o,y,g,v,u,D]),S=se({ref:s,id:a,type:$e(e,r),tabIndex:-1,"aria-haspopup":"listbox","aria-controls":F==null?void 0:F.id,"aria-expanded":D===0,"aria-labelledby":b,disabled:u||void 0,autoFocus:c,onMouseDown:T,onKeyDown:m},h,x,A);return H()({ourProps:S,theirProps:d,slot:L,defaultTag:gf,name:"Combobox.Button"})}var hf="div",vf=3;function Ef(e,n){var X,B,ee;let t=(0,Q.useId)(),{id:o=`headlessui-combobox-options-${t}`,hold:r=!1,anchor:i,portal:s=!1,modal:l=!0,transition:a=!1,...u}=e,c=Fn("Combobox.Options"),d=wn("Combobox.Options"),p=Bt(i);p&&(s=!0);let[f,m]=Wt(p),[T,b]=(0,q.useState)(null),g=Gt(),h=K(n,p?f:null,c.actions.setOptionsElement,b),[y,x,v,A,D]=le(c,ae=>[ae.comboboxState,ae.inputElement,ae.buttonElement,ae.optionsElement,ae.activationTrigger]),F=Pe(x||v),L=Pe(A),S=Me(),[O,C]=Ve(a,T,S!==null?(S&1)===1:y===0);ft(O,x,c.actions.closeCombobox);let N=d.__demoMode?!1:l&&y===0;pt(N,L);let j=d.__demoMode?!1:l&&y===0;_t(j,{allowed:(0,q.useCallback)(()=>[x,v,A],[x,v,A])}),W(()=>{var ae;d.optionsPropsRef.current.static=(ae=e.static)!=null?ae:!1},[d.optionsPropsRef,e.static]),W(()=>{d.optionsPropsRef.current.hold=r},[d.optionsPropsRef,r]),Bo(y===0,{container:A,accept(ae){return ae.getAttribute("role")==="option"?NodeFilter.FILTER_REJECT:ae.hasAttribute("role")?NodeFilter.FILTER_SKIP:NodeFilter.FILTER_ACCEPT},walk(ae){ae.setAttribute("role","none")}});let _=Le([v==null?void 0:v.id]),$=(0,q.useMemo)(()=>({open:y===0,option:void 0}),[y]),G=E(()=>{c.actions.setActivationTrigger(0)}),ne=E(ae=>{ae.preventDefault(),c.actions.setActivationTrigger(0)}),J=se(p?g():{},{"aria-labelledby":_,role:"listbox","aria-multiselectable":d.mode===1?!0:void 0,id:o,ref:h,style:{...u.style,...m,"--input-width":Pt(x,!0).width,"--button-width":Pt(v,!0).width},onWheel:D===0?void 0:G,onMouseDown:ne,...We(C)}),R=O&&y===1,P=Sn(R,(X=d.virtual)==null?void 0:X.options),w=Sn(R,d.value),k=E(ae=>d.compare(w,ae)),z=(0,q.useMemo)(()=>{if(!d.virtual)return d;if(P===void 0)throw new Error("Missing `options` in virtual mode");return P!==d.virtual.options?{...d,virtual:{...d.virtual,options:P}}:d},[d,P,(B=d.virtual)==null?void 0:B.options]);d.virtual&&Object.assign(u,{children:q.default.createElement(lo.Provider,{value:z},q.default.createElement(df,{slot:$},u.children))});let I=H(),U=(0,q.useMemo)(()=>d.mode===1?d:{...d,isSelected:k},[d,k]);return q.default.createElement(Qe,{enabled:s?e.static||O:!1,ownerDocument:F},q.default.createElement(lo.Provider,{value:U},I({ourProps:J,theirProps:{...u,children:q.default.createElement(_l,{freeze:R},typeof u.children=="function"?(ee=u.children)==null?void 0:ee.call(u,$):u.children)},slot:$,defaultTag:hf,features:vf,visible:O,name:"Combobox.Options"})))}var xf="div";function Pf(e,n){var N,j,_;let t=wn("Combobox.Option"),o=Fn("Combobox.Option"),r=(0,Q.useId)(),{id:i=`headlessui-combobox-option-${r}`,value:s,disabled:l=(_=(j=(N=t.virtual)==null?void 0:N.disabled)==null?void 0:j.call(N,s))!=null?_:!1,order:a=null,...u}=e,[c]=le(o,$=>[$.inputElement]),d=Kr(c),p=le(o,(0,q.useCallback)($=>o.selectors.isActive($,s,i),[s,i])),f=t.isSelected(s),m=(0,q.useRef)(null),T=pe({disabled:l,value:s,domRef:m,order:a}),b=(0,q.useContext)(jl),g=K(n,m,b?b.measureElement:null),h=E(()=>{o.actions.setIsTyping(!1),o.actions.onChange(s)});W(()=>o.actions.registerOption(i,T),[T,i]);let y=le(o,(0,q.useCallback)($=>o.selectors.shouldScrollIntoView($,s,i),[s,i]));W(()=>{if(y)return he().requestAnimationFrame(()=>{var $,G;(G=($=m.current)==null?void 0:$.scrollIntoView)==null||G.call($,{block:"nearest"})})},[y,m]);let x=E($=>{$.preventDefault(),$.button===0&&(l||(h(),$o()||requestAnimationFrame(()=>d()),t.mode===0&&o.actions.closeCombobox()))}),v=E(()=>{if(l)return o.actions.goToOption({focus:5});let $=t.calculateIndex(s);o.actions.goToOption({focus:4,idx:$})}),A=gn(),D=E($=>A.update($)),F=E($=>{if(!A.wasMoved($)||l||p)return;let G=t.calculateIndex(s);o.actions.goToOption({focus:4,idx:G},0)}),L=E($=>{A.wasMoved($)&&(l||p&&(t.optionsPropsRef.current.hold||o.actions.goToOption({focus:5})))}),S=(0,q.useMemo)(()=>({active:p,focus:p,selected:f,disabled:l}),[p,f,l]),O={id:i,ref:g,role:"option",tabIndex:l===!0?void 0:-1,"aria-disabled":l===!0?!0:void 0,"aria-selected":f,disabled:void 0,onMouseDown:x,onFocus:v,onPointerEnter:D,onMouseEnter:D,onPointerMove:F,onMouseMove:F,onPointerLeave:L,onMouseLeave:L};return H()({ourProps:O,theirProps:u,slot:S,defaultTag:xf,name:"Combobox.Option"})}var Rf=M(mf),Kl=M(yf),zl=M(bf),Xl=Xe,Yl=M(Ef),ql=M(Pf),Sf=Object.assign(Rf,{Input:zl,Button:Kl,Label:Xl,Options:Yl,Option:ql});var nr=require("react");var Cf=nr.Fragment;function Af(e,n){let{...t}=e,o=!1,{isFocusVisible:r,focusProps:i}=ce(),{isHovered:s,hoverProps:l}=fe({isDisabled:o}),{pressed:a,pressProps:u}=Se({disabled:o}),c=se({ref:n},i,l,u),d=(0,nr.useMemo)(()=>({hover:s,focus:r,active:a}),[s,r,a]);return H()({ourProps:c,theirProps:t,slot:d,defaultTag:Cf,name:"DataInteractive"})}var Of=M(Af);var re=ie(require("react"),1);function Jl(e,n=typeof document!="undefined"?document.defaultView:null,t){let o=nt(e,"escape");Ht(n,"keydown",r=>{o&&(r.defaultPrevented||r.key==="Escape"&&t(r))})}var yi=require("react");function Ql(){var o;let[e]=(0,yi.useState)(()=>typeof window!="undefined"&&typeof window.matchMedia=="function"?window.matchMedia("(pointer: coarse)"):null),[n,t]=(0,yi.useState)((o=e==null?void 0:e.matches)!=null?o:!1);return W(()=>{if(!e)return;function r(i){t(i.matches)}return e.addEventListener("change",r),()=>e.removeEventListener("change",r)},[e]),n}var Dt=ie(require("react"),1);function or({defaultContainers:e=[],portals:n,mainTreeNode:t}={}){let o=Pe(t),r=E(()=>{var s,l;let i=[];for(let a of e)a!==null&&(a instanceof HTMLElement?i.push(a):"current"in a&&a.current instanceof HTMLElement&&i.push(a.current));if(n!=null&&n.current)for(let a of n.current)i.push(a);for(let a of(s=o==null?void 0:o.querySelectorAll("html > *, body > *"))!=null?s:[])a!==document.body&&a!==document.head&&a instanceof HTMLElement&&a.id!=="headlessui-portal-root"&&(t&&(a.contains(t)||a.contains((l=t==null?void 0:t.getRootNode())==null?void 0:l.host))||i.some(u=>a.contains(u))||i.push(a));return i});return{resolveContainers:r,contains:E(i=>r().some(s=>s.contains(i)))}}var Zl=(0,Dt.createContext)(null);function _n({children:e,node:n}){let[t,o]=(0,Dt.useState)(null),r=ao(n!=null?n:t);return Dt.default.createElement(Zl.Provider,{value:r},e,r===null&&Dt.default.createElement(Fe,{features:4,ref:i=>{var s,l;if(i){for(let a of(l=(s=xe(i))==null?void 0:s.querySelectorAll("html > *, body > *"))!=null?l:[])if(a!==document.body&&a!==document.head&&a instanceof HTMLElement&&a!=null&&a.contains(i)){o(a);break}}}}))}function ao(e=null){var n;return(n=(0,Dt.useContext)(Zl))!=null?n:e}var yt=ie(require("react"),1);var ea=require("react");function sn(){let e=(0,ea.useRef)(!1);return W(()=>(e.current=!0,()=>{e.current=!1}),[]),e}var ta=require("react");function uo(){let e=(0,ta.useRef)(0);return No(!0,"keydown",t=>{t.key==="Tab"&&(e.current=t.shiftKey?1:0)},!0),e}function na(e){if(!e)return new Set;if(typeof e=="function")return new Set(e());let n=new Set;for(let t of e.current)t.current instanceof HTMLElement&&n.add(t.current);return n}var Df="div",rr=(s=>(s[s.None=0]="None",s[s.InitialFocus=1]="InitialFocus",s[s.TabLock=2]="TabLock",s[s.FocusLock=4]="FocusLock",s[s.RestoreFocus=8]="RestoreFocus",s[s.AutoFocus=16]="AutoFocus",s))(rr||{});function If(e,n){let t=(0,yt.useRef)(null),o=K(t,n),{initialFocus:r,initialFocusFallback:i,containers:s,features:l=15,...a}=e;Lt()||(l=0);let u=Pe(t);wf(l,{ownerDocument:u});let c=_f(l,{ownerDocument:u,container:t,initialFocus:r,initialFocusFallback:i});Hf(l,{ownerDocument:u,container:t,containers:s,previousActiveElement:c});let d=uo(),p=E(h=>{let y=t.current;if(!y)return;(v=>v())(()=>{Y(d.current,{[0]:()=>{ye(y,1,{skipElements:[h.relatedTarget,i]})},[1]:()=>{ye(y,8,{skipElements:[h.relatedTarget,i]})}})})}),f=nt(!!(l&2),"focus-trap#tab-lock"),m=Re(),T=(0,yt.useRef)(!1),b={ref:o,onKeyDown(h){h.key=="Tab"&&(T.current=!0,m.requestAnimationFrame(()=>{T.current=!1}))},onBlur(h){if(!(l&4))return;let y=na(s);t.current instanceof HTMLElement&&y.add(t.current);let x=h.relatedTarget;x instanceof HTMLElement&&x.dataset.headlessuiFocusGuard!=="true"&&(oa(y,x)||(T.current?ye(t.current,Y(d.current,{[0]:()=>4,[1]:()=>2})|16,{relativeTo:h.target}):h.target instanceof HTMLElement&&ot(h.target)))}},g=H();return yt.default.createElement(yt.default.Fragment,null,f&&yt.default.createElement(Fe,{as:"button",type:"button","data-headlessui-focus-guard":!0,onFocus:p,features:2}),g({ourProps:b,theirProps:a,defaultTag:Df,name:"FocusTrap"}),f&&yt.default.createElement(Fe,{as:"button",type:"button","data-headlessui-focus-guard":!0,onFocus:p,features:2}))}var Ff=M(If),vi=Object.assign(Ff,{features:rr});function Mf(e=!0){let n=(0,yt.useRef)(qe.slice());return qt(([t],[o])=>{o===!0&&t===!1&&Et(()=>{n.current.splice(0)}),o===!1&&t===!0&&(n.current=qe.slice())},[e,qe,n]),E(()=>{var t;return(t=n.current.find(o=>o!=null&&o.isConnected))!=null?t:null})}function wf(e,{ownerDocument:n}){let t=!!(e&8),o=Mf(t);qt(()=>{t||(n==null?void 0:n.activeElement)===(n==null?void 0:n.body)&&ot(o())},[t]),er(()=>{t&&ot(o())})}function _f(e,{ownerDocument:n,container:t,initialFocus:o,initialFocusFallback:r}){let i=(0,yt.useRef)(null),s=nt(!!(e&1),"focus-trap#initial-focus"),l=sn();return qt(()=>{if(e===0)return;if(!s){r!=null&&r.current&&ot(r.current);return}let a=t.current;a&&Et(()=>{if(!l.current)return;let u=n==null?void 0:n.activeElement;if(o!=null&&o.current){if((o==null?void 0:o.current)===u){i.current=u;return}}else if(a.contains(u)){i.current=u;return}if(o!=null&&o.current)ot(o.current);else{if(e&16){if(ye(a,65)!==0)return}else if(ye(a,1)!==0)return;if(r!=null&&r.current&&(ot(r.current),(n==null?void 0:n.activeElement)===r.current))return;console.warn("There are no focusable elements inside the <FocusTrap />")}i.current=n==null?void 0:n.activeElement})},[r,s,e]),i}function Hf(e,{ownerDocument:n,container:t,containers:o,previousActiveElement:r}){let i=sn(),s=!!(e&4);Ht(n==null?void 0:n.defaultView,"focus",l=>{if(!s||!i.current)return;let a=na(o);t.current instanceof HTMLElement&&a.add(t.current);let u=r.current;if(!u)return;let c=l.target;c&&c instanceof HTMLElement?oa(a,c)?(r.current=c,ot(c)):(l.preventDefault(),l.stopPropagation(),ot(u)):ot(r.current)},!0)}function oa(e,n){for(let t of e)if(t.contains(n))return!0;return!1}var te=ie(require("react"),1);function ra(e){var n;return!!(e.enter||e.enterFrom||e.enterTo||e.leave||e.leaveFrom||e.leaveTo)||((n=e.as)!=null?n:sa)!==te.Fragment||te.default.Children.count(e.children)===1}var ir=(0,te.createContext)(null);ir.displayName="TransitionContext";function kf(){let e=(0,te.useContext)(ir);if(e===null)throw new Error("A <Transition.Child /> is used but it is missing a parent <Transition /> or <Transition.Root />.");return e}function $f(){let e=(0,te.useContext)(sr);if(e===null)throw new Error("A <Transition.Child /> is used but it is missing a parent <Transition /> or <Transition.Root />.");return e}var sr=(0,te.createContext)(null);sr.displayName="NestingContext";function lr(e){return"children"in e?lr(e.children):e.current.filter(({el:n})=>n.current!==null).filter(({state:n})=>n==="visible").length>0}function ia(e,n){let t=pe(e),o=(0,te.useRef)([]),r=sn(),i=Re(),s=E((f,m=1)=>{let T=o.current.findIndex(({el:b})=>b===f);T!==-1&&(Y(m,{[0](){o.current.splice(T,1)},[1](){o.current[T].state="hidden"}}),i.microTask(()=>{var b;!lr(o)&&r.current&&((b=t.current)==null||b.call(t))}))}),l=E(f=>{let m=o.current.find(({el:T})=>T===f);return m?m.state!=="visible"&&(m.state="visible"):o.current.push({el:f,state:"visible"}),()=>s(f,0)}),a=(0,te.useRef)([]),u=(0,te.useRef)(Promise.resolve()),c=(0,te.useRef)({enter:[],leave:[]}),d=E((f,m,T)=>{a.current.splice(0),n&&(n.chains.current[m]=n.chains.current[m].filter(([b])=>b!==f)),n==null||n.chains.current[m].push([f,new Promise(b=>{a.current.push(b)})]),n==null||n.chains.current[m].push([f,new Promise(b=>{Promise.all(c.current[m].map(([g,h])=>h)).then(()=>b())})]),m==="enter"?u.current=u.current.then(()=>n==null?void 0:n.wait.current).then(()=>T(m)):T(m)}),p=E((f,m,T)=>{Promise.all(c.current[m].splice(0).map(([b,g])=>g)).then(()=>{var b;(b=a.current.shift())==null||b()}).then(()=>T(m))});return(0,te.useMemo)(()=>({children:o,register:l,unregister:s,onStart:d,onStop:p,wait:u,chains:c}),[l,s,o,d,p,c,u])}var sa=te.Fragment,la=1;function Nf(e,n){var I,U;let{transition:t=!0,beforeEnter:o,afterEnter:r,beforeLeave:i,afterLeave:s,enter:l,enterFrom:a,enterTo:u,entered:c,leave:d,leaveFrom:p,leaveTo:f,...m}=e,[T,b]=(0,te.useState)(null),g=(0,te.useRef)(null),h=ra(e),y=K(...h?[g,n,b]:n===null?[]:[n]),x=(I=m.unmount)==null||I?0:1,{show:v,appear:A,initial:D}=kf(),[F,L]=(0,te.useState)(v?"visible":"hidden"),S=$f(),{register:O,unregister:C}=S;W(()=>O(g),[O,g]),W(()=>{if(x===1&&g.current){if(v&&F!=="visible"){L("visible");return}return Y(F,{["hidden"]:()=>C(g),["visible"]:()=>O(g)})}},[F,g,O,C,v,x]);let N=Lt();W(()=>{if(h&&N&&F==="visible"&&g.current===null)throw new Error("Did you forget to passthrough the `ref` to the actual DOM node?")},[g,F,N,h]);let j=D&&!A,_=A&&v&&D,$=(0,te.useRef)(!1),G=ia(()=>{$.current||(L("hidden"),C(g))},S),ne=E(X=>{$.current=!0;let B=X?"enter":"leave";G.onStart(g,B,ee=>{ee==="enter"?o==null||o():ee==="leave"&&(i==null||i())})}),J=E(X=>{let B=X?"enter":"leave";$.current=!1,G.onStop(g,B,ee=>{ee==="enter"?r==null||r():ee==="leave"&&(s==null||s())}),B==="leave"&&!lr(G)&&(L("hidden"),C(g))});(0,te.useEffect)(()=>{h&&t||(ne(v),J(v))},[v,h,t]);let R=(()=>!(!t||!h||!N||j))(),[,P]=Ve(R,T,v,{start:ne,end:J}),w=st({ref:y,className:((U=Kn(m.className,_&&l,_&&a,P.enter&&l,P.enter&&P.closed&&a,P.enter&&!P.closed&&u,P.leave&&d,P.leave&&!P.closed&&p,P.leave&&P.closed&&f,!P.transition&&v&&c))==null?void 0:U.trim())||void 0,...We(P)}),k=0;F==="visible"&&(k|=1),F==="hidden"&&(k|=2),v&&F==="hidden"&&(k|=8),!v&&F==="visible"&&(k|=4);let z=H();return te.default.createElement(sr.Provider,{value:G},te.default.createElement(Ye,{value:k},z({ourProps:w,theirProps:m,defaultTag:sa,features:la,visible:F==="visible",name:"Transition.Child"})))}function Bf(e,n){let{show:t,appear:o=!1,unmount:r=!0,...i}=e,s=(0,te.useRef)(null),l=ra(e),a=K(...l?[s,n]:n===null?[]:[n]);Lt();let u=Me();if(t===void 0&&u!==null&&(t=(u&1)===1),t===void 0)throw new Error("A <Transition /> is used but it is missing a `show={true | false}` prop.");let[c,d]=(0,te.useState)(t?"visible":"hidden"),p=ia(()=>{t||d("hidden")}),[f,m]=(0,te.useState)(!0),T=(0,te.useRef)([t]);W(()=>{f!==!1&&T.current[T.current.length-1]!==t&&(T.current.push(t),m(!1))},[T,t]);let b=(0,te.useMemo)(()=>({show:t,appear:o,initial:f}),[t,o,f]);W(()=>{t?d("visible"):!lr(p)&&s.current!==null&&d("hidden")},[t,p]);let g={unmount:r},h=E(()=>{var v;f&&m(!1),(v=e.beforeEnter)==null||v.call(e)}),y=E(()=>{var v;f&&m(!1),(v=e.beforeLeave)==null||v.call(e)}),x=H();return te.default.createElement(sr.Provider,{value:p},te.default.createElement(ir.Provider,{value:b},x({ourProps:{...g,as:te.Fragment,children:te.default.createElement(aa,{ref:a,...g,...i,beforeEnter:h,beforeLeave:y})},theirProps:{},defaultTag:te.Fragment,features:la,visible:c==="visible",name:"Transition"})))}function Uf(e,n){let t=(0,te.useContext)(ir)!==null,o=Me()!==null;return te.default.createElement(te.default.Fragment,null,!t&&o?te.default.createElement(Ei,{ref:n,...e}):te.default.createElement(aa,{ref:n,...e}))}var Ei=M(Bf),aa=M(Nf),fo=M(Uf),xi=Object.assign(Ei,{Child:fo,Root:Ei});var Gf={[0](e,n){return e.titleId===n.id?e:{...e,titleId:n.id}}},Pi=(0,re.createContext)(null);Pi.displayName="DialogContext";function ar(e){let n=(0,re.useContext)(Pi);if(n===null){let t=new Error(`<${e} /> is missing a parent <Dialog /> component.`);throw Error.captureStackTrace&&Error.captureStackTrace(t,ar),t}return n}function Wf(e,n){return Y(n.type,Gf,e,n)}var ua=M(function(n,t){let o=(0,Q.useId)(),{id:r=`headlessui-dialog-${o}`,open:i,onClose:s,initialFocus:l,role:a="dialog",autoFocus:u=!0,__demoMode:c=!1,unmount:d=!1,...p}=n,f=(0,re.useRef)(!1);a=function(){return a==="dialog"||a==="alertdialog"?a:(f.current||(f.current=!0,console.warn(`Invalid role [${a}] passed to <Dialog />. Only \`dialog\` and and \`alertdialog\` are supported. Using \`dialog\` instead.`)),"dialog")}();let m=Me();i===void 0&&m!==null&&(i=(m&1)===1);let T=(0,re.useRef)(null),b=K(T,t),g=Pe(T),h=i?0:1,[y,x]=(0,re.useReducer)(Wf,{titleId:null,descriptionId:null,panelRef:(0,re.createRef)()}),v=E(()=>s(!1)),A=E(I=>x({type:0,id:I})),F=Lt()?h===0:!1,[L,S]=tr(),O={get current(){var I;return(I=y.panelRef.current)!=null?I:T.current}},C=ao(),{resolveContainers:N}=or({mainTreeNode:C,portals:L,defaultContainers:[O]}),j=m!==null?(m&4)===4:!1;_t(c||j?!1:F,{allowed:E(()=>{var I,U;return[(U=(I=T.current)==null?void 0:I.closest("[data-headlessui-portal]"))!=null?U:null]}),disallowed:E(()=>{var I;return[(I=C==null?void 0:C.closest("body > *:not(#headlessui-portal-root)"))!=null?I:null]})}),dt(F,N,I=>{I.preventDefault(),v()}),Jl(F,g==null?void 0:g.defaultView,I=>{I.preventDefault(),I.stopPropagation(),document.activeElement&&"blur"in document.activeElement&&typeof document.activeElement.blur=="function"&&document.activeElement.blur(),v()}),pt(c||j?!1:F,g,N),ft(F,T,v);let[G,ne]=tt(),J=(0,re.useMemo)(()=>[{dialogState:h,close:v,setTitleId:A,unmount:d},y],[h,y,v,A,d]),R=(0,re.useMemo)(()=>({open:h===0}),[h]),P={ref:b,id:r,role:a,tabIndex:-1,"aria-modal":c?void 0:h===0?!0:void 0,"aria-labelledby":y.titleId,"aria-describedby":G,unmount:d},w=!Ql(),k=0;F&&!c&&(k|=8,k|=2,u&&(k|=16),w&&(k|=1));let z=H();return re.default.createElement(Cn,null,re.default.createElement(di,{force:!0},re.default.createElement(Qe,null,re.default.createElement(Pi.Provider,{value:J},re.default.createElement(mi,{target:T},re.default.createElement(di,{force:!1},re.default.createElement(ne,{slot:R},re.default.createElement(S,null,re.default.createElement(vi,{initialFocus:l,initialFocusFallback:T,containers:N,features:k},re.default.createElement(dn,{value:v},z({ourProps:P,theirProps:p,slot:R,defaultTag:Vf,features:jf,visible:h===0,name:"Dialog"})))))))))))}),Vf="div",jf=3;function Kf(e,n){let{transition:t=!1,open:o,...r}=e,i=Me(),s=e.hasOwnProperty("open")||i!==null,l=e.hasOwnProperty("onClose");if(!s&&!l)throw new Error("You have to provide an `open` and an `onClose` prop to the `Dialog` component.");if(!s)throw new Error("You provided an `onClose` prop to the `Dialog`, but forgot an `open` prop.");if(!l)throw new Error("You provided an `open` prop to the `Dialog`, but forgot an `onClose` prop.");if(!i&&typeof e.open!="boolean")throw new Error(`You provided an \`open\` prop to the \`Dialog\`, but the value is not a boolean. Received: ${e.open}`);if(typeof e.onClose!="function")throw new Error(`You provided an \`onClose\` prop to the \`Dialog\`, but the value is not a function. Received: ${e.onClose}`);return(o!==void 0||t)&&!r.static?re.default.createElement(_n,null,re.default.createElement(xi,{show:o,transition:t,unmount:r.unmount},re.default.createElement(ua,{ref:n,...r}))):re.default.createElement(_n,null,re.default.createElement(ua,{ref:n,open:o,...r}))}var zf="div";function Xf(e,n){let t=(0,Q.useId)(),{id:o=`headlessui-dialog-panel-${t}`,transition:r=!1,...i}=e,[{dialogState:s,unmount:l},a]=ar("Dialog.Panel"),u=K(n,a.panelRef),c=(0,re.useMemo)(()=>({open:s===0}),[s]),d=E(b=>{b.stopPropagation()}),p={ref:u,id:o,onClick:d},f=r?fo:re.Fragment,m=r?{unmount:l}:{},T=H();return re.default.createElement(f,{...m},T({ourProps:p,theirProps:i,slot:c,defaultTag:zf,name:"Dialog.Panel"}))}var Yf="div";function qf(e,n){let{transition:t=!1,...o}=e,[{dialogState:r,unmount:i}]=ar("Dialog.Backdrop"),s=(0,re.useMemo)(()=>({open:r===0}),[r]),l={ref:n,"aria-hidden":!0},a=t?fo:re.Fragment,u=t?{unmount:i}:{},c=H();return re.default.createElement(a,{...u},c({ourProps:l,theirProps:o,slot:s,defaultTag:Yf,name:"Dialog.Backdrop"}))}var Jf="h2";function Qf(e,n){let t=(0,Q.useId)(),{id:o=`headlessui-dialog-title-${t}`,...r}=e,[{dialogState:i,setTitleId:s}]=ar("Dialog.Title"),l=K(n);(0,re.useEffect)(()=>(s(o),()=>s(null)),[o,s]);let a=(0,re.useMemo)(()=>({open:i===0}),[i]),u={ref:l,id:o};return H()({ourProps:u,theirProps:r,slot:a,defaultTag:Jf,name:"Dialog.Title"})}var Zf=M(Kf),ca=M(Xf),ed=M(qf),fa=M(Qf),td=xt,nd=Object.assign(Zf,{Panel:ca,Title:fa,Description:xt});var ue=ie(require("react"),1);var pa=ie(require("react"),1),da,ma=(da=pa.default.startTransition)!=null?da:function(n){n()};var od={[0]:e=>({...e,disclosureState:Y(e.disclosureState,{[0]:1,[1]:0})}),[1]:e=>e.disclosureState===1?e:{...e,disclosureState:1},[2](e,n){return e.buttonId===n.buttonId?e:{...e,buttonId:n.buttonId}},[3](e,n){return e.panelId===n.panelId?e:{...e,panelId:n.panelId}},[4](e,n){return e.buttonElement===n.element?e:{...e,buttonElement:n.element}},[5](e,n){return e.panelElement===n.element?e:{...e,panelElement:n.element}}},Ri=(0,ue.createContext)(null);Ri.displayName="DisclosureContext";function Si(e){let n=(0,ue.useContext)(Ri);if(n===null){let t=new Error(`<${e} /> is missing a parent <Disclosure /> component.`);throw Error.captureStackTrace&&Error.captureStackTrace(t,Si),t}return n}var Ci=(0,ue.createContext)(null);Ci.displayName="DisclosureAPIContext";function Ta(e){let n=(0,ue.useContext)(Ci);if(n===null){let t=new Error(`<${e} /> is missing a parent <Disclosure /> component.`);throw Error.captureStackTrace&&Error.captureStackTrace(t,Ta),t}return n}var Ai=(0,ue.createContext)(null);Ai.displayName="DisclosurePanelContext";function rd(){return(0,ue.useContext)(Ai)}function id(e,n){return Y(n.type,od,e,n)}var sd=ue.Fragment;function ld(e,n){let{defaultOpen:t=!1,...o}=e,r=(0,ue.useRef)(null),i=K(n,cn(T=>{r.current=T},e.as===void 0||e.as===ue.Fragment)),s=(0,ue.useReducer)(id,{disclosureState:t?0:1,buttonElement:null,panelElement:null,buttonId:null,panelId:null}),[{disclosureState:l,buttonId:a},u]=s,c=E(T=>{u({type:1});let b=xe(r);if(!b||!a)return;let g=(()=>T?T instanceof HTMLElement?T:T.current instanceof HTMLElement?T.current:b.getElementById(a):b.getElementById(a))();g==null||g.focus()}),d=(0,ue.useMemo)(()=>({close:c}),[c]),p=(0,ue.useMemo)(()=>({open:l===0,close:c}),[l,c]),f={ref:i},m=H();return ue.default.createElement(Ri.Provider,{value:s},ue.default.createElement(Ci.Provider,{value:d},ue.default.createElement(dn,{value:c},ue.default.createElement(Ye,{value:Y(l,{[0]:1,[1]:2})},m({ourProps:f,theirProps:o,slot:p,defaultTag:sd,name:"Disclosure"})))))}var ad="button";function ud(e,n){let t=(0,Q.useId)(),{id:o=`headlessui-disclosure-button-${t}`,disabled:r=!1,autoFocus:i=!1,...s}=e,[l,a]=Si("Disclosure.Button"),u=rd(),c=u===null?!1:u===l.panelId,d=(0,ue.useRef)(null),p=K(d,n,E(S=>{if(!c)return a({type:4,element:S})}));(0,ue.useEffect)(()=>{if(!c)return a({type:2,buttonId:o}),()=>{a({type:2,buttonId:null})}},[o,a,c]);let f=E(S=>{var O;if(c){if(l.disclosureState===1)return;switch(S.key){case" ":case"Enter":S.preventDefault(),S.stopPropagation(),a({type:0}),(O=l.buttonElement)==null||O.focus();break}}else switch(S.key){case" ":case"Enter":S.preventDefault(),S.stopPropagation(),a({type:0});break}}),m=E(S=>{switch(S.key){case" ":S.preventDefault();break}}),T=E(S=>{var O;Ie(S.currentTarget)||r||(c?(a({type:0}),(O=l.buttonElement)==null||O.focus()):a({type:0}))}),{isFocusVisible:b,focusProps:g}=ce({autoFocus:i}),{isHovered:h,hoverProps:y}=fe({isDisabled:r}),{pressed:x,pressProps:v}=Se({disabled:r}),A=(0,ue.useMemo)(()=>({open:l.disclosureState===0,hover:h,active:x,disabled:r,focus:b,autofocus:i}),[l,h,x,b,r,i]),D=$e(e,l.buttonElement),F=c?se({ref:p,type:D,disabled:r||void 0,autoFocus:i,onKeyDown:f,onClick:T},g,y,v):se({ref:p,id:o,type:D,"aria-expanded":l.disclosureState===0,"aria-controls":l.panelElement?l.panelId:void 0,disabled:r||void 0,autoFocus:i,onKeyDown:f,onKeyUp:m,onClick:T},g,y,v);return H()({ourProps:F,theirProps:s,slot:A,defaultTag:ad,name:"Disclosure.Button"})}var cd="div",fd=3;function dd(e,n){let t=(0,Q.useId)(),{id:o=`headlessui-disclosure-panel-${t}`,transition:r=!1,...i}=e,[s,l]=Si("Disclosure.Panel"),{close:a}=Ta("Disclosure.Panel"),[u,c]=(0,ue.useState)(null),d=K(n,E(h=>{ma(()=>l({type:5,element:h}))}),c);(0,ue.useEffect)(()=>(l({type:3,panelId:o}),()=>{l({type:3,panelId:null})}),[o,l]);let p=Me(),[f,m]=Ve(r,u,p!==null?(p&1)===1:s.disclosureState===0),T=(0,ue.useMemo)(()=>({open:s.disclosureState===0,close:a}),[s.disclosureState,a]),b={ref:d,id:o,...We(m)},g=H();return ue.default.createElement(Cn,null,ue.default.createElement(Ai.Provider,{value:s.panelId},g({ourProps:b,theirProps:i,slot:T,defaultTag:cd,features:fd,visible:f,name:"Disclosure.Panel"})))}var pd=M(ld),ba=M(ud),ga=M(dd),md=Object.assign(pd,{Button:ba,Panel:ga});var Kt=ie(require("react"),1);var Td="div";function bd(e,n){let t=`headlessui-control-${(0,Q.useId)()}`,[o,r]=_e(),[i,s]=tt(),l=Te(),{disabled:a=l||!1,...u}=e,c=(0,Kt.useMemo)(()=>({disabled:a}),[a]),d={ref:n,disabled:a||void 0,"aria-disabled":a||void 0},p=H();return Kt.default.createElement(So,{value:a},Kt.default.createElement(r,{value:o},Kt.default.createElement(s,{value:i},Kt.default.createElement(ds,{id:t},p({ourProps:d,theirProps:{...u,children:Kt.default.createElement(cs,null,typeof u.children=="function"?u.children(c):u.children)},slot:c,defaultTag:Td,name:"Field"})))))}var gd=M(bd);var po=ie(require("react"),1);var ur=require("react");function ya(e){let n=typeof e=="string"?e:void 0,[t,o]=(0,ur.useState)(n);return[n!=null?n:t,(0,ur.useCallback)(r=>{n||r instanceof HTMLElement&&o(r.tagName.toLowerCase())},[n])]}var ha="fieldset";function yd(e,n){var f;let t=Te(),{disabled:o=t||!1,...r}=e,[i,s]=ya((f=e.as)!=null?f:ha),l=K(n,s),[a,u]=_e(),c=(0,po.useMemo)(()=>({disabled:o}),[o]),d=i==="fieldset"?{ref:l,"aria-labelledby":a,disabled:o||void 0}:{ref:l,role:"group","aria-labelledby":a,"aria-disabled":o||void 0},p=H();return po.default.createElement(So,{value:o},po.default.createElement(u,null,p({ourProps:d,theirProps:r,slot:c,defaultTag:ha,name:"Fieldset"})))}var hd=M(yd);var va=require("react");var vd="input";function Ed(e,n){let t=(0,Q.useId)(),o=De(),r=Te(),{id:i=o||`headlessui-input-${t}`,disabled:s=r||!1,autoFocus:l=!1,invalid:a=!1,...u}=e,c=Le(),d=we(),{isFocused:p,focusProps:f}=ce({autoFocus:l}),{isHovered:m,hoverProps:T}=fe({isDisabled:s}),b=se({ref:n,id:i,"aria-labelledby":c,"aria-describedby":d,"aria-invalid":a?"true":void 0,disabled:s||void 0,autoFocus:l},f,T),g=(0,va.useMemo)(()=>({disabled:s,invalid:a,hover:m,focus:p,autofocus:l}),[s,a,m,p,l]);return H()({ourProps:b,theirProps:u,slot:g,defaultTag:vd,name:"Input"})}var xd=M(Ed);var Ea=ie(require("react"),1);function Pd(e,n){return Ea.default.createElement(Xe,{as:"div",ref:n,...e})}var Rd=M(Pd);var oe=ie(require("react"),1),zt=require("react-dom");var xa=require("react");function cr(e,n){let t=(0,xa.useRef)({left:0,top:0});if(W(()=>{if(!n)return;let i=n.getBoundingClientRect();i&&(t.current=i)},[e,n]),n==null||!e||n===document.activeElement)return!1;let o=n.getBoundingClientRect();return o.top!==t.current.top||o.left!==t.current.left}var Oi=require("react");var Pa=/([\u2700-\u27BF]|[\uE000-\uF8FF]|\uD83C[\uDC00-\uDFFF]|\uD83D[\uDC00-\uDFFF]|[\u2011-\u26FF]|\uD83E[\uDD10-\uDDFF])/g;function Ra(e){var i,s;let n=(i=e.innerText)!=null?i:"",t=e.cloneNode(!0);if(!(t instanceof HTMLElement))return n;let o=!1;for(let l of t.querySelectorAll('[hidden],[aria-hidden],[role="img"]'))l.remove(),o=!0;let r=o?(s=t.innerText)!=null?s:"":n;return Pa.test(r)&&(r=r.replace(Pa,"")),r}function Sa(e){let n=e.getAttribute("aria-label");if(typeof n=="string")return n.trim();let t=e.getAttribute("aria-labelledby");if(t){let o=t.split(" ").map(r=>{let i=document.getElementById(r);if(i){let s=i.getAttribute("aria-label");return typeof s=="string"?s.trim():Ra(i).trim()}return null}).filter(Boolean);if(o.length>0)return o.join(", ")}return Ra(e).trim()}function fr(e){let n=(0,Oi.useRef)(""),t=(0,Oi.useRef)("");return E(()=>{let o=e.current;if(!o)return"";let r=o.innerText;if(n.current===r)return t.current;let i=Sa(o).trim().toLowerCase();return n.current=r,t.current=i,i})}function Ca(e,n=t=>t){let t=e.activeOptionIndex!==null?e.options[e.activeOptionIndex]:null,o=He(n(e.options.slice()),i=>i.dataRef.current.domRef.current),r=t?o.indexOf(t):null;return r===-1&&(r=null),{options:o,activeOptionIndex:r}}var Sd={[1](e){return e.dataRef.current.disabled||e.listboxState===1?e:{...e,activeOptionIndex:null,listboxState:1,__demoMode:!1}},[0](e){if(e.dataRef.current.disabled||e.listboxState===0)return e;let n=e.activeOptionIndex,{isSelected:t}=e.dataRef.current,o=e.options.findIndex(r=>t(r.dataRef.current.value));return o!==-1&&(n=o),{...e,listboxState:0,activeOptionIndex:n,__demoMode:!1}},[2](e,n){var i,s,l,a,u;if(e.dataRef.current.disabled||e.listboxState===1)return e;let t={...e,searchQuery:"",activationTrigger:(i=n.trigger)!=null?i:1,__demoMode:!1};if(n.focus===5)return{...t,activeOptionIndex:null};if(n.focus===4)return{...t,activeOptionIndex:e.options.findIndex(c=>c.id===n.id)};if(n.focus===1){let c=e.activeOptionIndex;if(c!==null){let d=e.options[c].dataRef.current.domRef,p=Je(n,{resolveItems:()=>e.options,resolveActiveIndex:()=>e.activeOptionIndex,resolveId:f=>f.id,resolveDisabled:f=>f.dataRef.current.disabled});if(p!==null){let f=e.options[p].dataRef.current.domRef;if(((s=d.current)==null?void 0:s.previousElementSibling)===f.current||((l=f.current)==null?void 0:l.previousElementSibling)===null)return{...t,activeOptionIndex:p}}}}else if(n.focus===2){let c=e.activeOptionIndex;if(c!==null){let d=e.options[c].dataRef.current.domRef,p=Je(n,{resolveItems:()=>e.options,resolveActiveIndex:()=>e.activeOptionIndex,resolveId:f=>f.id,resolveDisabled:f=>f.dataRef.current.disabled});if(p!==null){let f=e.options[p].dataRef.current.domRef;if(((a=d.current)==null?void 0:a.nextElementSibling)===f.current||((u=f.current)==null?void 0:u.nextElementSibling)===null)return{...t,activeOptionIndex:p}}}}let o=Ca(e),r=Je(n,{resolveItems:()=>o.options,resolveActiveIndex:()=>o.activeOptionIndex,resolveId:c=>c.id,resolveDisabled:c=>c.dataRef.current.disabled});return{...t,...o,activeOptionIndex:r}},[3]:(e,n)=>{if(e.dataRef.current.disabled||e.listboxState===1)return e;let o=e.searchQuery!==""?0:1,r=e.searchQuery+n.value.toLowerCase(),s=(e.activeOptionIndex!==null?e.options.slice(e.activeOptionIndex+o).concat(e.options.slice(0,e.activeOptionIndex+o)):e.options).find(a=>{var u;return!a.dataRef.current.disabled&&((u=a.dataRef.current.textValue)==null?void 0:u.startsWith(r))}),l=s?e.options.indexOf(s):-1;return l===-1||l===e.activeOptionIndex?{...e,searchQuery:r}:{...e,searchQuery:r,activeOptionIndex:l,activationTrigger:1}},[4](e){return e.dataRef.current.disabled||e.listboxState===1||e.searchQuery===""?e:{...e,searchQuery:""}},[5]:(e,n)=>{let t=e.options.concat(n.options),o=e.activeOptionIndex;if(e.activeOptionIndex===null){let{isSelected:r}=e.dataRef.current;if(r){let i=t.findIndex(s=>r==null?void 0:r(s.dataRef.current.value));i!==-1&&(o=i)}}return{...e,options:t,activeOptionIndex:o,pendingShouldSort:!0}},[6]:(e,n)=>{let t=e.options,o=[],r=new Set(n.options);for(let[i,s]of t.entries())if(r.has(s.id)&&(o.push(i),r.delete(s.id),r.size===0))break;if(o.length>0){t=t.slice();for(let i of o.reverse())t.splice(i,1)}return{...e,options:t,activationTrigger:1}},[7]:(e,n)=>e.buttonElement===n.element?e:{...e,buttonElement:n.element},[8]:(e,n)=>e.optionsElement===n.element?e:{...e,optionsElement:n.element},[9]:e=>e.pendingShouldSort?{...e,...Ca(e),pendingShouldSort:!1}:e},Hn=class extends jt{constructor(t){super(t);Be(this,"actions",{onChange:t=>{let{onChange:o,compare:r,mode:i,value:s}=this.state.dataRef.current;return Y(i,{[0]:()=>o==null?void 0:o(t),[1]:()=>{let l=s.slice(),a=l.findIndex(u=>r(u,t));return a===-1?l.push(t):l.splice(a,1),o==null?void 0:o(l)}})},registerOption:tn(()=>{let t=[],o=new Set;return[(r,i)=>{o.has(i)||(o.add(i),t.push({id:r,dataRef:i}))},()=>(o.clear(),this.send({type:5,options:t.splice(0)}))]}),unregisterOption:tn(()=>{let t=[];return[o=>t.push(o),()=>{this.send({type:6,options:t.splice(0)})}]}),goToOption:tn(()=>{let t=null;return[(o,r)=>{t={type:2,...o,trigger:r}},()=>t&&this.send(t)]}),closeListbox:()=>{this.send({type:1})},openListbox:()=>{this.send({type:0})},selectActiveOption:()=>{if(this.state.activeOptionIndex!==null){let{dataRef:t,id:o}=this.state.options[this.state.activeOptionIndex];this.actions.onChange(t.current.value),this.send({type:2,focus:4,id:o})}},selectOption:t=>{let o=this.state.options.find(r=>r.id===t);o&&this.actions.onChange(o.dataRef.current.value)},search:t=>{this.send({type:3,value:t})},clearSearch:()=>{this.send({type:4})},setButtonElement:t=>{this.send({type:7,element:t})},setOptionsElement:t=>{this.send({type:8,element:t})}});Be(this,"selectors",{activeDescendantId(t){var i;let o=t.activeOptionIndex,r=t.options;return o===null||(i=r[o])==null?void 0:i.id},isActive(t,o){var s;let r=t.activeOptionIndex,i=t.options;return r!==null?((s=i[r])==null?void 0:s.id)===o:!1},shouldScrollIntoView(t,o){return t.__demoMode||t.listboxState!==0||t.activationTrigger===0?!1:this.isActive(t,o)}});this.on(5,()=>{requestAnimationFrame(()=>{this.send({type:9})})})}static new({__demoMode:t=!1}={}){return new Hn({dataRef:{current:{}},listboxState:t?0:1,options:[],searchQuery:"",activeOptionIndex:null,activationTrigger:1,buttonElement:null,optionsElement:null,__demoMode:t})}reduce(t,o){return Y(o.type,Sd,t,o)}};var kn=require("react");var Li=(0,kn.createContext)(null);function dr(e){let n=(0,kn.useContext)(Li);if(n===null){let t=new Error(`<${e} /> is missing a parent <Listbox /> component.`);throw Error.captureStackTrace&&Error.captureStackTrace(t,Di),t}return n}function Di({__demoMode:e=!1}={}){return(0,kn.useMemo)(()=>Hn.new({__demoMode:e}),[])}var pr=(0,oe.createContext)(null);pr.displayName="ListboxDataContext";function mo(e){let n=(0,oe.useContext)(pr);if(n===null){let t=new Error(`<${e} /> is missing a parent <Listbox /> component.`);throw Error.captureStackTrace&&Error.captureStackTrace(t,mo),t}return n}var Cd=oe.Fragment;function Ad(e,n){let t=Te(),{value:o,defaultValue:r,form:i,name:s,onChange:l,by:a,invalid:u=!1,disabled:c=t||!1,horizontal:d=!1,multiple:p=!1,__demoMode:f=!1,...m}=e,T=d?"horizontal":"vertical",b=K(n),g=at(r),[h=p?[]:void 0,y]=lt(o,l,g),x=Di({__demoMode:f}),v=(0,oe.useRef)({static:!1,hold:!1}),A=(0,oe.useRef)(new Map),D=pn(a),F=(0,oe.useCallback)(R=>Y(L.mode,{[1]:()=>h.some(P=>D(P,R)),[0]:()=>D(h,R)}),[h]),L=(0,oe.useMemo)(()=>({value:h,disabled:c,invalid:u,mode:p?1:0,orientation:T,onChange:y,compare:D,isSelected:F,optionsPropsRef:v,listRef:A}),[h,c,u,p,T,y,D,F,v,A]);W(()=>{x.state.dataRef.current=L},[L]);let S=le(x,R=>R.listboxState),O=S===0,[C,N]=le(x,R=>[R.buttonElement,R.optionsElement]);dt(O,[C,N],(R,P)=>{x.send({type:1}),Rt(P,1)||(R.preventDefault(),C==null||C.focus())});let j=(0,oe.useMemo)(()=>({open:S===0,disabled:c,invalid:u,value:h}),[S,c,u,h]),[_,$]=_e({inherit:!0}),G={ref:b},ne=(0,oe.useCallback)(()=>{if(g!==void 0)return y==null?void 0:y(g)},[y,g]),J=H();return oe.default.createElement($,{value:_,props:{htmlFor:C==null?void 0:C.id},slot:{open:S===0,disabled:c}},oe.default.createElement(Vt,null,oe.default.createElement(Li.Provider,{value:x},oe.default.createElement(pr.Provider,{value:L},oe.default.createElement(Ye,{value:Y(S,{[0]:1,[1]:2})},s!=null&&h!=null&&oe.default.createElement(ut,{disabled:c,data:{[s]:h},form:i,onReset:ne}),J({ourProps:G,theirProps:m,slot:j,defaultTag:Cd,name:"Listbox"}))))))}var Od="button";function Ld(e,n){let t=(0,Q.useId)(),o=De(),r=mo("Listbox.Button"),i=dr("Listbox.Button"),{id:s=o||`headlessui-listbox-button-${t}`,disabled:l=r.disabled||!1,autoFocus:a=!1,...u}=e,c=K(n,Ut(),i.actions.setButtonElement),d=Jo(),p=E(_=>{switch(_.key){case"Enter":Mt(_.currentTarget);break;case" ":case"ArrowDown":_.preventDefault(),(0,zt.flushSync)(()=>i.actions.openListbox()),r.value||i.actions.goToOption({focus:0});break;case"ArrowUp":_.preventDefault(),(0,zt.flushSync)(()=>i.actions.openListbox()),r.value||i.actions.goToOption({focus:3});break}}),f=E(_=>{switch(_.key){case" ":_.preventDefault();break}}),m=E(_=>{var $;if(_.button===0){if(Ie(_.currentTarget))return _.preventDefault();i.state.listboxState===0?((0,zt.flushSync)(()=>i.actions.closeListbox()),($=i.state.buttonElement)==null||$.focus({preventScroll:!0})):(_.preventDefault(),i.actions.openListbox())}}),T=E(_=>_.preventDefault()),b=Le([s]),g=we(),{isFocusVisible:h,focusProps:y}=ce({autoFocus:a}),{isHovered:x,hoverProps:v}=fe({isDisabled:l}),{pressed:A,pressProps:D}=Se({disabled:l}),F=le(i,_=>_.listboxState),L=(0,oe.useMemo)(()=>({open:F===0,active:A||F===0,disabled:l,invalid:r.invalid,value:r.value,hover:x,focus:h,autofocus:a}),[F,r.value,l,x,h,A,r.invalid,a]),S=le(i,_=>_.listboxState===0),[O,C]=le(i,_=>[_.buttonElement,_.optionsElement]),N=se(d(),{ref:c,id:s,type:$e(e,O),"aria-haspopup":"listbox","aria-controls":C==null?void 0:C.id,"aria-expanded":S,"aria-labelledby":b,"aria-describedby":g,disabled:l||void 0,autoFocus:a,onKeyDown:p,onKeyUp:f,onKeyPress:T,onMouseDown:m},y,v,D);return H()({ourProps:N,theirProps:u,slot:L,defaultTag:Od,name:"Listbox.Button"})}var Aa=(0,oe.createContext)(!1),Dd="div",Id=3;function Fd(e,n){let t=(0,Q.useId)(),{id:o=`headlessui-listbox-options-${t}`,anchor:r,portal:i=!1,modal:s=!0,transition:l=!1,...a}=e,u=Bt(r),[c,d]=(0,oe.useState)(null);u&&(i=!0);let p=mo("Listbox.Options"),f=dr("Listbox.Options"),[m,T,b,g]=le(f,B=>[B.listboxState,B.buttonElement,B.optionsElement,B.__demoMode]),h=Pe(T),y=Pe(b),x=Me(),[v,A]=Ve(l,c,x!==null?(x&1)===1:m===0);ft(v,T,f.actions.closeListbox);let D=g?!1:s&&m===0;pt(D,y);let F=g?!1:s&&m===0;_t(F,{allowed:(0,oe.useCallback)(()=>[T,b],[T,b])});let L=m!==0,O=cr(L,T)?!1:v,C=v&&m===1,N=Sn(C,p.value),j=E(B=>p.compare(N,B)),_=le(f,B=>{var ae;if(u==null||!((ae=u==null?void 0:u.to)!=null&&ae.includes("selection")))return null;let ee=B.options.findIndex(it=>j(it.dataRef.current.value));return ee===-1&&(ee=0),ee}),$=(()=>{if(u==null)return;if(_===null)return{...u,inner:void 0};let B=Array.from(p.listRef.current.values());return{...u,inner:{listRef:{current:B},index:_}}})(),[G,ne]=Wt($),J=Gt(),R=K(n,u?G:null,f.actions.setOptionsElement,d),P=Re();(0,oe.useEffect)(()=>{var ee;let B=b;B&&m===0&&B!==((ee=xe(B))==null?void 0:ee.activeElement)&&(B==null||B.focus({preventScroll:!0}))},[m,b]);let w=E(B=>{var ee,ae;switch(P.dispose(),B.key){case" ":if(f.state.searchQuery!=="")return B.preventDefault(),B.stopPropagation(),f.actions.search(B.key);case"Enter":if(B.preventDefault(),B.stopPropagation(),f.state.activeOptionIndex!==null){let{dataRef:it}=f.state.options[f.state.activeOptionIndex];f.actions.onChange(it.current.value)}p.mode===0&&((0,zt.flushSync)(()=>f.actions.closeListbox()),(ee=f.state.buttonElement)==null||ee.focus({preventScroll:!0}));break;case Y(p.orientation,{vertical:"ArrowDown",horizontal:"ArrowRight"}):return B.preventDefault(),B.stopPropagation(),f.actions.goToOption({focus:2});case Y(p.orientation,{vertical:"ArrowUp",horizontal:"ArrowLeft"}):return B.preventDefault(),B.stopPropagation(),f.actions.goToOption({focus:1});case"Home":case"PageUp":return B.preventDefault(),B.stopPropagation(),f.actions.goToOption({focus:0});case"End":case"PageDown":return B.preventDefault(),B.stopPropagation(),f.actions.goToOption({focus:3});case"Escape":B.preventDefault(),B.stopPropagation(),(0,zt.flushSync)(()=>f.actions.closeListbox()),(ae=f.state.buttonElement)==null||ae.focus({preventScroll:!0});return;case"Tab":B.preventDefault(),B.stopPropagation(),(0,zt.flushSync)(()=>f.actions.closeListbox()),ko(f.state.buttonElement,B.shiftKey?2:4);break;default:B.key.length===1&&(f.actions.search(B.key),P.setTimeout(()=>f.actions.clearSearch(),350));break}}),k=le(f,B=>{var ee;return(ee=B.buttonElement)==null?void 0:ee.id}),z=(0,oe.useMemo)(()=>({open:m===0}),[m]),I=se(u?J():{},{id:o,ref:R,"aria-activedescendant":le(f,f.selectors.activeDescendantId),"aria-multiselectable":p.mode===1?!0:void 0,"aria-labelledby":k,"aria-orientation":p.orientation,onKeyDown:w,role:"listbox",tabIndex:m===0?0:void 0,style:{...a.style,...ne,"--button-width":Pt(T,!0).width},...We(A)}),U=H(),X=(0,oe.useMemo)(()=>p.mode===1?p:{...p,isSelected:j},[p,j]);return oe.default.createElement(Qe,{enabled:i?e.static||v:!1,ownerDocument:h},oe.default.createElement(pr.Provider,{value:X},U({ourProps:I,theirProps:a,slot:z,defaultTag:Dd,features:Id,visible:O,name:"Listbox.Options"})))}var Md="div";function wd(e,n){let t=(0,Q.useId)(),{id:o=`headlessui-listbox-option-${t}`,disabled:r=!1,value:i,...s}=e,l=(0,oe.useContext)(Aa)===!0,a=mo("Listbox.Option"),u=dr("Listbox.Option"),c=le(u,S=>u.selectors.isActive(S,o)),d=a.isSelected(i),p=(0,oe.useRef)(null),f=fr(p),m=pe({disabled:r,value:i,domRef:p,get textValue(){return f()}}),T=K(n,p,S=>{S?a.listRef.current.set(o,S):a.listRef.current.delete(o)}),b=le(u,S=>u.selectors.shouldScrollIntoView(S,o));W(()=>{if(b)return he().requestAnimationFrame(()=>{var S,O;(O=(S=p.current)==null?void 0:S.scrollIntoView)==null||O.call(S,{block:"nearest"})})},[b,p]),W(()=>{if(!l)return u.actions.registerOption(o,m),()=>u.actions.unregisterOption(o)},[m,o,l]);let g=E(S=>{var O;if(r)return S.preventDefault();u.actions.onChange(i),a.mode===0&&((0,zt.flushSync)(()=>u.actions.closeListbox()),(O=u.state.buttonElement)==null||O.focus({preventScroll:!0}))}),h=E(()=>{if(r)return u.actions.goToOption({focus:5});u.actions.goToOption({focus:4,id:o})}),y=gn(),x=E(S=>{y.update(S),!r&&(c||u.actions.goToOption({focus:4,id:o},0))}),v=E(S=>{y.wasMoved(S)&&(r||c||u.actions.goToOption({focus:4,id:o},0))}),A=E(S=>{y.wasMoved(S)&&(r||c&&u.actions.goToOption({focus:5}))}),D=(0,oe.useMemo)(()=>({active:c,focus:c,selected:d,disabled:r,selectedOption:d&&l}),[c,d,r,l]),F=l?{}:{id:o,ref:T,role:"option",tabIndex:r===!0?void 0:-1,"aria-disabled":r===!0?!0:void 0,"aria-selected":d,disabled:void 0,onClick:g,onFocus:h,onPointerEnter:x,onMouseEnter:x,onPointerMove:v,onMouseMove:v,onPointerLeave:A,onMouseLeave:A},L=H();return!d&&l?null:L({ourProps:F,theirProps:s,slot:D,defaultTag:Md,name:"Listbox.Option"})}var _d=oe.Fragment;function Hd(e,n){let{options:t,placeholder:o,...r}=e,s={ref:K(n)},l=mo("ListboxSelectedOption"),a=(0,oe.useMemo)(()=>({}),[]),u=l.value===void 0||l.value===null||l.mode===1&&Array.isArray(l.value)&&l.value.length===0,c=H();return oe.default.createElement(Aa.Provider,{value:!0},c({ourProps:s,theirProps:{...r,children:oe.default.createElement(oe.default.Fragment,null,o&&u?o:t)},slot:a,defaultTag:_d,name:"ListboxSelectedOption"}))}var kd=M(Ad),Oa=M(Ld),La=Xe,Da=M(Fd),Ia=M(wd),Fa=M(Hd),$d=Object.assign(kd,{Button:Oa,Label:La,Options:Da,Option:Ia,SelectedOption:Fa});var ge=ie(require("react"),1),Tr=require("react-dom");function Ma(e,n=t=>t){let t=e.activeItemIndex!==null?e.items[e.activeItemIndex]:null,o=He(n(e.items.slice()),i=>i.dataRef.current.domRef.current),r=t?o.indexOf(t):null;return r===-1&&(r=null),{items:o,activeItemIndex:r}}var Nd={[1](e){return e.menuState===1?e:{...e,activeItemIndex:null,pendingFocus:{focus:5},menuState:1}},[0](e,n){return e.menuState===0?e:{...e,__demoMode:!1,pendingFocus:n.focus,menuState:0}},[2]:(e,n)=>{var i,s,l,a,u;if(e.menuState===1)return e;let t={...e,searchQuery:"",activationTrigger:(i=n.trigger)!=null?i:1,__demoMode:!1};if(n.focus===5)return{...t,activeItemIndex:null};if(n.focus===4)return{...t,activeItemIndex:e.items.findIndex(c=>c.id===n.id)};if(n.focus===1){let c=e.activeItemIndex;if(c!==null){let d=e.items[c].dataRef.current.domRef,p=Je(n,{resolveItems:()=>e.items,resolveActiveIndex:()=>e.activeItemIndex,resolveId:f=>f.id,resolveDisabled:f=>f.dataRef.current.disabled});if(p!==null){let f=e.items[p].dataRef.current.domRef;if(((s=d.current)==null?void 0:s.previousElementSibling)===f.current||((l=f.current)==null?void 0:l.previousElementSibling)===null)return{...t,activeItemIndex:p}}}}else if(n.focus===2){let c=e.activeItemIndex;if(c!==null){let d=e.items[c].dataRef.current.domRef,p=Je(n,{resolveItems:()=>e.items,resolveActiveIndex:()=>e.activeItemIndex,resolveId:f=>f.id,resolveDisabled:f=>f.dataRef.current.disabled});if(p!==null){let f=e.items[p].dataRef.current.domRef;if(((a=d.current)==null?void 0:a.nextElementSibling)===f.current||((u=f.current)==null?void 0:u.nextElementSibling)===null)return{...t,activeItemIndex:p}}}}let o=Ma(e),r=Je(n,{resolveItems:()=>o.items,resolveActiveIndex:()=>o.activeItemIndex,resolveId:c=>c.id,resolveDisabled:c=>c.dataRef.current.disabled});return{...t,...o,activeItemIndex:r}},[3]:(e,n)=>{let o=e.searchQuery!==""?0:1,r=e.searchQuery+n.value.toLowerCase(),s=(e.activeItemIndex!==null?e.items.slice(e.activeItemIndex+o).concat(e.items.slice(0,e.activeItemIndex+o)):e.items).find(a=>{var u;return((u=a.dataRef.current.textValue)==null?void 0:u.startsWith(r))&&!a.dataRef.current.disabled}),l=s?e.items.indexOf(s):-1;return l===-1||l===e.activeItemIndex?{...e,searchQuery:r}:{...e,searchQuery:r,activeItemIndex:l,activationTrigger:1}},[4](e){return e.searchQuery===""?e:{...e,searchQuery:"",searchActiveItemIndex:null}},[5]:(e,n)=>{let t=e.items.concat(n.items.map(r=>r)),o=e.activeItemIndex;return e.pendingFocus.focus!==5&&(o=Je(e.pendingFocus,{resolveItems:()=>t,resolveActiveIndex:()=>e.activeItemIndex,resolveId:r=>r.id,resolveDisabled:r=>r.dataRef.current.disabled})),{...e,items:t,activeItemIndex:o,pendingFocus:{focus:5},pendingShouldSort:!0}},[6]:(e,n)=>{let t=e.items,o=[],r=new Set(n.items);for(let[i,s]of t.entries())if(r.has(s.id)&&(o.push(i),r.delete(s.id),r.size===0))break;if(o.length>0){t=t.slice();for(let i of o.reverse())t.splice(i,1)}return{...e,items:t,activationTrigger:1}},[7]:(e,n)=>e.buttonElement===n.element?e:{...e,buttonElement:n.element},[8]:(e,n)=>e.itemsElement===n.element?e:{...e,itemsElement:n.element},[9]:e=>e.pendingShouldSort?{...e,...Ma(e),pendingShouldSort:!1}:e},$n=class extends jt{constructor(t){super(t);Be(this,"actions",{registerItem:tn(()=>{let t=[],o=new Set;return[(r,i)=>{o.has(i)||(o.add(i),t.push({id:r,dataRef:i}))},()=>(o.clear(),this.send({type:5,items:t.splice(0)}))]}),unregisterItem:tn(()=>{let t=[];return[o=>t.push(o),()=>this.send({type:6,items:t.splice(0)})]})});Be(this,"selectors",{activeDescendantId(t){var i;let o=t.activeItemIndex,r=t.items;return o===null||(i=r[o])==null?void 0:i.id},isActive(t,o){var s;let r=t.activeItemIndex,i=t.items;return r!==null?((s=i[r])==null?void 0:s.id)===o:!1},shouldScrollIntoView(t,o){return t.__demoMode||t.menuState!==0||t.activationTrigger===0?!1:this.isActive(t,o)}});this.on(5,()=>{requestAnimationFrame(()=>{this.send({type:9})})})}static new({__demoMode:t=!1}={}){return new $n({__demoMode:t,menuState:t?0:1,buttonElement:null,itemsElement:null,items:[],searchQuery:"",activeItemIndex:null,activationTrigger:1,pendingShouldSort:!1,pendingFocus:{focus:5}})}reduce(t,o){return Y(o.type,Nd,t,o)}};var Nn=require("react");var Ii=(0,Nn.createContext)(null);function mr(e){let n=(0,Nn.useContext)(Ii);if(n===null){let t=new Error(`<${e} /> is missing a parent <Menu /> component.`);throw Error.captureStackTrace&&Error.captureStackTrace(t,Fi),t}return n}function Fi({__demoMode:e=!1}={}){return(0,Nn.useMemo)(()=>$n.new({__demoMode:e}),[])}var Bd=ge.Fragment;function Ud(e,n){let{__demoMode:t=!1,...o}=e,r=Fi({__demoMode:t}),[i,s,l]=le(r,m=>[m.menuState,m.itemsElement,m.buttonElement]),a=K(n),u=i===0;dt(u,[l,s],(m,T)=>{var b;r.send({type:1}),Rt(T,1)||(m.preventDefault(),(b=r.state.buttonElement)==null||b.focus())});let c=E(()=>{r.send({type:1})}),d=(0,ge.useMemo)(()=>({open:i===0,close:c}),[i,c]),p={ref:a},f=H();return ge.default.createElement(Vt,null,ge.default.createElement(Ii.Provider,{value:r},ge.default.createElement(Ye,{value:Y(i,{[0]:1,[1]:2})},f({ourProps:p,theirProps:o,slot:d,defaultTag:Bd,name:"Menu"}))))}var Gd="button";function Wd(e,n){let t=mr("Menu.Button"),o=(0,Q.useId)(),{id:r=`headlessui-menu-button-${o}`,disabled:i=!1,autoFocus:s=!1,...l}=e,a=(0,ge.useRef)(null),u=Jo(),c=K(n,a,Ut(),E(L=>t.send({type:7,element:L}))),d=E(L=>{switch(L.key){case" ":case"Enter":case"ArrowDown":L.preventDefault(),L.stopPropagation(),t.send({type:0,focus:{focus:0}});break;case"ArrowUp":L.preventDefault(),L.stopPropagation(),t.send({type:0,focus:{focus:3}});break}}),p=E(L=>{switch(L.key){case" ":L.preventDefault();break}}),[f,m]=le(t,L=>[L.menuState,L.itemsElement]),T=E(L=>{var S;if(L.button===0){if(Ie(L.currentTarget))return L.preventDefault();i||(f===0?((0,Tr.flushSync)(()=>t.send({type:1})),(S=a.current)==null||S.focus({preventScroll:!0})):(L.preventDefault(),t.send({type:0,focus:{focus:5},trigger:0})))}}),{isFocusVisible:b,focusProps:g}=ce({autoFocus:s}),{isHovered:h,hoverProps:y}=fe({isDisabled:i}),{pressed:x,pressProps:v}=Se({disabled:i}),A=(0,ge.useMemo)(()=>({open:f===0,active:x||f===0,disabled:i,hover:h,focus:b,autofocus:s}),[f,h,b,x,i,s]),D=se(u(),{ref:c,id:r,type:$e(e,a.current),"aria-haspopup":"menu","aria-controls":m==null?void 0:m.id,"aria-expanded":f===0,disabled:i||void 0,autoFocus:s,onKeyDown:d,onKeyUp:p,onMouseDown:T},g,y,v);return H()({ourProps:D,theirProps:l,slot:A,defaultTag:Gd,name:"Menu.Button"})}var Vd="div",jd=3;function Kd(e,n){let t=(0,Q.useId)(),{id:o=`headlessui-menu-items-${t}`,anchor:r,portal:i=!1,modal:s=!0,transition:l=!1,...a}=e,u=Bt(r),c=mr("Menu.Items"),[d,p]=Wt(u),f=Gt(),[m,T]=(0,ge.useState)(null),b=K(n,u?d:null,E(R=>c.send({type:8,element:R})),T),[g,h]=le(c,R=>[R.menuState,R.buttonElement]),y=Pe(h),x=Pe(m);u&&(i=!0);let v=Me(),[A,D]=Ve(l,m,v!==null?(v&1)===1:g===0);ft(A,h,()=>{c.send({type:1})});let F=le(c,R=>R.__demoMode),L=F?!1:s&&g===0;pt(L,x);let S=F?!1:s&&g===0;_t(S,{allowed:(0,ge.useCallback)(()=>[h,m],[h,m])});let O=g!==0,N=cr(O,h)?!1:A;(0,ge.useEffect)(()=>{let R=m;R&&g===0&&R!==(x==null?void 0:x.activeElement)&&R.focus({preventScroll:!0})},[g,m,x]),Bo(g===0,{container:m,accept(R){return R.getAttribute("role")==="menuitem"?NodeFilter.FILTER_REJECT:R.hasAttribute("role")?NodeFilter.FILTER_SKIP:NodeFilter.FILTER_ACCEPT},walk(R){R.setAttribute("role","none")}});let j=Re(),_=E(R=>{var P,w,k;switch(j.dispose(),R.key){case" ":if(c.state.searchQuery!=="")return R.preventDefault(),R.stopPropagation(),c.send({type:3,value:R.key});case"Enter":if(R.preventDefault(),R.stopPropagation(),c.state.activeItemIndex!==null){let{dataRef:z}=c.state.items[c.state.activeItemIndex];(w=(P=z.current)==null?void 0:P.domRef.current)==null||w.click()}c.send({type:1}),Wr(c.state.buttonElement);break;case"ArrowDown":return R.preventDefault(),R.stopPropagation(),c.send({type:2,focus:2});case"ArrowUp":return R.preventDefault(),R.stopPropagation(),c.send({type:2,focus:1});case"Home":case"PageUp":return R.preventDefault(),R.stopPropagation(),c.send({type:2,focus:0});case"End":case"PageDown":return R.preventDefault(),R.stopPropagation(),c.send({type:2,focus:3});case"Escape":R.preventDefault(),R.stopPropagation(),(0,Tr.flushSync)(()=>c.send({type:1})),(k=c.state.buttonElement)==null||k.focus({preventScroll:!0});break;case"Tab":R.preventDefault(),R.stopPropagation(),(0,Tr.flushSync)(()=>c.send({type:1})),ko(c.state.buttonElement,R.shiftKey?2:4);break;default:R.key.length===1&&(c.send({type:3,value:R.key}),j.setTimeout(()=>c.send({type:4}),350));break}}),$=E(R=>{switch(R.key){case" ":R.preventDefault();break}}),G=(0,ge.useMemo)(()=>({open:g===0}),[g]),ne=se(u?f():{},{"aria-activedescendant":le(c,c.selectors.activeDescendantId),"aria-labelledby":le(c,R=>{var P;return(P=R.buttonElement)==null?void 0:P.id}),id:o,onKeyDown:_,onKeyUp:$,role:"menu",tabIndex:g===0?0:void 0,ref:b,style:{...a.style,...p,"--button-width":Pt(h,!0).width},...We(D)}),J=H();return ge.default.createElement(Qe,{enabled:i?e.static||A:!1,ownerDocument:y},J({ourProps:ne,theirProps:a,slot:G,defaultTag:Vd,features:jd,visible:N,name:"Menu.Items"}))}var zd=ge.Fragment;function Xd(e,n){let t=(0,Q.useId)(),{id:o=`headlessui-menu-item-${t}`,disabled:r=!1,...i}=e,s=mr("Menu.Item"),l=le(s,O=>s.selectors.isActive(O,o)),a=(0,ge.useRef)(null),u=K(n,a),c=le(s,O=>s.selectors.shouldScrollIntoView(O,o));W(()=>{if(c)return he().requestAnimationFrame(()=>{var O,C;(C=(O=a.current)==null?void 0:O.scrollIntoView)==null||C.call(O,{block:"nearest"})})},[c,a]);let d=fr(a),p=(0,ge.useRef)({disabled:r,domRef:a,get textValue(){return d()}});W(()=>{p.current.disabled=r},[p,r]),W(()=>(s.actions.registerItem(o,p),()=>s.actions.unregisterItem(o)),[p,o]);let f=E(()=>{s.send({type:1})}),m=E(O=>{if(r)return O.preventDefault();s.send({type:1}),Wr(s.state.buttonElement)}),T=E(()=>{if(r)return s.send({type:2,focus:5});s.send({type:2,focus:4,id:o})}),b=gn(),g=E(O=>{b.update(O),!r&&(l||s.send({type:2,focus:4,id:o,trigger:0}))}),h=E(O=>{b.wasMoved(O)&&(r||l||s.send({type:2,focus:4,id:o,trigger:0}))}),y=E(O=>{b.wasMoved(O)&&(r||l&&s.send({type:2,focus:5}))}),[x,v]=_e(),[A,D]=tt(),F=(0,ge.useMemo)(()=>({active:l,focus:l,disabled:r,close:f}),[l,r,f]),L={id:o,ref:u,role:"menuitem",tabIndex:r===!0?void 0:-1,"aria-disabled":r===!0?!0:void 0,"aria-labelledby":x,"aria-describedby":A,disabled:void 0,onClick:m,onFocus:T,onPointerEnter:g,onMouseEnter:g,onPointerMove:h,onMouseMove:h,onPointerLeave:y,onMouseLeave:y},S=H();return ge.default.createElement(v,null,ge.default.createElement(D,null,S({ourProps:L,theirProps:i,slot:F,defaultTag:zd,name:"Menu.Item"})))}var Yd="div";function qd(e,n){let[t,o]=_e(),r=e,i={ref:n,"aria-labelledby":t,role:"group"},s=H();return ge.default.createElement(o,null,s({ourProps:i,theirProps:r,slot:{},defaultTag:Yd,name:"Menu.Section"}))}var Jd="header";function Qd(e,n){let t=(0,Q.useId)(),{id:o=`headlessui-menu-heading-${t}`,...r}=e,i=Io();W(()=>i.register(o),[o,i.register]);let s={id:o,ref:n,role:"presentation",...i.props};return H()({ourProps:s,theirProps:r,slot:{},defaultTag:Jd,name:"Menu.Heading"})}var Zd="div";function ep(e,n){let t=e,o={ref:n,role:"separator"};return H()({ourProps:o,theirProps:t,slot:{},defaultTag:Zd,name:"Menu.Separator"})}var tp=M(Ud),wa=M(Wd),_a=M(Kd),Ha=M(Xd),ka=M(qd),$a=M(Qd),Na=M(ep),np=Object.assign(tp,{Button:wa,Items:_a,Item:Ha,Section:ka,Heading:$a,Separator:Na});var V=ie(require("react"),1);var op={[0]:e=>({...e,popoverState:Y(e.popoverState,{[0]:1,[1]:0}),__demoMode:!1}),[1](e){return e.popoverState===1?e:{...e,popoverState:1,__demoMode:!1}},[2](e,n){return e.button===n.button?e:{...e,button:n.button}},[3](e,n){return e.buttonId===n.buttonId?e:{...e,buttonId:n.buttonId}},[4](e,n){return e.panel===n.panel?e:{...e,panel:n.panel}},[5](e,n){return e.panelId===n.panelId?e:{...e,panelId:n.panelId}}},Mi=(0,V.createContext)(null);Mi.displayName="PopoverContext";function br(e){let n=(0,V.useContext)(Mi);if(n===null){let t=new Error(`<${e} /> is missing a parent <Popover /> component.`);throw Error.captureStackTrace&&Error.captureStackTrace(t,br),t}return n}var gr=(0,V.createContext)(null);gr.displayName="PopoverAPIContext";function wi(e){let n=(0,V.useContext)(gr);if(n===null){let t=new Error(`<${e} /> is missing a parent <Popover /> component.`);throw Error.captureStackTrace&&Error.captureStackTrace(t,wi),t}return n}var _i=(0,V.createContext)(null);_i.displayName="PopoverGroupContext";function Ba(){return(0,V.useContext)(_i)}var yr=(0,V.createContext)(null);yr.displayName="PopoverPanelContext";function rp(){return(0,V.useContext)(yr)}function ip(e,n){return Y(n.type,op,e,n)}var sp="div";function lp(e,n){var J;let{__demoMode:t=!1,...o}=e,r=(0,V.useRef)(null),i=K(n,cn(R=>{r.current=R})),s=(0,V.useRef)([]),l=(0,V.useReducer)(ip,{__demoMode:t,popoverState:t?0:1,buttons:s,button:null,buttonId:null,panel:null,panelId:null,beforePanelSentinel:(0,V.createRef)(),afterPanelSentinel:(0,V.createRef)(),afterButtonSentinel:(0,V.createRef)()}),[{popoverState:a,button:u,buttonId:c,panel:d,panelId:p,beforePanelSentinel:f,afterPanelSentinel:m,afterButtonSentinel:T},b]=l,g=Pe((J=r.current)!=null?J:u),h=(0,V.useMemo)(()=>{if(!u||!d)return!1;for(let U of document.querySelectorAll("body > *"))if(Number(U==null?void 0:U.contains(u))^Number(U==null?void 0:U.contains(d)))return!0;let R=Tn(),P=R.indexOf(u),w=(P+R.length-1)%R.length,k=(P+1)%R.length,z=R[w],I=R[k];return!d.contains(z)&&!d.contains(I)},[u,d]),y=pe(c),x=pe(p),v=(0,V.useMemo)(()=>({buttonId:y,panelId:x,close:()=>b({type:1})}),[y,x,b]),A=Ba(),D=A==null?void 0:A.registerPopover,F=E(()=>{var R;return(R=A==null?void 0:A.isFocusWithinPopoverGroup())!=null?R:(g==null?void 0:g.activeElement)&&((u==null?void 0:u.contains(g.activeElement))||(d==null?void 0:d.contains(g.activeElement)))});(0,V.useEffect)(()=>D==null?void 0:D(v),[D,v]);let[L,S]=tr(),O=ao(u),C=or({mainTreeNode:O,portals:L,defaultContainers:[u,d]});Ht(g==null?void 0:g.defaultView,"focus",R=>{var P,w,k,z,I,U;R.target!==window&&R.target instanceof HTMLElement&&a===0&&(F()||u&&d&&(C.contains(R.target)||(w=(P=f.current)==null?void 0:P.contains)!=null&&w.call(P,R.target)||(z=(k=m.current)==null?void 0:k.contains)!=null&&z.call(k,R.target)||(U=(I=T.current)==null?void 0:I.contains)!=null&&U.call(I,R.target)||b({type:1})))},!0),dt(a===0,C.resolveContainers,(R,P)=>{b({type:1}),Rt(P,1)||(R.preventDefault(),u==null||u.focus())});let j=E(R=>{b({type:1});let P=(()=>R?R instanceof HTMLElement?R:"current"in R&&R.current instanceof HTMLElement?R.current:u:u)();P==null||P.focus()}),_=(0,V.useMemo)(()=>({close:j,isPortalled:h}),[j,h]),$=(0,V.useMemo)(()=>({open:a===0,close:j}),[a,j]),G={ref:i},ne=H();return V.default.createElement(_n,{node:O},V.default.createElement(Vt,null,V.default.createElement(yr.Provider,{value:null},V.default.createElement(Mi.Provider,{value:l},V.default.createElement(gr.Provider,{value:_},V.default.createElement(dn,{value:j},V.default.createElement(Ye,{value:Y(a,{[0]:1,[1]:2})},V.default.createElement(S,null,ne({ourProps:G,theirProps:o,slot:$,defaultTag:sp,name:"Popover"})))))))))}var ap="button";function up(e,n){let t=(0,Q.useId)(),{id:o=`headlessui-popover-button-${t}`,disabled:r=!1,autoFocus:i=!1,...s}=e,[l,a]=br("Popover.Button"),{isPortalled:u}=wi("Popover.Button"),c=(0,V.useRef)(null),d=`headlessui-focus-sentinel-${(0,Q.useId)()}`,p=Ba(),f=p==null?void 0:p.closeOthers,T=rp()!==null;(0,V.useEffect)(()=>{if(!T)return a({type:3,buttonId:o}),()=>{a({type:3,buttonId:null})}},[T,o,a]);let[b]=(0,V.useState)(()=>Symbol()),g=K(c,n,Ut(),E(P=>{if(!T){if(P)l.buttons.current.push(b);else{let w=l.buttons.current.indexOf(b);w!==-1&&l.buttons.current.splice(w,1)}l.buttons.current.length>1&&console.warn("You are already using a <Popover.Button /> but only 1 <Popover.Button /> is supported."),P&&a({type:2,button:P})}})),h=K(c,n),y=Pe(c),x=E(P=>{var w,k,z;if(T){if(l.popoverState===1)return;switch(P.key){case" ":case"Enter":P.preventDefault(),(k=(w=P.target).click)==null||k.call(w),a({type:1}),(z=l.button)==null||z.focus();break}}else switch(P.key){case" ":case"Enter":P.preventDefault(),P.stopPropagation(),l.popoverState===1&&(f==null||f(l.buttonId)),a({type:0});break;case"Escape":if(l.popoverState!==0)return f==null?void 0:f(l.buttonId);if(!c.current||y!=null&&y.activeElement&&!c.current.contains(y.activeElement))return;P.preventDefault(),P.stopPropagation(),a({type:1});break}}),v=E(P=>{T||P.key===" "&&P.preventDefault()}),A=E(P=>{var w,k;Ie(P.currentTarget)||r||(T?(a({type:1}),(w=l.button)==null||w.focus()):(P.preventDefault(),P.stopPropagation(),l.popoverState===1&&(f==null||f(l.buttonId)),a({type:0}),(k=l.button)==null||k.focus()))}),D=E(P=>{P.preventDefault(),P.stopPropagation()}),{isFocusVisible:F,focusProps:L}=ce({autoFocus:i}),{isHovered:S,hoverProps:O}=fe({isDisabled:r}),{pressed:C,pressProps:N}=Se({disabled:r}),j=l.popoverState===0,_=(0,V.useMemo)(()=>({open:j,active:C||j,disabled:r,hover:S,focus:F,autofocus:i}),[j,S,F,C,r,i]),$=$e(e,l.button),G=T?se({ref:h,type:$,onKeyDown:x,onClick:A,disabled:r||void 0,autoFocus:i},L,O,N):se({ref:g,id:l.buttonId,type:$,"aria-expanded":l.popoverState===0,"aria-controls":l.panel?l.panelId:void 0,disabled:r||void 0,autoFocus:i,onKeyDown:x,onKeyUp:v,onClick:A,onMouseDown:D},L,O,N),ne=uo(),J=E(()=>{let P=l.panel;if(!P)return;function w(){Y(ne.current,{[0]:()=>ye(P,1),[1]:()=>ye(P,8)})===0&&ye(Tn().filter(z=>z.dataset.headlessuiFocusGuard!=="true"),Y(ne.current,{[0]:4,[1]:2}),{relativeTo:l.button})}w()}),R=H();return V.default.createElement(V.default.Fragment,null,R({ourProps:G,theirProps:s,slot:_,defaultTag:ap,name:"Popover.Button"}),j&&!T&&u&&V.default.createElement(Fe,{id:d,ref:l.afterButtonSentinel,features:2,"data-headlessui-focus-guard":!0,as:"button",type:"button",onFocus:J}))}var cp="div",fp=3;function Ua(e,n){let t=(0,Q.useId)(),{id:o=`headlessui-popover-backdrop-${t}`,transition:r=!1,...i}=e,[{popoverState:s},l]=br("Popover.Backdrop"),[a,u]=(0,V.useState)(null),c=K(n,u),d=Me(),[p,f]=Ve(r,a,d!==null?(d&1)===1:s===0),m=E(h=>{if(Ie(h.currentTarget))return h.preventDefault();l({type:1})}),T=(0,V.useMemo)(()=>({open:s===0}),[s]),b={ref:c,id:o,"aria-hidden":!0,onClick:m,...We(f)};return H()({ourProps:b,theirProps:i,slot:T,defaultTag:cp,features:fp,visible:p,name:"Popover.Backdrop"})}var dp="div",pp=3;function mp(e,n){let t=(0,Q.useId)(),{id:o=`headlessui-popover-panel-${t}`,focus:r=!1,anchor:i,portal:s=!1,modal:l=!1,transition:a=!1,...u}=e,[c,d]=br("Popover.Panel"),{close:p,isPortalled:f}=wi("Popover.Panel"),m=`headlessui-focus-sentinel-before-${t}`,T=`headlessui-focus-sentinel-after-${t}`,b=(0,V.useRef)(null),g=Bt(i),[h,y]=Wt(g),x=Gt();g&&(s=!0);let[v,A]=(0,V.useState)(null),D=K(b,n,g?h:null,E(P=>d({type:4,panel:P})),A),F=Pe(c.button),L=Pe(b);W(()=>(d({type:5,panelId:o}),()=>{d({type:5,panelId:null})}),[o,d]);let S=Me(),[O,C]=Ve(a,v,S!==null?(S&1)===1:c.popoverState===0);ft(O,c.button,()=>{d({type:1})});let N=c.__demoMode?!1:l&&O;pt(N,L);let j=E(P=>{var w;switch(P.key){case"Escape":if(c.popoverState!==0||!b.current||L!=null&&L.activeElement&&!b.current.contains(L.activeElement))return;P.preventDefault(),P.stopPropagation(),d({type:1}),(w=c.button)==null||w.focus();break}});(0,V.useEffect)(()=>{var P;e.static||c.popoverState===1&&((P=e.unmount)==null||P)&&d({type:4,panel:null})},[c.popoverState,e.unmount,e.static,d]),(0,V.useEffect)(()=>{if(c.__demoMode||!r||c.popoverState!==0||!b.current)return;let P=L==null?void 0:L.activeElement;b.current.contains(P)||ye(b.current,1)},[c.__demoMode,r,b.current,c.popoverState]);let _=(0,V.useMemo)(()=>({open:c.popoverState===0,close:p}),[c.popoverState,p]),$=se(g?x():{},{ref:D,id:o,onKeyDown:j,onBlur:r&&c.popoverState===0?P=>{var k,z,I,U,X;let w=P.relatedTarget;w&&b.current&&((k=b.current)!=null&&k.contains(w)||(d({type:1}),((I=(z=c.beforePanelSentinel.current)==null?void 0:z.contains)!=null&&I.call(z,w)||(X=(U=c.afterPanelSentinel.current)==null?void 0:U.contains)!=null&&X.call(U,w))&&w.focus({preventScroll:!0})))}:void 0,tabIndex:-1,style:{...u.style,...y,"--button-width":Pt(c.button,!0).width},...We(C)}),G=uo(),ne=E(()=>{let P=b.current;if(!P)return;function w(){Y(G.current,{[0]:()=>{var z;ye(P,1)===0&&((z=c.afterPanelSentinel.current)==null||z.focus())},[1]:()=>{var k;(k=c.button)==null||k.focus({preventScroll:!0})}})}w()}),J=E(()=>{let P=b.current;if(!P)return;function w(){Y(G.current,{[0]:()=>{if(!c.button)return;let k=Tn(),z=k.indexOf(c.button),I=k.slice(0,z+1),X=[...k.slice(z+1),...I];for(let B of X.slice())if(B.dataset.headlessuiFocusGuard==="true"||v!=null&&v.contains(B)){let ee=X.indexOf(B);ee!==-1&&X.splice(ee,1)}ye(X,1,{sorted:!1})},[1]:()=>{var z;ye(P,2)===0&&((z=c.button)==null||z.focus())}})}w()}),R=H();return V.default.createElement(Cn,null,V.default.createElement(yr.Provider,{value:o},V.default.createElement(gr.Provider,{value:{close:p,isPortalled:f}},V.default.createElement(Qe,{enabled:s?e.static||O:!1,ownerDocument:F},O&&f&&V.default.createElement(Fe,{id:m,ref:c.beforePanelSentinel,features:2,"data-headlessui-focus-guard":!0,as:"button",type:"button",onFocus:ne}),R({ourProps:$,theirProps:u,slot:_,defaultTag:dp,features:pp,visible:O,name:"Popover.Panel"}),O&&f&&V.default.createElement(Fe,{id:T,ref:c.afterPanelSentinel,features:2,"data-headlessui-focus-guard":!0,as:"button",type:"button",onFocus:J})))))}var Tp="div";function bp(e,n){let t=(0,V.useRef)(null),o=K(t,n),[r,i]=(0,V.useState)([]),s=E(T=>{i(b=>{let g=b.indexOf(T);if(g!==-1){let h=b.slice();return h.splice(g,1),h}return b})}),l=E(T=>(i(b=>[...b,T]),()=>s(T))),a=E(()=>{var g;let T=xe(t);if(!T)return!1;let b=T.activeElement;return(g=t.current)!=null&&g.contains(b)?!0:r.some(h=>{var y,x;return((y=T.getElementById(h.buttonId.current))==null?void 0:y.contains(b))||((x=T.getElementById(h.panelId.current))==null?void 0:x.contains(b))})}),u=E(T=>{for(let b of r)b.buttonId.current!==T&&b.close()}),c=(0,V.useMemo)(()=>({registerPopover:l,unregisterPopover:s,isFocusWithinPopoverGroup:a,closeOthers:u}),[l,s,a,u]),d=(0,V.useMemo)(()=>({}),[]),p=e,f={ref:o},m=H();return V.default.createElement(_n,null,V.default.createElement(_i.Provider,{value:c},m({ourProps:f,theirProps:p,slot:d,defaultTag:Tp,name:"Popover.Group"})))}var gp=M(lp),Ga=M(up),Wa=M(Ua),Va=M(Ua),ja=M(mp),Ka=M(bp),yp=Object.assign(gp,{Button:Ga,Backdrop:Va,Overlay:Wa,Panel:ja,Group:Ka});var de=ie(require("react"),1);var hp={[0](e,n){let t=[...e.options,{id:n.id,element:n.element,propsRef:n.propsRef}];return{...e,options:He(t,o=>o.element.current)}},[1](e,n){let t=e.options.slice(),o=e.options.findIndex(r=>r.id===n.id);return o===-1?e:(t.splice(o,1),{...e,options:t})}},Hi=(0,de.createContext)(null);Hi.displayName="RadioGroupDataContext";function ki(e){let n=(0,de.useContext)(Hi);if(n===null){let t=new Error(`<${e} /> is missing a parent <RadioGroup /> component.`);throw Error.captureStackTrace&&Error.captureStackTrace(t,ki),t}return n}var $i=(0,de.createContext)(null);$i.displayName="RadioGroupActionsContext";function Ni(e){let n=(0,de.useContext)($i);if(n===null){let t=new Error(`<${e} /> is missing a parent <RadioGroup /> component.`);throw Error.captureStackTrace&&Error.captureStackTrace(t,Ni),t}return n}function vp(e,n){return Y(n.type,hp,e,n)}var Ep="div";function xp(e,n){let t=(0,Q.useId)(),o=Te(),{id:r=`headlessui-radiogroup-${t}`,value:i,form:s,name:l,onChange:a,by:u,disabled:c=o||!1,defaultValue:d,tabIndex:p=0,...f}=e,m=pn(u),[T,b]=(0,de.useReducer)(vp,{options:[]}),g=T.options,[h,y]=_e(),[x,v]=tt(),A=(0,de.useRef)(null),D=K(A,n),F=at(d),[L,S]=lt(i,a,F),O=(0,de.useMemo)(()=>g.find(w=>!w.propsRef.current.disabled),[g]),C=(0,de.useMemo)(()=>g.some(w=>m(w.propsRef.current.value,L)),[g,L]),N=E(w=>{var z;if(c||m(w,L))return!1;let k=(z=g.find(I=>m(I.propsRef.current.value,w)))==null?void 0:z.propsRef.current;return k!=null&&k.disabled?!1:(S==null||S(w),!0)}),j=E(w=>{let k=A.current;if(!k)return;let z=xe(k),I=g.filter(U=>U.propsRef.current.disabled===!1).map(U=>U.element.current);switch(w.key){case"Enter":Mt(w.currentTarget);break;case"ArrowLeft":case"ArrowUp":if(w.preventDefault(),w.stopPropagation(),ye(I,18)===2){let X=g.find(B=>B.element.current===(z==null?void 0:z.activeElement));X&&N(X.propsRef.current.value)}break;case"ArrowRight":case"ArrowDown":if(w.preventDefault(),w.stopPropagation(),ye(I,20)===2){let X=g.find(B=>B.element.current===(z==null?void 0:z.activeElement));X&&N(X.propsRef.current.value)}break;case" ":{w.preventDefault(),w.stopPropagation();let U=g.find(X=>X.element.current===(z==null?void 0:z.activeElement));U&&N(U.propsRef.current.value)}break}}),_=E(w=>(b({type:0,...w}),()=>b({type:1,id:w.id}))),$=(0,de.useMemo)(()=>({value:L,firstOption:O,containsCheckedOption:C,disabled:c,compare:m,tabIndex:p,...T}),[L,O,C,c,m,p,T]),G=(0,de.useMemo)(()=>({registerOption:_,change:N}),[_,N]),ne={ref:D,id:r,role:"radiogroup","aria-labelledby":h,"aria-describedby":x,onKeyDown:j},J=(0,de.useMemo)(()=>({value:L}),[L]),R=(0,de.useCallback)(()=>{if(F!==void 0)return N(F)},[N,F]),P=H();return de.default.createElement(v,{name:"RadioGroup.Description"},de.default.createElement(y,{name:"RadioGroup.Label"},de.default.createElement($i.Provider,{value:G},de.default.createElement(Hi.Provider,{value:$},l!=null&&de.default.createElement(ut,{disabled:c,data:{[l]:L||"on"},overrides:{type:"radio",checked:L!=null},form:s,onReset:R}),P({ourProps:ne,theirProps:f,slot:J,defaultTag:Ep,name:"RadioGroup"})))))}var Pp="div";function Rp(e,n){var O;let t=ki("RadioGroup.Option"),o=Ni("RadioGroup.Option"),r=(0,Q.useId)(),{id:i=`headlessui-radiogroup-option-${r}`,value:s,disabled:l=t.disabled||!1,autoFocus:a=!1,...u}=e,c=(0,de.useRef)(null),d=K(c,n),[p,f]=_e(),[m,T]=tt(),b=pe({value:s,disabled:l});W(()=>o.registerOption({id:i,element:c,propsRef:b}),[i,o,c,b]);let g=E(C=>{var N;if(Ie(C.currentTarget))return C.preventDefault();o.change(s)&&((N=c.current)==null||N.focus())}),h=((O=t.firstOption)==null?void 0:O.id)===i,{isFocusVisible:y,focusProps:x}=ce({autoFocus:a}),{isHovered:v,hoverProps:A}=fe({isDisabled:l}),D=t.compare(t.value,s),F=se({ref:d,id:i,role:"radio","aria-checked":D?"true":"false","aria-labelledby":p,"aria-describedby":m,"aria-disabled":l?!0:void 0,tabIndex:(()=>l?-1:D||!t.containsCheckedOption&&h?t.tabIndex:-1)(),onClick:l?void 0:g,autoFocus:a},x,A),L=(0,de.useMemo)(()=>({checked:D,disabled:l,active:y,hover:v,focus:y,autofocus:a}),[D,l,v,y,a]),S=H();return de.default.createElement(T,{name:"RadioGroup.Description"},de.default.createElement(f,{name:"RadioGroup.Label"},S({ourProps:F,theirProps:u,slot:L,defaultTag:Pp,name:"RadioGroup.Option"})))}var Sp="span";function Cp(e,n){var O;let t=ki("Radio"),o=Ni("Radio"),r=(0,Q.useId)(),i=De(),s=Te(),{id:l=i||`headlessui-radio-${r}`,value:a,disabled:u=t.disabled||s||!1,autoFocus:c=!1,...d}=e,p=(0,de.useRef)(null),f=K(p,n),m=Le(),T=we(),b=pe({value:a,disabled:u});W(()=>o.registerOption({id:l,element:p,propsRef:b}),[l,o,p,b]);let g=E(C=>{var N;if(Ie(C.currentTarget))return C.preventDefault();o.change(a)&&((N=p.current)==null||N.focus())}),{isFocusVisible:h,focusProps:y}=ce({autoFocus:c}),{isHovered:x,hoverProps:v}=fe({isDisabled:u}),A=((O=t.firstOption)==null?void 0:O.id)===l,D=t.compare(t.value,a),F=se({ref:f,id:l,role:"radio","aria-checked":D?"true":"false","aria-labelledby":m,"aria-describedby":T,"aria-disabled":u?!0:void 0,tabIndex:(()=>u?-1:D||!t.containsCheckedOption&&A?t.tabIndex:-1)(),autoFocus:c,onClick:u?void 0:g},y,v),L=(0,de.useMemo)(()=>({checked:D,disabled:u,hover:x,focus:h,autofocus:c}),[D,u,x,h,c]);return H()({ourProps:F,theirProps:d,slot:L,defaultTag:Sp,name:"Radio"})}var Ap=M(xp),za=M(Rp),Xa=M(Cp),Ya=Xe,qa=xt,Op=Object.assign(Ap,{Option:za,Radio:Xa,Label:Ya,Description:qa});var Ja=require("react");var Lp="select";function Dp(e,n){let t=(0,Q.useId)(),o=De(),r=Te(),{id:i=o||`headlessui-select-${t}`,disabled:s=r||!1,invalid:l=!1,autoFocus:a=!1,...u}=e,c=Le(),d=we(),{isFocusVisible:p,focusProps:f}=ce({autoFocus:a}),{isHovered:m,hoverProps:T}=fe({isDisabled:s}),{pressed:b,pressProps:g}=Se({disabled:s}),h=se({ref:n,id:i,"aria-labelledby":c,"aria-describedby":d,"aria-invalid":l?"true":void 0,disabled:s||void 0,autoFocus:a},f,T,g),y=(0,Ja.useMemo)(()=>({disabled:s,invalid:l,hover:m,focus:p,active:b,autofocus:a}),[s,l,m,p,b,a]);return H()({ourProps:h,theirProps:u,slot:y,defaultTag:Lp,name:"Select"})}var Ip=M(Dp);var Ee=ie(require("react"),1);var Bi=(0,Ee.createContext)(null);Bi.displayName="GroupContext";var Fp=Ee.Fragment;function Mp(e){var d;let[n,t]=(0,Ee.useState)(null),[o,r]=_e(),[i,s]=tt(),l=(0,Ee.useMemo)(()=>({switch:n,setSwitch:t}),[n,t]),a={},u=e,c=H();return Ee.default.createElement(s,{name:"Switch.Description",value:i},Ee.default.createElement(r,{name:"Switch.Label",value:o,props:{htmlFor:(d=l.switch)==null?void 0:d.id,onClick(p){n&&(p.currentTarget instanceof HTMLLabelElement&&p.preventDefault(),n.click(),n.focus({preventScroll:!0}))}}},Ee.default.createElement(Bi.Provider,{value:l},c({ourProps:a,theirProps:u,slot:{},defaultTag:Fp,name:"Switch.Group"}))))}var wp="button";function _p(e,n){var U;let t=(0,Q.useId)(),o=De(),r=Te(),{id:i=o||`headlessui-switch-${t}`,disabled:s=r||!1,checked:l,defaultChecked:a,onChange:u,name:c,value:d,form:p,autoFocus:f=!1,...m}=e,T=(0,Ee.useContext)(Bi),[b,g]=(0,Ee.useState)(null),h=(0,Ee.useRef)(null),y=K(h,n,T===null?null:T.setSwitch,g),x=at(a),[v,A]=lt(l,u,x!=null?x:!1),D=Re(),[F,L]=(0,Ee.useState)(!1),S=E(()=>{L(!0),A==null||A(!v),D.nextFrame(()=>{L(!1)})}),O=E(X=>{if(Ie(X.currentTarget))return X.preventDefault();X.preventDefault(),S()}),C=E(X=>{X.key===" "?(X.preventDefault(),S()):X.key==="Enter"&&Mt(X.currentTarget)}),N=E(X=>X.preventDefault()),j=Le(),_=we(),{isFocusVisible:$,focusProps:G}=ce({autoFocus:f}),{isHovered:ne,hoverProps:J}=fe({isDisabled:s}),{pressed:R,pressProps:P}=Se({disabled:s}),w=(0,Ee.useMemo)(()=>({checked:v,disabled:s,hover:ne,focus:$,active:R,autofocus:f,changing:F}),[v,ne,$,R,s,F,f]),k=se({id:i,ref:y,role:"switch",type:$e(e,b),tabIndex:e.tabIndex===-1?0:(U=e.tabIndex)!=null?U:0,"aria-checked":v,"aria-labelledby":j,"aria-describedby":_,disabled:s||void 0,autoFocus:f,onClick:O,onKeyUp:C,onKeyPress:N},G,J,P),z=(0,Ee.useCallback)(()=>{if(x!==void 0)return A==null?void 0:A(x)},[A,x]),I=H();return Ee.default.createElement(Ee.default.Fragment,null,c!=null&&Ee.default.createElement(ut,{disabled:s,data:{[c]:d||"on"},overrides:{type:"checkbox",checked:v},form:p,onReset:z}),I({ourProps:k,theirProps:m,slot:w,defaultTag:wp,name:"Switch"}))}var Hp=M(_p),Qa=Mp,Za=Xe,eu=xt,kp=Object.assign(Hp,{Group:Qa,Label:Za,Description:eu});var me=ie(require("react"),1);var hr=ie(require("react"),1);function tu({onFocus:e}){let[n,t]=(0,hr.useState)(!0),o=sn();return n?hr.default.createElement(Fe,{as:"button",type:"button",features:2,onFocus:r=>{r.preventDefault();let i,s=50;function l(){if(s--<=0){i&&cancelAnimationFrame(i);return}if(e()){if(cancelAnimationFrame(i),!o.current)return;t(!1);return}i=requestAnimationFrame(l)}i=requestAnimationFrame(l)}}):null}var Ze=ie(require("react"),1),nu=Ze.createContext(null);function $p(){return{groups:new Map,get(e,n){var s;let t=this.groups.get(e);t||(t=new Map,this.groups.set(e,t));let o=(s=t.get(n))!=null?s:0;t.set(n,o+1);let r=Array.from(t.keys()).indexOf(n);function i(){let l=t.get(n);l>1?t.set(n,l-1):t.delete(n)}return[r,i]}}}function ou({children:e}){let n=Ze.useRef($p());return Ze.createElement(nu.Provider,{value:n},e)}function Ui(e){let n=Ze.useContext(nu);if(!n)throw new Error("You must wrap your component in a <StableCollection>");let t=Ze.useId(),[o,r]=n.current.get(e,t);return Ze.useEffect(()=>r,[]),o}var Np={[0](e,n){var c;let t=He(e.tabs,d=>d.current),o=He(e.panels,d=>d.current),r=t.filter(d=>{var p;return!((p=d.current)!=null&&p.hasAttribute("disabled"))}),i={...e,tabs:t,panels:o};if(n.index<0||n.index>t.length-1){let d=Y(Math.sign(n.index-e.selectedIndex),{[-1]:()=>1,[0]:()=>Y(Math.sign(n.index),{[-1]:()=>0,[0]:()=>0,[1]:()=>1}),[1]:()=>0});if(r.length===0)return i;let p=Y(d,{[0]:()=>t.indexOf(r[0]),[1]:()=>t.indexOf(r[r.length-1])});return{...i,selectedIndex:p===-1?e.selectedIndex:p}}let s=t.slice(0,n.index),a=[...t.slice(n.index),...s].find(d=>r.includes(d));if(!a)return i;let u=(c=t.indexOf(a))!=null?c:e.selectedIndex;return u===-1&&(u=e.selectedIndex),{...i,selectedIndex:u}},[1](e,n){if(e.tabs.includes(n.tab))return e;let t=e.tabs[e.selectedIndex],o=He([...e.tabs,n.tab],i=>i.current),r=e.selectedIndex;return e.info.current.isControlled||(r=o.indexOf(t),r===-1&&(r=e.selectedIndex)),{...e,tabs:o,selectedIndex:r}},[2](e,n){return{...e,tabs:e.tabs.filter(t=>t!==n.tab)}},[3](e,n){return e.panels.includes(n.panel)?e:{...e,panels:He([...e.panels,n.panel],t=>t.current)}},[4](e,n){return{...e,panels:e.panels.filter(t=>t!==n.panel)}}},Gi=(0,me.createContext)(null);Gi.displayName="TabsDataContext";function Bn(e){let n=(0,me.useContext)(Gi);if(n===null){let t=new Error(`<${e} /> is missing a parent <Tab.Group /> component.`);throw Error.captureStackTrace&&Error.captureStackTrace(t,Bn),t}return n}var Wi=(0,me.createContext)(null);Wi.displayName="TabsActionsContext";function Vi(e){let n=(0,me.useContext)(Wi);if(n===null){let t=new Error(`<${e} /> is missing a parent <Tab.Group /> component.`);throw Error.captureStackTrace&&Error.captureStackTrace(t,Vi),t}return n}function Bp(e,n){return Y(n.type,Np,e,n)}var Up="div";function Gp(e,n){let{defaultIndex:t=0,vertical:o=!1,manual:r=!1,onChange:i,selectedIndex:s=null,...l}=e,a=o?"vertical":"horizontal",u=r?"manual":"auto",c=s!==null,d=pe({isControlled:c}),p=K(n),[f,m]=(0,me.useReducer)(Bp,{info:d,selectedIndex:s!=null?s:t,tabs:[],panels:[]}),T=(0,me.useMemo)(()=>({selectedIndex:f.selectedIndex}),[f.selectedIndex]),b=pe(i||(()=>{})),g=pe(f.tabs),h=(0,me.useMemo)(()=>({orientation:a,activation:u,...f}),[a,u,f]),y=E(S=>(m({type:1,tab:S}),()=>m({type:2,tab:S}))),x=E(S=>(m({type:3,panel:S}),()=>m({type:4,panel:S}))),v=E(S=>{A.current!==S&&b.current(S),c||m({type:0,index:S})}),A=pe(c?e.selectedIndex:f.selectedIndex),D=(0,me.useMemo)(()=>({registerTab:y,registerPanel:x,change:v}),[]);W(()=>{m({type:0,index:s!=null?s:t})},[s]),W(()=>{if(A.current===void 0||f.tabs.length<=0)return;let S=He(f.tabs,C=>C.current);S.some((C,N)=>f.tabs[N]!==C)&&v(S.indexOf(f.tabs[A.current]))});let F={ref:p},L=H();return me.default.createElement(ou,null,me.default.createElement(Wi.Provider,{value:D},me.default.createElement(Gi.Provider,{value:h},h.tabs.length<=0&&me.default.createElement(tu,{onFocus:()=>{var S,O;for(let C of g.current)if(((S=C.current)==null?void 0:S.tabIndex)===0)return(O=C.current)==null||O.focus(),!0;return!1}}),L({ourProps:F,theirProps:l,slot:T,defaultTag:Up,name:"Tabs"}))))}var Wp="div";function Vp(e,n){let{orientation:t,selectedIndex:o}=Bn("Tab.List"),r=K(n),i=(0,me.useMemo)(()=>({selectedIndex:o}),[o]),s=e,l={ref:r,role:"tablist","aria-orientation":t};return H()({ourProps:l,theirProps:s,slot:i,defaultTag:Wp,name:"Tabs.List"})}var jp="button";function Kp(e,n){var J,R;let t=(0,Q.useId)(),{id:o=`headlessui-tabs-tab-${t}`,disabled:r=!1,autoFocus:i=!1,...s}=e,{orientation:l,activation:a,selectedIndex:u,tabs:c,panels:d}=Bn("Tab"),p=Vi("Tab"),f=Bn("Tab"),[m,T]=(0,me.useState)(null),b=(0,me.useRef)(null),g=K(b,n,T);W(()=>p.registerTab(b),[p,b]);let h=Ui("tabs"),y=c.indexOf(b);y===-1&&(y=h);let x=y===u,v=E(P=>{var k;let w=P();if(w===2&&a==="auto"){let z=(k=xe(b))==null?void 0:k.activeElement,I=f.tabs.findIndex(U=>U.current===z);I!==-1&&p.change(I)}return w}),A=E(P=>{let w=c.map(z=>z.current).filter(Boolean);if(P.key===" "||P.key==="Enter"){P.preventDefault(),P.stopPropagation(),p.change(y);return}switch(P.key){case"Home":case"PageUp":return P.preventDefault(),P.stopPropagation(),v(()=>ye(w,1));case"End":case"PageDown":return P.preventDefault(),P.stopPropagation(),v(()=>ye(w,8))}if(v(()=>Y(l,{vertical(){return P.key==="ArrowUp"?ye(w,18):P.key==="ArrowDown"?ye(w,20):0},horizontal(){return P.key==="ArrowLeft"?ye(w,18):P.key==="ArrowRight"?ye(w,20):0}}))===2)return P.preventDefault()}),D=(0,me.useRef)(!1),F=E(()=>{var P;D.current||(D.current=!0,(P=b.current)==null||P.focus({preventScroll:!0}),p.change(y),Et(()=>{D.current=!1}))}),L=E(P=>{P.preventDefault()}),{isFocusVisible:S,focusProps:O}=ce({autoFocus:i}),{isHovered:C,hoverProps:N}=fe({isDisabled:r}),{pressed:j,pressProps:_}=Se({disabled:r}),$=(0,me.useMemo)(()=>({selected:x,hover:C,active:j,focus:S,autofocus:i,disabled:r}),[x,C,S,j,i,r]),G=se({ref:g,onKeyDown:A,onMouseDown:L,onClick:F,id:o,role:"tab",type:$e(e,m),"aria-controls":(R=(J=d[y])==null?void 0:J.current)==null?void 0:R.id,"aria-selected":x,tabIndex:x?0:-1,disabled:r||void 0,autoFocus:i},O,N,_);return H()({ourProps:G,theirProps:s,slot:$,defaultTag:jp,name:"Tabs.Tab"})}var zp="div";function Xp(e,n){let{selectedIndex:t}=Bn("Tab.Panels"),o=K(n),r=(0,me.useMemo)(()=>({selectedIndex:t}),[t]),i=e,s={ref:o};return H()({ourProps:s,theirProps:i,slot:r,defaultTag:zp,name:"Tabs.Panels"})}var Yp="div",qp=3;function Jp(e,n){var x,v,A,D;let t=(0,Q.useId)(),{id:o=`headlessui-tabs-panel-${t}`,tabIndex:r=0,...i}=e,{selectedIndex:s,tabs:l,panels:a}=Bn("Tab.Panel"),u=Vi("Tab.Panel"),c=(0,me.useRef)(null),d=K(c,n);W(()=>u.registerPanel(c),[u,c]);let p=Ui("panels"),f=a.indexOf(c);f===-1&&(f=p);let m=f===s,{isFocusVisible:T,focusProps:b}=ce(),g=(0,me.useMemo)(()=>({selected:m,focus:T}),[m,T]),h=se({ref:d,id:o,role:"tabpanel","aria-labelledby":(v=(x=l[f])==null?void 0:x.current)==null?void 0:v.id,tabIndex:m?r:-1},b),y=H();return!m&&((A=i.unmount)==null||A)&&!((D=i.static)!=null&&D)?me.default.createElement(Fe,{"aria-hidden":"true",...h}):y({ourProps:h,theirProps:i,slot:g,defaultTag:Yp,features:qp,visible:m,name:"Tabs.Panel"})}var Qp=M(Kp),ru=M(Gp),iu=M(Vp),su=M(Xp),lu=M(Jp),Zp=Object.assign(Qp,{Group:ru,List:iu,Panels:su,Panel:lu});var au=require("react");var em="textarea";function tm(e,n){let t=(0,Q.useId)(),o=De(),r=Te(),{id:i=o||`headlessui-textarea-${t}`,disabled:s=r||!1,autoFocus:l=!1,invalid:a=!1,...u}=e,c=Le(),d=we(),{isFocused:p,focusProps:f}=ce({autoFocus:l}),{isHovered:m,hoverProps:T}=fe({isDisabled:s}),b=se({ref:n,id:i,"aria-labelledby":c,"aria-describedby":d,"aria-invalid":a?"true":void 0,disabled:s||void 0,autoFocus:l},f,T),g=(0,au.useMemo)(()=>({disabled:s,invalid:a,hover:m,focus:p,autofocus:l}),[s,a,m,p,l]);return H()({ourProps:b,theirProps:u,slot:g,defaultTag:em,name:"Textarea"})}var nm=M(tm);
