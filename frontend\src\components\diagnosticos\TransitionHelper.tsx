import React, { useState, useEffect } from 'react';
import { 
  <PERSON>R<PERSON>, 
  AlertTriangle, 
  CheckCircle, 
  XCircle, 
  Info, 
  RefreshCw,
  TrendingUp,
  TrendingDown,
  Minus
} from 'lucide-react';
import { useDiagnosis } from '../../hooks/useDiagnosis';
import { TransitionMapping, TransitionHelperProps } from '../../types/diagnosis.types';

export const TransitionHelper: React.FC<TransitionHelperProps> = ({
  cie10Code,
  onMappingSelect,
  showAlternatives = true,
  showConfidence = true,
  className = ''
}) => {
  const [mapping, setMapping] = useState<TransitionMapping | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const { mapCie10ToCie11 } = useDiagnosis();

  // Cargar mapeo cuando cambie el código
  useEffect(() => {
    if (cie10Code) {
      loadMapping(cie10Code);
    } else {
      setMapping(null);
      setError(null);
    }
  }, [cie10Code]);

  const loadMapping = async (code: string) => {
    setIsLoading(true);
    setError(null);
    
    try {
      const result = await mapCie10ToCie11(code);
      setMapping(result);
    } catch (err: any) {
      setError(err.message || 'Error al obtener mapeo');
      setMapping(null);
    } finally {
      setIsLoading(false);
    }
  };

  // Renderizar indicador de confianza
  const renderConfidenceIndicator = (confidence: number) => {
    const percentage = Math.round(confidence * 100);
    let color = 'text-error';
    let icon = TrendingDown;

    if (confidence >= 0.8) {
      color = 'text-success';
      icon = TrendingUp;
    } else if (confidence >= 0.6) {
      color = 'text-warning';
      icon = Minus;
    }

    const Icon = icon;

    return (
      <div className={`flex items-center gap-1 ${color}`}>
        <Icon className="h-4 w-4" />
        <span className="text-sm font-medium">{percentage}%</span>
      </div>
    );
  };

  // Renderizar tipo de mapeo
  const renderMappingType = (type: TransitionMapping['mappingType']) => {
    const config = {
      exact: {
        icon: CheckCircle,
        color: 'text-success bg-success/10',
        label: 'Mapeo Exacto'
      },
      approximate: {
        icon: AlertTriangle,
        color: 'text-warning bg-warning/10',
        label: 'Mapeo Aproximado'
      },
      multiple: {
        icon: Info,
        color: 'text-info bg-info/10',
        label: 'Múltiples Opciones'
      },
      none: {
        icon: XCircle,
        color: 'text-error bg-error/10',
        label: 'Sin Mapeo'
      }
    };

    const { icon: Icon, color, label } = config[type];

    return (
      <div className={`inline-flex items-center gap-2 px-3 py-1 rounded-full ${color}`}>
        <Icon className="h-4 w-4" />
        <span className="text-sm font-medium">{label}</span>
      </div>
    );
  };

  // Renderizar alternativas
  const renderAlternatives = (alternatives: TransitionMapping['alternatives']) => {
    if (!alternatives || alternatives.length === 0) return null;

    return (
      <div className="mt-4">
        <h4 className="text-sm font-semibold text-text-primary mb-2">
          Alternativas Sugeridas:
        </h4>
        <div className="space-y-2">
          {alternatives.map((alt, index) => (
            <div
              key={index}
              className="flex items-center justify-between p-3 bg-bg-secondary rounded-lg border border-border hover:border-primary/50 transition-colors cursor-pointer"
              onClick={() => onMappingSelect?.({
                ...mapping!,
                cie11Code: alt.code,
                cie11Title: alt.title,
                confidence: alt.confidence
              })}
            >
              <div className="flex-1">
                <div className="flex items-center gap-2 mb-1">
                  <span className="font-semibold text-sm">{alt.code}</span>
                  {showConfidence && renderConfidenceIndicator(alt.confidence)}
                </div>
                <p className="text-sm text-text-secondary">{alt.title}</p>
              </div>
              <ArrowRight className="h-4 w-4 text-muted" />
            </div>
          ))}
        </div>
      </div>
    );
  };

  if (!cie10Code) {
    return (
      <div className={`p-4 text-center text-muted ${className}`}>
        <Info className="h-8 w-8 mx-auto mb-2 opacity-50" />
        <p>Ingrese un código CIE-10 para ver el mapeo a CIE-11</p>
      </div>
    );
  }

  if (isLoading) {
    return (
      <div className={`p-6 text-center ${className}`}>
        <RefreshCw className="h-8 w-8 mx-auto mb-3 animate-spin text-primary" />
        <p className="text-text-secondary">Buscando mapeo CIE-10 → CIE-11...</p>
      </div>
    );
  }

  if (error) {
    return (
      <div className={`p-4 bg-error/10 border border-error/20 rounded-lg ${className}`}>
        <div className="flex items-center gap-2 mb-2">
          <XCircle className="h-5 w-5 text-error" />
          <h3 className="font-semibold text-error">Error de Mapeo</h3>
        </div>
        <p className="text-sm text-error/80">{error}</p>
        <button
          onClick={() => loadMapping(cie10Code)}
          className="mt-3 px-3 py-1 bg-error text-white rounded text-sm hover:bg-error/90 transition-colors"
        >
          Reintentar
        </button>
      </div>
    );
  }

  if (!mapping) {
    return (
      <div className={`p-4 bg-warning/10 border border-warning/20 rounded-lg ${className}`}>
        <div className="flex items-center gap-2 mb-2">
          <AlertTriangle className="h-5 w-5 text-warning" />
          <h3 className="font-semibold text-warning">Mapeo No Disponible</h3>
        </div>
        <p className="text-sm text-warning/80">
          No se encontró mapeo para el código CIE-10: <strong>{cie10Code}</strong>
        </p>
      </div>
    );
  }

  return (
    <div className={`bg-bg-card border border-border rounded-lg p-4 ${className}`}>
      {/* Header */}
      <div className="flex items-center justify-between mb-4">
        <h3 className="text-lg font-semibold text-text-primary">
          Transición CIE-10 → CIE-11
        </h3>
        {renderMappingType(mapping.mappingType)}
      </div>

      {/* Mapeo principal */}
      <div className="bg-bg-secondary rounded-lg p-4 mb-4">
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4 items-center">
          {/* CIE-10 */}
          <div className="text-center">
            <div className="bg-accent/20 text-accent px-3 py-1 rounded-full text-sm font-medium mb-2 inline-block">
              CIE-10
            </div>
            <div className="font-semibold text-lg">{mapping.cie10Code}</div>
            <p className="text-sm text-text-secondary mt-1">{mapping.cie10Title}</p>
          </div>

          {/* Flecha */}
          <div className="flex justify-center">
            <ArrowRight className="h-8 w-8 text-primary" />
          </div>

          {/* CIE-11 */}
          <div className="text-center">
            <div className="bg-secondary/20 text-secondary px-3 py-1 rounded-full text-sm font-medium mb-2 inline-block">
              CIE-11
            </div>
            {mapping.cie11Code ? (
              <>
                <div className="font-semibold text-lg">{mapping.cie11Code}</div>
                <p className="text-sm text-text-secondary mt-1">{mapping.cie11Title}</p>
              </>
            ) : (
              <div className="text-muted">Sin mapeo directo</div>
            )}
          </div>
        </div>

        {/* Confianza */}
        {showConfidence && mapping.confidence > 0 && (
          <div className="flex items-center justify-center mt-4 pt-4 border-t border-border">
            <div className="flex items-center gap-2">
              <span className="text-sm text-text-secondary">Confianza:</span>
              {renderConfidenceIndicator(mapping.confidence)}
            </div>
          </div>
        )}
      </div>

      {/* Notas */}
      {mapping.notes && (
        <div className="bg-info/10 border border-info/20 rounded-lg p-3 mb-4">
          <div className="flex items-start gap-2">
            <Info className="h-4 w-4 text-info mt-0.5 flex-shrink-0" />
            <div>
              <h4 className="font-medium text-info mb-1">Notas del Mapeo</h4>
              <p className="text-sm text-info/80">{mapping.notes}</p>
            </div>
          </div>
        </div>
      )}

      {/* Alternativas */}
      {showAlternatives && mapping.alternatives && renderAlternatives(mapping.alternatives)}

      {/* Acciones */}
      <div className="flex gap-2 mt-4">
        {mapping.cie11Code && (
          <button
            onClick={() => onMappingSelect?.(mapping)}
            className="flex-1 bg-primary text-white px-4 py-2 rounded-lg hover:bg-primary-dark transition-colors font-medium"
          >
            Usar Mapeo CIE-11
          </button>
        )}
        <button
          onClick={() => loadMapping(cie10Code)}
          className="px-4 py-2 border border-border rounded-lg hover:bg-border/50 transition-colors"
          title="Actualizar mapeo"
        >
          <RefreshCw className="h-4 w-4" />
        </button>
      </div>

      {/* Información adicional */}
      <div className="mt-4 pt-4 border-t border-border">
        <div className="grid grid-cols-2 gap-4 text-sm">
          <div>
            <span className="text-text-secondary">Tipo de mapeo:</span>
            <span className="ml-2 font-medium">{mapping.mappingType}</span>
          </div>
          <div>
            <span className="text-text-secondary">Confianza:</span>
            <span className="ml-2 font-medium">{Math.round(mapping.confidence * 100)}%</span>
          </div>
        </div>
      </div>
    </div>
  );
};

export default TransitionHelper;
