import { CSSProperties } from 'react';

export interface FooterProps {
  glassStyle?: CSSProperties;
}

export const Footer = ({ glassStyle }: FooterProps = {}) => {
  return (
    <footer
      className="glass-footer py-4 px-6 flex-shrink-0"
      style={glassStyle}
    >
      <div className="flex flex-col sm:flex-row justify-between items-center">
        <div className="flex items-center space-x-4 mb-2 sm:mb-0">
          <div className="flex items-center space-x-2">
            <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
            <span className="text-sm text-adaptive-muted">Sistema Activo</span>
          </div>
          <div className="hidden sm:flex items-center space-x-2">
            <svg className="w-3 h-3 text-red-500 animate-pulse" fill="currentColor" viewBox="0 0 20 20">
              <path fillRule="evenodd" d="M3.172 5.172a4 4 0 015.656 0L10 6.343l1.172-1.171a4 4 0 115.656 5.656L10 17.657l-6.828-6.829a4 4 0 010-5.656z" clipRule="evenodd" />
            </svg>
            <span className="text-xs text-adaptive-subtle">Desarrollado con ❤️ para la salud</span>
          </div>
        </div>
        <div className="flex items-center space-x-4 text-xs text-adaptive-subtle">
          <span>&copy; {new Date().getFullYear()} Sistema Hipócrates</span>
          <span className="hidden sm:inline">v2.1.0</span>
          <div className="flex items-center space-x-1">
            <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
            <span>Estable</span>
          </div>
        </div>
      </div>
    </footer>
  );
};
