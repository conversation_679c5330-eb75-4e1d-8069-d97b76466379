import { useState, useRef, useEffect, CSSProperties } from 'react';
import { useNavigate } from 'react-router-dom';
// import { Bell, Search, Settings } from 'lucide-react';
import { ThemeToggle } from '../ui/ThemeToggle';
import { UICustomizer } from '../ui/UICustomizer';
import { useTheme } from '../../hooks/useTheme';
// import { useAuth } from '../../context/AuthContext';

// Iconos SVG temporales hasta que lucide-react funcione
const SearchIcon = () => (
  <svg className="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
  </svg>
);

const BellIcon = () => (
  <svg className="h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M15 17h5l-1.405-1.405A2.032 2.032 0 0118 14.158V11a6.002 6.002 0 00-4-5.659V5a2 2 0 10-4 0v.341C7.67 6.165 6 8.388 6 11v3.159c0 .538-.214 1.055-.595 1.436L4 17h5m6 0v1a3 3 0 11-6 0v-1m6 0H9" />
  </svg>
);

const SettingsIcon = () => (
  <svg className="h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z" />
    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
  </svg>
);

// Icono discreto para personalización de UI
const PaletteIcon = () => (
  <svg className="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M7 21a4 4 0 01-4-4V5a2 2 0 012-2h4a2 2 0 012 2v12a4 4 0 01-4 4zM7 3H5a2 2 0 00-2 2v12a4 4 0 004 4h2a2 2 0 002-2V5a2 2 0 00-2-2z" />
    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 9h6m-6 4h6m2-7h4a2 2 0 012 2v8a2 2 0 01-2 2h-4" />
  </svg>
);

export interface HeaderProps {
  glassStyle?: CSSProperties;
  onToggleSidebar?: () => void;
}

export const Header = ({ glassStyle, onToggleSidebar }: HeaderProps) => {
  // Temporalmente, no usar el contexto de autenticación para depuración
  // const { user, logout } = useAuth();
  const user = { username: 'Usuario de prueba', hospital_id: '1' };
  const logout = () => console.log('Logout');
  const navigate = useNavigate();
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const [isSearchOpen, setIsSearchOpen] = useState(false);
  const menuRef = useRef<HTMLDivElement>(null);
  const { isDark } = useTheme();

  // Cerrar el menú cuando se hace clic fuera de él
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (menuRef.current && !menuRef.current.contains(event.target as Node)) {
        setIsMenuOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  // Manejar clic en cambiar contraseña
  const handleChangePassword = () => {
    setIsMenuOpen(false);
    navigate('/perfil/cambiar-password');
  };

  // Manejar clic en cerrar sesión
  const handleLogout = () => {
    setIsMenuOpen(false);
    logout();
    navigate('/login');
  };

  return (
    <header
      className="glass-header h-16 flex items-center justify-between px-6 w-full fixed top-0 left-0 right-0 z-50"
      style={glassStyle}
    >
      {/* Logo y título con animación */}
      <div className="flex items-center space-x-4">
        {/* Botón hamburguesa para móviles */}
        <button
          onClick={onToggleSidebar}
          className="lg:hidden glass-card p-2 rounded-xl text-adaptive-muted hover:text-adaptive transition-all duration-200 transform hover:scale-105"
        >
          <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M4 6h16M4 12h16M4 18h16" />
          </svg>
        </button>

        <div className="flex items-center space-x-3">
          <div className="relative">
            <div className="w-10 h-10 bg-gradient-to-br from-blue-500 via-purple-600 to-indigo-700 rounded-xl flex items-center justify-center shadow-lg float-animation">
              <span className="text-white font-bold text-lg">H</span>
            </div>
            <div className="absolute -top-1 -right-1 w-3 h-3 bg-green-400 rounded-full animate-pulse shadow-sm"></div>
          </div>
          <div>
            <h1 className="text-adaptive text-xl font-bold tracking-tight">Sistema Hipócrates</h1>
            <p className="text-adaptive-subtle text-xs">Gestión Hospitalaria Integral</p>
          </div>
        </div>
      </div>

      {/* Barra de búsqueda central con glassmorphism */}
      <div className="flex-1 max-w-lg mx-8">
        <div className="relative">
          <div className="absolute left-4 top-1/2 transform -translate-y-1/2 text-adaptive-muted">
            <SearchIcon />
          </div>
          <input
            type="text"
            placeholder="Buscar pacientes, citas, diagnósticos..."
            className="
              w-full pl-12 pr-4 py-3 glass-card text-adaptive placeholder:text-adaptive-subtle
              focus:outline-none focus:ring-2 focus:ring-blue-400/50 focus:border-transparent
              transition-all duration-300 hover:shadow-lg
            "
            onFocus={() => setIsSearchOpen(true)}
            onBlur={() => setIsSearchOpen(false)}
          />
          {isSearchOpen && (
            <div className="absolute top-full left-0 right-0 mt-2 glass-modal p-4 z-50 animate-in slide-in-from-top-2 duration-200">
              <div className="space-y-3">
                <div className="text-sm text-adaptive-muted">
                  Búsquedas recientes:
                </div>
                <div className="space-y-2">
                  <div className="flex items-center space-x-3 p-2 rounded-lg hover:bg-white/10 dark:hover:bg-black/10 cursor-pointer transition-colors">
                    <SearchIcon />
                    <span className="text-adaptive-muted text-sm">Paciente: María González</span>
                  </div>
                  <div className="flex items-center space-x-3 p-2 rounded-lg hover:bg-white/10 dark:hover:bg-black/10 cursor-pointer transition-colors">
                    <SearchIcon />
                    <span className="text-adaptive-muted text-sm">Diagnóstico: Hipertensión</span>
                  </div>
                </div>
              </div>
            </div>
          )}
        </div>
      </div>

      {/* Acciones del header con glassmorphism */}
      <div className="flex items-center space-x-3">
        {/* Theme Toggle */}
        <div className="glass-card p-2 rounded-xl">
          <ThemeToggle size="md" variant="button" />
        </div>

        {/* Personalización UI */}
        <div className="glass-card p-2 rounded-xl">
          <UICustomizer />
        </div>

        {/* Notificaciones con glassmorphism */}
        <div className="relative">
          <button className="
            glass-card p-3 rounded-xl text-adaptive-muted
            hover:text-adaptive hover:shadow-lg focus:outline-none focus:ring-2 focus:ring-blue-400/50
            transition-all duration-300 transform hover:scale-105
          ">
            <BellIcon />
          </button>
          <span className="absolute -top-1 -right-1 w-5 h-5 bg-gradient-to-r from-red-500 to-pink-500 rounded-full flex items-center justify-center shadow-lg animate-pulse">
            <span className="text-white text-xs font-bold">3</span>
          </span>
        </div>

        {/* Configuraciones con glassmorphism */}
        <button className="
          glass-card p-3 rounded-xl text-adaptive-muted
          hover:text-adaptive hover:shadow-lg focus:outline-none focus:ring-2 focus:ring-blue-400/50
          transition-all duration-300 transform hover:scale-105
        ">
          <SettingsIcon />
        </button>

        {/* Menú de usuario con glassmorphism */}
        <div className="relative" ref={menuRef}>
          <button
            onClick={() => setIsMenuOpen(!isMenuOpen)}
            className="glass-card flex items-center space-x-3 p-3 rounded-xl hover:shadow-lg focus:outline-none focus:ring-2 focus:ring-blue-400/50 transition-all duration-300 transform hover:scale-105"
          >
            <div className="w-10 h-10 rounded-full bg-gradient-to-br from-blue-500 via-purple-600 to-indigo-700 flex items-center justify-center text-white font-bold shadow-lg">
              {user?.username.charAt(0).toUpperCase()}
            </div>
            <div className="hidden md:block text-left">
              <p className="text-adaptive font-semibold text-sm">{user?.username}</p>
              <p className="text-adaptive-subtle text-xs">Hospital ID: {user?.hospital_id}</p>
            </div>
            <svg
              className={`w-4 h-4 text-adaptive-muted transition-transform duration-300 ${isMenuOpen ? 'rotate-180' : ''}`}
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M19 9l-7 7-7-7" />
            </svg>
          </button>

          {isMenuOpen && (
            <div className="absolute right-0 mt-3 w-64 glass-modal py-3 z-50 animate-in slide-in-from-top-2 duration-200">
              <div className="px-4 py-3 border-b border-white/10 dark:border-black/10">
                <div className="flex items-center space-x-3">
                  <div className="w-12 h-12 rounded-full bg-gradient-to-br from-blue-500 via-purple-600 to-indigo-700 flex items-center justify-center text-white font-bold shadow-lg">
                    {user?.username.charAt(0).toUpperCase()}
                  </div>
                  <div>
                    <p className="text-adaptive font-semibold">{user?.username}</p>
                    <p className="text-adaptive-subtle text-sm">Hospital ID: {user?.hospital_id}</p>
                  </div>
                </div>
              </div>

              <div className="py-2">
                <button
                  onClick={() => {
                    setIsMenuOpen(false);
                    navigate('/perfil');
                  }}
                  className="w-full text-left px-4 py-3 text-sm text-adaptive hover:bg-white/10 dark:hover:bg-black/10 transition-colors duration-200 flex items-center space-x-3"
                >
                  <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                  </svg>
                  <span>Ver Perfil</span>
                </button>

                <button
                  onClick={handleChangePassword}
                  className="w-full text-left px-4 py-3 text-sm text-adaptive hover:bg-white/10 dark:hover:bg-black/10 transition-colors duration-200 flex items-center space-x-3"
                >
                  <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M15 7a2 2 0 012 2m4 0a6 6 0 01-7.743 5.743L11 17H9v2H7v2H4a1 1 0 01-1-1v-2.586a1 1 0 01.293-.707l5.964-5.964A6 6 0 1121 9z" />
                  </svg>
                  <span>Cambiar Contraseña</span>
                </button>

                <div className="border-t border-white/10 dark:border-black/10 my-2"></div>

                <button
                  onClick={handleLogout}
                  className="w-full text-left px-4 py-3 text-sm text-red-400 hover:bg-red-500/10 transition-colors duration-200 flex items-center space-x-3"
                >
                  <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1" />
                  </svg>
                  <span>Cerrar Sesión</span>
                </button>
              </div>
            </div>
          )}
        </div>
      </div>
    </header>
  );
};
