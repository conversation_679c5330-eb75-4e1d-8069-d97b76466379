import React from 'react';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import {
  faUsers,
  faUserCheck,
  faUserTimes,
  faPlane,
  faHeart,
  faMoneyBillWave,
  faChartLine,
  faCalendarAlt,
  faExclamationTriangle,
  faCheckCircle,
  faClock,
  faArrowUp,
  faArrowDown,
  faPercent,
  faBriefcase,
  faSitemap,
  faGraduationCap,
  faStarHalfAlt,
  faFileContract,
  faTools
} from '@fortawesome/free-solid-svg-icons';
import { ResumenRecursosHumanos } from '../../../types/recursosHumanos';

interface DashboardRRHHProps {
  resumen?: ResumenRecursosHumanos;
  isLoading: boolean;
}

const DashboardRRHH: React.FC<DashboardRRHHProps> = ({ resumen, isLoading }) => {
  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary"></div>
      </div>
    );
  }

  if (!resumen) {
    return (
      <div className="text-center py-12">
        <FontAwesomeIcon icon={faExclamationTriangle} className="text-6xl text-warning mb-4" />
        <h3 className="text-xl font-semibold text-primary mb-2">No hay datos disponibles</h3>
        <p className="text-muted">No se pudieron cargar los datos del dashboard</p>
      </div>
    );
  }

  const formatCurrency = (value: number) => {
    return new Intl.NumberFormat('es-CO', {
      style: 'currency',
      currency: 'COP',
      minimumFractionDigits: 0
    }).format(value);
  };

  const getPercentage = (value: number, total: number) => {
    return total > 0 ? ((value / total) * 100).toFixed(1) : '0';
  };

  return (
    <div className="space-y-6">
      {/* KPIs Principales */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <div className="bg-card p-6 rounded-lg shadow-lg">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-muted">Total Empleados</p>
              <p className="text-3xl font-bold text-primary">{resumen.total_empleados}</p>
              <p className="text-sm text-success flex items-center mt-1">
                <FontAwesomeIcon icon={faArrowUp} className="mr-1" />
                +5.2% vs mes anterior
              </p>
            </div>
            <div className="bg-primary/20 p-3 rounded-full">
              <FontAwesomeIcon icon={faUsers} className="text-primary text-2xl" />
            </div>
          </div>
        </div>

        <div className="bg-card p-6 rounded-lg shadow-lg">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-muted">Empleados Activos</p>
              <p className="text-3xl font-bold text-success">{resumen.empleados_activos}</p>
              <p className="text-sm text-success flex items-center mt-1">
                <FontAwesomeIcon icon={faCheckCircle} className="mr-1" />
                {getPercentage(resumen.empleados_activos, resumen.total_empleados)}%
              </p>
            </div>
            <div className="bg-success/20 p-3 rounded-full">
              <FontAwesomeIcon icon={faUserCheck} className="text-success text-2xl" />
            </div>
          </div>
        </div>

        <div className="bg-card p-6 rounded-lg shadow-lg">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-muted">En Vacaciones</p>
              <p className="text-3xl font-bold text-warning">{resumen.empleados_vacaciones}</p>
              <p className="text-sm text-warning flex items-center mt-1">
                <FontAwesomeIcon icon={faPlane} className="mr-1" />
                {getPercentage(resumen.empleados_vacaciones, resumen.total_empleados)}%
              </p>
            </div>
            <div className="bg-warning/20 p-3 rounded-full">
              <FontAwesomeIcon icon={faPlane} className="text-warning text-2xl" />
            </div>
          </div>
        </div>

        <div className="bg-card p-6 rounded-lg shadow-lg">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-muted">En Licencia</p>
              <p className="text-3xl font-bold text-info">{resumen.empleados_licencia}</p>
              <p className="text-sm text-info flex items-center mt-1">
                <FontAwesomeIcon icon={faHeart} className="mr-1" />
                {getPercentage(resumen.empleados_licencia, resumen.total_empleados)}%
              </p>
            </div>
            <div className="bg-info/20 p-3 rounded-full">
              <FontAwesomeIcon icon={faHeart} className="text-info text-2xl" />
            </div>
          </div>
        </div>
      </div>

      {/* Distribución por Departamento */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <div className="bg-card p-6 rounded-lg shadow-lg">
          <h3 className="text-lg font-semibold text-primary mb-4 flex items-center">
            <FontAwesomeIcon icon={faSitemap} className="mr-2" />
            📊 Distribución por Departamento
          </h3>
          
          <div className="space-y-3">
            {Object.entries(resumen.por_departamento).map(([departamento, cantidad], index) => (
              <div key={departamento} className="flex items-center justify-between p-3 bg-secondary/20 rounded-lg">
                <div className="flex items-center">
                  <div className="w-3 h-3 rounded-full mr-3" style={{
                    backgroundColor: ['#3B82F6', '#10B981', '#F59E0B', '#EF4444', '#8B5CF6', '#06B6D4'][index % 6]
                  }}></div>
                  <span className="font-medium text-primary">{departamento}</span>
                </div>
                <div className="text-right">
                  <div className="text-lg font-bold text-primary">{cantidad}</div>
                  <div className="text-sm text-muted">
                    {getPercentage(cantidad, resumen.total_empleados)}%
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>

        <div className="bg-card p-6 rounded-lg shadow-lg">
          <h3 className="text-lg font-semibold text-primary mb-4 flex items-center">
            <FontAwesomeIcon icon={faBriefcase} className="mr-2" />
            💼 Top Cargos
          </h3>
          
          <div className="space-y-3">
            {Object.entries(resumen.por_cargo)
              .sort(([,a], [,b]) => b - a)
              .slice(0, 6)
              .map(([cargo, cantidad], index) => (
              <div key={cargo} className="flex items-center justify-between p-3 bg-secondary/20 rounded-lg">
                <div className="flex items-center">
                  <div className="bg-primary text-white rounded-full w-6 h-6 flex items-center justify-center text-sm font-bold mr-3">
                    {index + 1}
                  </div>
                  <span className="font-medium text-primary">{cargo}</span>
                </div>
                <div className="text-right">
                  <div className="text-lg font-bold text-primary">{cantidad}</div>
                  <div className="text-sm text-muted">empleados</div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* Tipos de Contrato y Nómina */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <div className="bg-card p-6 rounded-lg shadow-lg">
          <h3 className="text-lg font-semibold text-primary mb-4 flex items-center">
            <FontAwesomeIcon icon={faFileContract} className="mr-2" />
            📋 Tipos de Contrato
          </h3>
          
          <div className="space-y-4">
            {Object.entries(resumen.por_tipo_contrato).map(([tipo, cantidad], index) => (
              <div key={tipo} className="flex items-center justify-between p-3 bg-secondary/20 rounded-lg">
                <div className="flex items-center">
                  <div className="w-3 h-3 rounded-full mr-3" style={{
                    backgroundColor: ['#10B981', '#F59E0B', '#EF4444', '#8B5CF6'][index % 4]
                  }}></div>
                  <span className="font-medium text-primary">{tipo}</span>
                </div>
                <div className="text-right">
                  <div className="text-lg font-bold text-primary">{cantidad}</div>
                  <div className="text-sm text-muted">
                    {getPercentage(cantidad, resumen.total_empleados)}%
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>

        <div className="bg-card p-6 rounded-lg shadow-lg">
          <h3 className="text-lg font-semibold text-primary mb-4 flex items-center">
            <FontAwesomeIcon icon={faMoneyBillWave} className="mr-2" />
            💰 Estado de Nómina
          </h3>
          
          {resumen.nomina_actual ? (
            <div className="space-y-4">
              <div className="bg-success/20 border border-success/30 rounded-lg p-4">
                <div className="flex items-center justify-between mb-2">
                  <span className="font-medium text-success">Período Actual</span>
                  <FontAwesomeIcon icon={faCheckCircle} className="text-success" />
                </div>
                <div className="text-sm text-muted mb-2">{resumen.nomina_actual.periodo}</div>
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <div className="text-xs text-muted">Total Bruto</div>
                    <div className="text-lg font-bold text-primary">
                      {formatCurrency(resumen.nomina_actual.total_bruto)}
                    </div>
                  </div>
                  <div>
                    <div className="text-xs text-muted">Total Neto</div>
                    <div className="text-lg font-bold text-success">
                      {formatCurrency(resumen.nomina_actual.total_neto)}
                    </div>
                  </div>
                </div>
                <div className="mt-2">
                  <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                    resumen.nomina_actual.estado === 'Pagada' ? 'bg-success/20 text-success' :
                    resumen.nomina_actual.estado === 'Aprobada' ? 'bg-info/20 text-info' :
                    'bg-warning/20 text-warning'
                  }`}>
                    {resumen.nomina_actual.estado}
                  </span>
                </div>
              </div>
            </div>
          ) : (
            <div className="bg-warning/20 border border-warning/30 rounded-lg p-4">
              <div className="flex items-center">
                <FontAwesomeIcon icon={faExclamationTriangle} className="text-warning mr-2" />
                <span className="font-medium text-warning">No hay nómina procesada</span>
              </div>
              <p className="text-sm text-muted mt-2">
                No se ha procesado la nómina para el período actual
              </p>
            </div>
          )}
        </div>
      </div>

      {/* Indicadores Adicionales */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <div className="bg-card p-6 rounded-lg shadow-lg">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-muted">Departamentos</p>
              <p className="text-2xl font-bold text-primary">{Object.keys(resumen.por_departamento).length}</p>
              <p className="text-sm text-muted">Activos</p>
            </div>
            <FontAwesomeIcon icon={faSitemap} className="text-primary text-3xl" />
          </div>
        </div>

        <div className="bg-card p-6 rounded-lg shadow-lg">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-muted">Cargos</p>
              <p className="text-2xl font-bold text-secondary">{Object.keys(resumen.por_cargo).length}</p>
              <p className="text-sm text-muted">Diferentes</p>
            </div>
            <FontAwesomeIcon icon={faBriefcase} className="text-secondary text-3xl" />
          </div>
        </div>

        <div className="bg-card p-6 rounded-lg shadow-lg">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-muted">Rotación</p>
              <p className="text-2xl font-bold text-info">8.5%</p>
              <p className="text-sm text-muted">Anual</p>
            </div>
            <FontAwesomeIcon icon={faChartLine} className="text-info text-3xl" />
          </div>
        </div>

        <div className="bg-card p-6 rounded-lg shadow-lg">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-muted">Satisfacción</p>
              <p className="text-2xl font-bold text-success">87%</p>
              <p className="text-sm text-muted">Promedio</p>
            </div>
            <FontAwesomeIcon icon={faStarHalfAlt} className="text-success text-3xl" />
          </div>
        </div>
      </div>

      {/* Alertas y Notificaciones */}
      <div className="bg-card p-6 rounded-lg shadow-lg">
        <h3 className="text-lg font-semibold text-primary mb-4 flex items-center">
          <FontAwesomeIcon icon={faExclamationTriangle} className="mr-2" />
          🚨 Alertas y Notificaciones
        </h3>
        
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          <div className="bg-warning/20 border border-warning/30 rounded-lg p-4">
            <div className="flex items-center mb-2">
              <FontAwesomeIcon icon={faCalendarAlt} className="text-warning mr-2" />
              <span className="font-medium text-warning">Vacaciones Pendientes</span>
            </div>
            <p className="text-sm text-muted">23 empleados tienen vacaciones pendientes por tomar</p>
          </div>

          <div className="bg-info/20 border border-info/30 rounded-lg p-4">
            <div className="flex items-center mb-2">
              <FontAwesomeIcon icon={faGraduationCap} className="text-info mr-2" />
              <span className="font-medium text-info">Capacitaciones</span>
            </div>
            <p className="text-sm text-muted">15 empleados requieren capacitación obligatoria</p>
          </div>

          <div className="bg-error/20 border border-error/30 rounded-lg p-4">
            <div className="flex items-center mb-2">
              <FontAwesomeIcon icon={faFileContract} className="text-error mr-2" />
              <span className="font-medium text-error">Contratos por Vencer</span>
            </div>
            <p className="text-sm text-muted">8 contratos vencen en los próximos 30 días</p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default DashboardRRHH;
