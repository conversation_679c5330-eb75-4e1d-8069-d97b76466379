import React, { useState } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { Button } from '../../components/ui/Button';
import { Input } from '../../components/ui/Input';
import { toast } from 'react-hot-toast';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { 
  faVideo, 
  faCalendarAlt, 
  faDownload, 
  faFilter,
  faChartBar,
  faFileExcel,
  faFilePdf,
  faSearch,
  faUserMd,
  faClipboardList,
  faClock,
  faCheckCircle,
  faTimesCircle
} from '@fortawesome/free-solid-svg-icons';

// Schema de validación para filtros de reporte
const reporteSchema = z.object({
  fecha_inicio: z.string().min(1, 'Fecha de inicio requerida'),
  fecha_fin: z.string().min(1, '<PERSON>cha de fin requerida'),
  tipo_reporte: z.enum(['general', 'por_profesional', 'por_especialidad', 'duracion_promedio', 'calidad_conexion'], {
    required_error: 'Debe seleccionar un tipo de reporte'
  }),
  profesional_id: z.string().optional(),
  especialidad: z.string().optional(),
  estado_teleconsulta: z.enum(['todas', 'programadas', 'en_progreso', 'finalizadas', 'canceladas']).default('todas'),
  plataforma: z.enum(['todas', 'zoom', 'teams', 'meet', 'webex', 'jitsi']).default('todas'),
  formato_exportacion: z.enum(['excel', 'pdf', 'csv']).default('excel'),
});

type ReporteFormData = z.infer<typeof reporteSchema>;

// Datos mock para el reporte
const datosTeleconsultas = [
  {
    id: 1,
    paciente: 'Juan Pérez',
    documento: '12345678',
    profesional: 'Dr. García',
    especialidad: 'Medicina General',
    fecha_programada: '2024-01-15T10:00:00',
    fecha_inicio: '2024-01-15T10:05:00',
    fecha_fin: '2024-01-15T10:35:00',
    duracion_minutos: 30,
    plataforma: 'Zoom',
    calidad_conexion: 'Excelente',
    estado: 'Finalizada',
    motivo: 'Control de hipertensión'
  },
  {
    id: 2,
    paciente: 'María González',
    documento: '87654321',
    profesional: 'Dra. Rodríguez',
    especialidad: 'Cardiología',
    fecha_programada: '2024-01-18T14:00:00',
    fecha_inicio: '2024-01-18T14:02:00',
    fecha_fin: '2024-01-18T14:47:00',
    duracion_minutos: 45,
    plataforma: 'Teams',
    calidad_conexion: 'Buena',
    estado: 'Finalizada',
    motivo: 'Consulta de seguimiento post-infarto'
  },
  {
    id: 3,
    paciente: 'Carlos López',
    documento: '11223344',
    profesional: 'Dr. Martínez',
    especialidad: 'Pediatría',
    fecha_programada: '2024-01-20T16:00:00',
    fecha_inicio: null,
    fecha_fin: null,
    duracion_minutos: 0,
    plataforma: 'Meet',
    calidad_conexion: null,
    estado: 'Programada',
    motivo: 'Control de crecimiento y desarrollo'
  }
];

const estadisticas = {
  total_teleconsultas: 245,
  teleconsultas_finalizadas: 198,
  teleconsultas_programadas: 32,
  teleconsultas_canceladas: 15,
  duracion_promedio: 32.5,
  satisfaccion_promedio: 4.2,
  ahorro_tiempo_promedio: 45, // minutos
  ahorro_costos_promedio: 85000 // pesos
};

export const ReportesTeleconsultas: React.FC = () => {
  const [isGenerating, setIsGenerating] = useState(false);
  const [reporteGenerado, setReporteGenerado] = useState(false);
  const [datosReporte, setDatosReporte] = useState(datosTeleconsultas);

  const {
    register,
    handleSubmit,
    formState: { errors },
    watch
  } = useForm<ReporteFormData>({
    resolver: zodResolver(reporteSchema),
    defaultValues: {
      fecha_inicio: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
      fecha_fin: new Date().toISOString().split('T')[0],
      tipo_reporte: 'general',
      estado_teleconsulta: 'todas',
      plataforma: 'todas',
      formato_exportacion: 'excel'
    }
  });

  const tipoReporte = watch('tipo_reporte');
  const formatoExportacion = watch('formato_exportacion');

  const onSubmit = async (data: ReporteFormData) => {
    setIsGenerating(true);
    try {
      console.log('Generando reporte de teleconsultas con filtros:', data);
      
      // Simular generación de reporte
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      // Filtrar datos según criterios
      let datosFiltrados = datosTeleconsultas;
      
      if (data.estado_teleconsulta !== 'todas') {
        const estadoMap = {
          'programadas': 'Programada',
          'en_progreso': 'En Progreso',
          'finalizadas': 'Finalizada',
          'canceladas': 'Cancelada'
        };
        datosFiltrados = datosFiltrados.filter(item => 
          item.estado === estadoMap[data.estado_teleconsulta as keyof typeof estadoMap]
        );
      }
      
      if (data.plataforma !== 'todas') {
        datosFiltrados = datosFiltrados.filter(item => 
          item.plataforma.toLowerCase() === data.plataforma
        );
      }
      
      setDatosReporte(datosFiltrados);
      setReporteGenerado(true);
      toast.success('Reporte de teleconsultas generado exitosamente');
    } catch (error) {
      console.error('Error al generar reporte:', error);
      toast.error('Error al generar el reporte');
    } finally {
      setIsGenerating(false);
    }
  };

  const exportarReporte = (formato: string) => {
    toast.success(`Exportando reporte en formato ${formato.toUpperCase()}`);
    // TODO: Implementar exportación real
  };

  const getEstadoColor = (estado: string) => {
    switch (estado) {
      case 'Finalizada':
        return 'bg-success/20 text-success';
      case 'Programada':
        return 'bg-info/20 text-info';
      case 'En Progreso':
        return 'bg-warning/20 text-warning';
      case 'Cancelada':
        return 'bg-error/20 text-error';
      default:
        return 'bg-secondary/20 text-secondary';
    }
  };

  const getCalidadColor = (calidad: string | null) => {
    if (!calidad) return 'bg-secondary/20 text-secondary';
    switch (calidad) {
      case 'Excelente':
        return 'bg-success/20 text-success';
      case 'Buena':
        return 'bg-info/20 text-info';
      case 'Regular':
        return 'bg-warning/20 text-warning';
      case 'Mala':
        return 'bg-error/20 text-error';
      default:
        return 'bg-secondary/20 text-secondary';
    }
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="bg-card p-6 rounded-lg shadow-lg">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-2xl font-bold text-primary flex items-center">
              <FontAwesomeIcon icon={faVideo} className="mr-2" />
              Reportes de Teleconsultas
            </h1>
            <p className="text-muted mt-1">
              Genere reportes detallados sobre teleconsultas, duración, calidad y satisfacción
            </p>
          </div>
          <div className="text-right">
            <div className="text-sm text-muted">Última actualización</div>
            <div className="text-primary font-medium">{new Date().toLocaleString()}</div>
          </div>
        </div>
      </div>

      {/* Estadísticas Rápidas */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 xl:grid-cols-8 gap-4">
        <div className="bg-card p-4 rounded-lg shadow">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-muted">Total</p>
              <p className="text-2xl font-bold text-primary">{estadisticas.total_teleconsultas}</p>
            </div>
            <FontAwesomeIcon icon={faVideo} className="text-primary text-xl" />
          </div>
        </div>

        <div className="bg-card p-4 rounded-lg shadow">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-muted">Finalizadas</p>
              <p className="text-2xl font-bold text-success">{estadisticas.teleconsultas_finalizadas}</p>
            </div>
            <FontAwesomeIcon icon={faCheckCircle} className="text-success text-xl" />
          </div>
        </div>

        <div className="bg-card p-4 rounded-lg shadow">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-muted">Programadas</p>
              <p className="text-2xl font-bold text-info">{estadisticas.teleconsultas_programadas}</p>
            </div>
            <FontAwesomeIcon icon={faCalendarAlt} className="text-info text-xl" />
          </div>
        </div>

        <div className="bg-card p-4 rounded-lg shadow">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-muted">Canceladas</p>
              <p className="text-2xl font-bold text-error">{estadisticas.teleconsultas_canceladas}</p>
            </div>
            <FontAwesomeIcon icon={faTimesCircle} className="text-error text-xl" />
          </div>
        </div>

        <div className="bg-card p-4 rounded-lg shadow">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-muted">Duración Promedio</p>
              <p className="text-2xl font-bold text-info">{estadisticas.duracion_promedio} min</p>
            </div>
            <FontAwesomeIcon icon={faClock} className="text-info text-xl" />
          </div>
        </div>

        <div className="bg-card p-4 rounded-lg shadow">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-muted">Satisfacción</p>
              <p className="text-2xl font-bold text-success">{estadisticas.satisfaccion_promedio}/5</p>
            </div>
            <FontAwesomeIcon icon={faChartBar} className="text-success text-xl" />
          </div>
        </div>

        <div className="bg-card p-4 rounded-lg shadow">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-muted">Ahorro Tiempo</p>
              <p className="text-2xl font-bold text-warning">{estadisticas.ahorro_tiempo_promedio} min</p>
            </div>
            <FontAwesomeIcon icon={faClock} className="text-warning text-xl" />
          </div>
        </div>

        <div className="bg-card p-4 rounded-lg shadow">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-muted">Ahorro Costos</p>
              <p className="text-xl font-bold text-success">${estadisticas.ahorro_costos_promedio.toLocaleString()}</p>
            </div>
            <FontAwesomeIcon icon={faClipboardList} className="text-success text-xl" />
          </div>
        </div>
      </div>

      {/* Formulario de Filtros */}
      <div className="bg-card p-6 rounded-lg shadow-lg">
        <h3 className="text-lg font-semibold text-primary mb-4 flex items-center">
          <FontAwesomeIcon icon={faFilter} className="mr-2" />
          🔍 Filtros de Reporte
        </h3>

        <form onSubmit={handleSubmit(onSubmit)} className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            <div>
              <label className="block text-sm font-medium text-secondary mb-1">
                Fecha de Inicio *
              </label>
              <Input
                {...register('fecha_inicio')}
                type="date"
                error={errors.fecha_inicio?.message}
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-secondary mb-1">
                Fecha de Fin *
              </label>
              <Input
                {...register('fecha_fin')}
                type="date"
                error={errors.fecha_fin?.message}
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-secondary mb-1">
                Tipo de Reporte *
              </label>
              <select
                {...register('tipo_reporte')}
                className="w-full p-3 rounded-lg bg-secondary border border-color text-primary focus:ring-2 focus:ring-primary focus:border-primary transition-colors"
              >
                <option value="general">📊 Reporte General</option>
                <option value="por_profesional">👨‍⚕️ Por Profesional</option>
                <option value="por_especialidad">🏥 Por Especialidad</option>
                <option value="duracion_promedio">⏱️ Duración Promedio</option>
                <option value="calidad_conexion">📶 Calidad de Conexión</option>
              </select>
              {errors.tipo_reporte && (
                <p className="mt-1 text-sm text-error">{errors.tipo_reporte.message}</p>
              )}
            </div>

            <div>
              <label className="block text-sm font-medium text-secondary mb-1">
                Estado
              </label>
              <select
                {...register('estado_teleconsulta')}
                className="w-full p-3 rounded-lg bg-secondary border border-color text-primary focus:ring-2 focus:ring-primary focus:border-primary transition-colors"
              >
                <option value="todas">Todas</option>
                <option value="programadas">Programadas</option>
                <option value="en_progreso">En Progreso</option>
                <option value="finalizadas">Finalizadas</option>
                <option value="canceladas">Canceladas</option>
              </select>
            </div>

            <div>
              <label className="block text-sm font-medium text-secondary mb-1">
                Plataforma
              </label>
              <select
                {...register('plataforma')}
                className="w-full p-3 rounded-lg bg-secondary border border-color text-primary focus:ring-2 focus:ring-primary focus:border-primary transition-colors"
              >
                <option value="todas">Todas</option>
                <option value="zoom">Zoom</option>
                <option value="teams">Microsoft Teams</option>
                <option value="meet">Google Meet</option>
                <option value="webex">Cisco Webex</option>
                <option value="jitsi">Jitsi Meet</option>
              </select>
            </div>
          </div>

          <div className="flex justify-between items-center pt-4">
            <div className="flex items-center gap-4">
              <label className="text-sm font-medium text-secondary">Formato de Exportación:</label>
              <div className="flex gap-2">
                <label className="flex items-center">
                  <input
                    {...register('formato_exportacion')}
                    type="radio"
                    value="excel"
                    className="mr-2"
                  />
                  <FontAwesomeIcon icon={faFileExcel} className="text-success mr-1" />
                  Excel
                </label>
                <label className="flex items-center">
                  <input
                    {...register('formato_exportacion')}
                    type="radio"
                    value="pdf"
                    className="mr-2"
                  />
                  <FontAwesomeIcon icon={faFilePdf} className="text-error mr-1" />
                  PDF
                </label>
              </div>
            </div>

            <Button
              type="submit"
              disabled={isGenerating}
              className="min-w-[150px]"
            >
              {isGenerating ? (
                <>
                  <span className="mr-2 inline-block w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"></span>
                  Generando...
                </>
              ) : (
                <>
                  <FontAwesomeIcon icon={faSearch} className="mr-2" />
                  Generar Reporte
                </>
              )}
            </Button>
          </div>
        </form>
      </div>

      {/* Resultados del Reporte */}
      {reporteGenerado && (
        <div className="bg-card p-6 rounded-lg shadow-lg">
          <div className="flex justify-between items-center mb-4">
            <h3 className="text-lg font-semibold text-primary flex items-center">
              <FontAwesomeIcon icon={faChartBar} className="mr-2" />
              📋 Resultados del Reporte
            </h3>
            <div className="flex gap-2">
              <Button
                variant="outline"
                onClick={() => exportarReporte('excel')}
                className="text-success border-success hover:bg-success/10"
              >
                <FontAwesomeIcon icon={faFileExcel} className="mr-2" />
                Excel
              </Button>
              <Button
                variant="outline"
                onClick={() => exportarReporte('pdf')}
                className="text-error border-error hover:bg-error/10"
              >
                <FontAwesomeIcon icon={faFilePdf} className="mr-2" />
                PDF
              </Button>
            </div>
          </div>

          {/* Tabla de Resultados */}
          <div className="overflow-x-auto">
            <table className="w-full border-collapse">
              <thead>
                <tr className="bg-secondary/50">
                  <th className="border border-color p-3 text-left text-secondary font-medium">Paciente</th>
                  <th className="border border-color p-3 text-left text-secondary font-medium">Profesional</th>
                  <th className="border border-color p-3 text-left text-secondary font-medium">Especialidad</th>
                  <th className="border border-color p-3 text-left text-secondary font-medium">Fecha</th>
                  <th className="border border-color p-3 text-left text-secondary font-medium">Duración</th>
                  <th className="border border-color p-3 text-left text-secondary font-medium">Plataforma</th>
                  <th className="border border-color p-3 text-left text-secondary font-medium">Calidad</th>
                  <th className="border border-color p-3 text-left text-secondary font-medium">Estado</th>
                  <th className="border border-color p-3 text-left text-secondary font-medium">Motivo</th>
                </tr>
              </thead>
              <tbody>
                {datosReporte.map((item) => (
                  <tr key={item.id} className="hover:bg-secondary/20 transition-colors">
                    <td className="border border-color p-3 text-primary">{item.paciente}</td>
                    <td className="border border-color p-3 text-primary">{item.profesional}</td>
                    <td className="border border-color p-3 text-primary">{item.especialidad}</td>
                    <td className="border border-color p-3 text-primary">
                      {new Date(item.fecha_programada).toLocaleDateString()}
                    </td>
                    <td className="border border-color p-3 text-primary font-medium">
                      {item.duracion_minutos > 0 ? `${item.duracion_minutos} min` : '-'}
                    </td>
                    <td className="border border-color p-3 text-primary">{item.plataforma}</td>
                    <td className="border border-color p-3">
                      {item.calidad_conexion ? (
                        <span className={`px-2 py-1 rounded-full text-xs font-medium ${getCalidadColor(item.calidad_conexion)}`}>
                          {item.calidad_conexion}
                        </span>
                      ) : (
                        <span className="text-muted">-</span>
                      )}
                    </td>
                    <td className="border border-color p-3">
                      <span className={`px-2 py-1 rounded-full text-xs font-medium ${getEstadoColor(item.estado)}`}>
                        {item.estado}
                      </span>
                    </td>
                    <td className="border border-color p-3 text-primary">{item.motivo}</td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>

          {/* Resumen del Reporte */}
          <div className="mt-6 grid grid-cols-1 md:grid-cols-4 gap-4">
            <div className="bg-secondary/30 p-4 rounded-lg">
              <h4 className="font-medium text-secondary mb-2">Total de Registros</h4>
              <p className="text-2xl font-bold text-primary">{datosReporte.length}</p>
            </div>
            <div className="bg-secondary/30 p-4 rounded-lg">
              <h4 className="font-medium text-secondary mb-2">Duración Promedio</h4>
              <p className="text-2xl font-bold text-info">
                {datosReporte.filter(item => item.duracion_minutos > 0).length > 0
                  ? (datosReporte.reduce((acc, item) => acc + item.duracion_minutos, 0) /
                     datosReporte.filter(item => item.duracion_minutos > 0).length).toFixed(1)
                  : '0'} min
              </p>
            </div>
            <div className="bg-secondary/30 p-4 rounded-lg">
              <h4 className="font-medium text-secondary mb-2">Teleconsultas Finalizadas</h4>
              <p className="text-2xl font-bold text-success">
                {datosReporte.filter(item => item.estado === 'Finalizada').length}
              </p>
            </div>
            <div className="bg-secondary/30 p-4 rounded-lg">
              <h4 className="font-medium text-secondary mb-2">Tasa de Finalización</h4>
              <p className="text-2xl font-bold text-success">
                {datosReporte.length > 0
                  ? ((datosReporte.filter(item => item.estado === 'Finalizada').length / datosReporte.length) * 100).toFixed(1)
                  : '0'}%
              </p>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};
