import React, { useState } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { Button } from '../../components/ui/Button';
import { Input } from '../../components/ui/Input';
import { toast } from 'react-hot-toast';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { 
  faBoxes, 
  faCalendarAlt, 
  faDownload, 
  faFilter,
  faChartBar,
  faFileExcel,
  faFilePdf,
  faSearch,
  faExclamationTriangle,
  faCheckCircle,
  faClock,
  faWarehouse,
  faPills,
  faStethoscope,
  faTruck,
  faChartLine,
  faInfoCircle
} from '@fortawesome/free-solid-svg-icons';

// Schema de validación para filtros de reporte
const reporteInventarioSchema = z.object({
  fecha_inicio: z.string().min(1, 'Fecha de inicio requerida'),
  fecha_fin: z.string().min(1, 'Fecha de fin requerida'),
  tipo_reporte: z.enum(['general', 'stock_bajo', 'movimientos', 'valoracion', 'caducidad', 'rotacion'], {
    required_error: 'Debe seleccionar un tipo de reporte'
  }),
  tipo_recurso: z.enum(['todos', 'Medicamento', 'Material Médico', 'Insumo', 'Equipamiento', 'Otro']).default('todos'),
  ubicacion: z.string().optional(),
  incluir_agotados: z.boolean().default(false),
  formato_exportacion: z.enum(['excel', 'pdf', 'csv']).default('excel'),
});

type ReporteInventarioFormData = z.infer<typeof reporteInventarioSchema>;

// Datos mock para el reporte
const datosInventario = [
  {
    id: 1,
    codigo: 'MED001',
    nombre: 'Paracetamol 500mg',
    tipo_recurso: 'Medicamento',
    stock_actual: 150,
    stock_minimo: 100,
    stock_maximo: 500,
    valor_unitario: 250,
    valor_total: 37500,
    ubicacion: 'Farmacia Central',
    fecha_vencimiento: '2024-12-31',
    proveedor: 'Laboratorios ABC',
    lote: 'LOT001',
    estado: 'Normal'
  },
  {
    id: 2,
    codigo: 'MAT002',
    nombre: 'Jeringas 10ml',
    tipo_recurso: 'Material Médico',
    stock_actual: 25,
    stock_minimo: 50,
    stock_maximo: 200,
    valor_unitario: 150,
    valor_total: 3750,
    ubicacion: 'Almacén General',
    fecha_vencimiento: '2025-06-30',
    proveedor: 'Suministros Médicos XYZ',
    lote: 'LOT002',
    estado: 'Bajo'
  },
  {
    id: 3,
    codigo: 'INS003',
    nombre: 'Gasas Estériles',
    tipo_recurso: 'Insumo',
    stock_actual: 5,
    stock_minimo: 30,
    stock_maximo: 150,
    valor_unitario: 500,
    valor_total: 2500,
    ubicacion: 'Quirófano',
    fecha_vencimiento: '2024-08-15',
    proveedor: 'Insumos Hospitalarios',
    lote: 'LOT003',
    estado: 'Crítico'
  }
];

const estadisticasInventario = {
  total_items: 1247,
  items_stock_normal: 892,
  items_stock_bajo: 234,
  items_stock_critico: 121,
  valor_total_inventario: 45678900,
  items_proximos_vencer: 67,
  rotacion_promedio: 15.3, // días
  items_sin_movimiento: 89
};

export const ReportesInventario: React.FC = () => {
  const [isGenerating, setIsGenerating] = useState(false);
  const [reporteGenerado, setReporteGenerado] = useState(false);
  const [datosReporte, setDatosReporte] = useState(datosInventario);

  const {
    register,
    handleSubmit,
    formState: { errors },
    watch
  } = useForm<ReporteInventarioFormData>({
    resolver: zodResolver(reporteInventarioSchema),
    defaultValues: {
      fecha_inicio: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
      fecha_fin: new Date().toISOString().split('T')[0],
      tipo_reporte: 'general',
      tipo_recurso: 'todos',
      incluir_agotados: false,
      formato_exportacion: 'excel'
    }
  });

  const tipoReporte = watch('tipo_reporte');
  const tipoRecurso = watch('tipo_recurso');

  const onSubmit = async (data: ReporteInventarioFormData) => {
    setIsGenerating(true);
    try {
      console.log('Generando reporte de inventario con filtros:', data);
      
      // Simular generación de reporte
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      // Filtrar datos según criterios
      let datosFiltrados = datosInventario;
      
      if (data.tipo_recurso !== 'todos') {
        datosFiltrados = datosFiltrados.filter(item => item.tipo_recurso === data.tipo_recurso);
      }
      
      if (data.tipo_reporte === 'stock_bajo') {
        datosFiltrados = datosFiltrados.filter(item => item.stock_actual <= item.stock_minimo);
      }
      
      if (data.ubicacion) {
        datosFiltrados = datosFiltrados.filter(item => 
          item.ubicacion.toLowerCase().includes(data.ubicacion!.toLowerCase())
        );
      }
      
      setDatosReporte(datosFiltrados);
      setReporteGenerado(true);
      toast.success('Reporte de inventario generado exitosamente');
    } catch (error) {
      console.error('Error al generar reporte:', error);
      toast.error('Error al generar el reporte');
    } finally {
      setIsGenerating(false);
    }
  };

  const exportarReporte = (formato: string) => {
    toast.success(`Exportando reporte en formato ${formato.toUpperCase()}`);
    // TODO: Implementar exportación real
  };

  const getEstadoColor = (estado: string) => {
    switch (estado) {
      case 'Normal':
        return 'bg-success/20 text-success';
      case 'Bajo':
        return 'bg-warning/20 text-warning';
      case 'Crítico':
        return 'bg-error/20 text-error';
      default:
        return 'bg-secondary/20 text-secondary';
    }
  };

  const getEstadoIcon = (estado: string) => {
    switch (estado) {
      case 'Normal':
        return faCheckCircle;
      case 'Bajo':
        return faClock;
      case 'Crítico':
        return faExclamationTriangle;
      default:
        return faInfoCircle;
    }
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="bg-card p-6 rounded-lg shadow-lg">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-2xl font-bold text-primary flex items-center">
              <FontAwesomeIcon icon={faBoxes} className="mr-2" />
              Reportes de Inventario
            </h1>
            <p className="text-muted mt-1">
              Genere reportes detallados sobre stock, movimientos, valoración y rotación de inventario
            </p>
          </div>
          <div className="text-right">
            <div className="text-sm text-muted">Última actualización</div>
            <div className="text-primary font-medium">{new Date().toLocaleString()}</div>
          </div>
        </div>
      </div>

      {/* Estadísticas Rápidas */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 xl:grid-cols-8 gap-4">
        <div className="bg-card p-4 rounded-lg shadow">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-muted">Total Items</p>
              <p className="text-2xl font-bold text-primary">{estadisticasInventario.total_items}</p>
            </div>
            <FontAwesomeIcon icon={faBoxes} className="text-primary text-xl" />
          </div>
        </div>

        <div className="bg-card p-4 rounded-lg shadow">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-muted">Stock Normal</p>
              <p className="text-2xl font-bold text-success">{estadisticasInventario.items_stock_normal}</p>
            </div>
            <FontAwesomeIcon icon={faCheckCircle} className="text-success text-xl" />
          </div>
        </div>

        <div className="bg-card p-4 rounded-lg shadow">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-muted">Stock Bajo</p>
              <p className="text-2xl font-bold text-warning">{estadisticasInventario.items_stock_bajo}</p>
            </div>
            <FontAwesomeIcon icon={faClock} className="text-warning text-xl" />
          </div>
        </div>

        <div className="bg-card p-4 rounded-lg shadow">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-muted">Stock Crítico</p>
              <p className="text-2xl font-bold text-error">{estadisticasInventario.items_stock_critico}</p>
            </div>
            <FontAwesomeIcon icon={faExclamationTriangle} className="text-error text-xl" />
          </div>
        </div>

        <div className="bg-card p-4 rounded-lg shadow">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-muted">Valor Total</p>
              <p className="text-xl font-bold text-success">${estadisticasInventario.valor_total_inventario.toLocaleString()}</p>
            </div>
            <FontAwesomeIcon icon={faChartBar} className="text-success text-xl" />
          </div>
        </div>

        <div className="bg-card p-4 rounded-lg shadow">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-muted">Por Vencer</p>
              <p className="text-2xl font-bold text-warning">{estadisticasInventario.items_proximos_vencer}</p>
            </div>
            <FontAwesomeIcon icon={faCalendarAlt} className="text-warning text-xl" />
          </div>
        </div>

        <div className="bg-card p-4 rounded-lg shadow">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-muted">Rotación</p>
              <p className="text-2xl font-bold text-info">{estadisticasInventario.rotacion_promedio} días</p>
            </div>
            <FontAwesomeIcon icon={faChartLine} className="text-info text-xl" />
          </div>
        </div>

        <div className="bg-card p-4 rounded-lg shadow">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-muted">Sin Movimiento</p>
              <p className="text-2xl font-bold text-error">{estadisticasInventario.items_sin_movimiento}</p>
            </div>
            <FontAwesomeIcon icon={faWarehouse} className="text-error text-xl" />
          </div>
        </div>
      </div>

      {/* Formulario de Filtros */}
      <div className="bg-card p-6 rounded-lg shadow-lg">
        <h3 className="text-lg font-semibold text-primary mb-4 flex items-center">
          <FontAwesomeIcon icon={faFilter} className="mr-2" />
          🔍 Filtros de Reporte
        </h3>

        <form onSubmit={handleSubmit(onSubmit)} className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            <div>
              <label className="block text-sm font-medium text-secondary mb-1">
                Fecha de Inicio *
              </label>
              <Input
                {...register('fecha_inicio')}
                type="date"
                error={errors.fecha_inicio?.message}
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-secondary mb-1">
                Fecha de Fin *
              </label>
              <Input
                {...register('fecha_fin')}
                type="date"
                error={errors.fecha_fin?.message}
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-secondary mb-1">
                Tipo de Reporte *
              </label>
              <select
                {...register('tipo_reporte')}
                className="w-full p-3 rounded-lg bg-secondary border border-color text-primary focus:ring-2 focus:ring-primary focus:border-primary transition-colors"
              >
                <option value="general">📊 Reporte General</option>
                <option value="stock_bajo">⚠️ Stock Bajo/Crítico</option>
                <option value="movimientos">📦 Movimientos de Inventario</option>
                <option value="valoracion">💰 Valoración de Inventario</option>
                <option value="caducidad">📅 Próximos a Vencer</option>
                <option value="rotacion">🔄 Rotación de Inventario</option>
              </select>
              {errors.tipo_reporte && (
                <p className="mt-1 text-sm text-error">{errors.tipo_reporte.message}</p>
              )}
            </div>

            <div>
              <label className="block text-sm font-medium text-secondary mb-1">
                Tipo de Recurso
              </label>
              <select
                {...register('tipo_recurso')}
                className="w-full p-3 rounded-lg bg-secondary border border-color text-primary focus:ring-2 focus:ring-primary focus:border-primary transition-colors"
              >
                <option value="todos">Todos los tipos</option>
                <option value="Medicamento">💊 Medicamentos</option>
                <option value="Material Médico">🩺 Material Médico</option>
                <option value="Insumo">📋 Insumos</option>
                <option value="Equipamiento">⚕️ Equipamiento</option>
                <option value="Otro">📦 Otros</option>
              </select>
            </div>

            <div>
              <label className="block text-sm font-medium text-secondary mb-1">
                Ubicación
              </label>
              <Input
                {...register('ubicacion')}
                placeholder="Filtrar por ubicación"
                error={errors.ubicacion?.message}
              />
            </div>

            <div className="flex items-center space-x-3">
              <input
                {...register('incluir_agotados')}
                type="checkbox"
                className="w-4 h-4 text-primary border-color rounded focus:ring-primary"
              />
              <label className="text-sm font-medium text-secondary">
                Incluir items agotados
              </label>
            </div>
          </div>

          <div className="flex justify-between items-center pt-4">
            <div className="flex items-center gap-4">
              <label className="text-sm font-medium text-secondary">Formato de Exportación:</label>
              <div className="flex gap-2">
                <label className="flex items-center">
                  <input
                    {...register('formato_exportacion')}
                    type="radio"
                    value="excel"
                    className="mr-2"
                  />
                  <FontAwesomeIcon icon={faFileExcel} className="text-success mr-1" />
                  Excel
                </label>
                <label className="flex items-center">
                  <input
                    {...register('formato_exportacion')}
                    type="radio"
                    value="pdf"
                    className="mr-2"
                  />
                  <FontAwesomeIcon icon={faFilePdf} className="text-error mr-1" />
                  PDF
                </label>
              </div>
            </div>

            <Button
              type="submit"
              disabled={isGenerating}
              className="min-w-[150px]"
            >
              {isGenerating ? (
                <>
                  <span className="mr-2 inline-block w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"></span>
                  Generando...
                </>
              ) : (
                <>
                  <FontAwesomeIcon icon={faSearch} className="mr-2" />
                  Generar Reporte
                </>
              )}
            </Button>
          </div>
        </form>
      </div>

      {/* Información del Tipo de Reporte */}
      {tipoReporte && (
        <div className="bg-card p-4 rounded-lg shadow-lg">
          <h4 className="text-md font-semibold text-primary mb-3 flex items-center">
            <FontAwesomeIcon icon={faInfoCircle} className="mr-2" />
            ℹ️ Información del Reporte: {tipoReporte.replace('_', ' ').toUpperCase()}
          </h4>

          <div className="text-sm text-muted">
            {tipoReporte === 'general' && (
              <ul className="space-y-1">
                <li>• Vista completa del inventario con todos los items</li>
                <li>• Incluye stock actual, mínimo y ubicaciones</li>
                <li>• Valoración total por categorías</li>
                <li>• Indicadores de estado de stock</li>
              </ul>
            )}
            {tipoReporte === 'stock_bajo' && (
              <ul className="space-y-1">
                <li>• Items con stock por debajo del mínimo establecido</li>
                <li>• Clasificación por nivel de criticidad</li>
                <li>• Sugerencias de reposición</li>
                <li>• Impacto en operaciones</li>
              </ul>
            )}
            {tipoReporte === 'movimientos' && (
              <ul className="space-y-1">
                <li>• Historial de entradas y salidas</li>
                <li>• Análisis de consumo por período</li>
                <li>• Identificación de patrones de uso</li>
                <li>• Trazabilidad completa</li>
              </ul>
            )}
            {tipoReporte === 'valoracion' && (
              <ul className="space-y-1">
                <li>• Valor monetario del inventario</li>
                <li>• Distribución por categorías</li>
                <li>• Análisis de inversión</li>
                <li>• Indicadores financieros</li>
              </ul>
            )}
            {tipoReporte === 'caducidad' && (
              <ul className="space-y-1">
                <li>• Items próximos a vencer</li>
                <li>• Alertas por fechas de vencimiento</li>
                <li>• Priorización para uso</li>
                <li>• Prevención de pérdidas</li>
              </ul>
            )}
            {tipoReporte === 'rotacion' && (
              <ul className="space-y-1">
                <li>• Velocidad de rotación por item</li>
                <li>• Items de alta y baja rotación</li>
                <li>• Optimización de stock</li>
                <li>• Análisis de eficiencia</li>
              </ul>
            )}
          </div>
        </div>
      )}

      {/* Resultados del Reporte */}
      {reporteGenerado && (
        <div className="bg-card p-6 rounded-lg shadow-lg">
          <div className="flex justify-between items-center mb-4">
            <h3 className="text-lg font-semibold text-primary flex items-center">
              <FontAwesomeIcon icon={faChartBar} className="mr-2" />
              📋 Resultados del Reporte
            </h3>
            <div className="flex gap-2">
              <Button
                variant="outline"
                onClick={() => exportarReporte('excel')}
                className="text-success border-success hover:bg-success/10"
              >
                <FontAwesomeIcon icon={faFileExcel} className="mr-2" />
                Excel
              </Button>
              <Button
                variant="outline"
                onClick={() => exportarReporte('pdf')}
                className="text-error border-error hover:bg-error/10"
              >
                <FontAwesomeIcon icon={faFilePdf} className="mr-2" />
                PDF
              </Button>
            </div>
          </div>

          {/* Tabla de Resultados */}
          <div className="overflow-x-auto">
            <table className="w-full border-collapse">
              <thead>
                <tr className="bg-secondary/50">
                  <th className="border border-color p-3 text-left text-secondary font-medium">Código</th>
                  <th className="border border-color p-3 text-left text-secondary font-medium">Nombre</th>
                  <th className="border border-color p-3 text-left text-secondary font-medium">Tipo</th>
                  <th className="border border-color p-3 text-left text-secondary font-medium">Stock Actual</th>
                  <th className="border border-color p-3 text-left text-secondary font-medium">Stock Mínimo</th>
                  <th className="border border-color p-3 text-left text-secondary font-medium">Valor Total</th>
                  <th className="border border-color p-3 text-left text-secondary font-medium">Ubicación</th>
                  <th className="border border-color p-3 text-left text-secondary font-medium">Vencimiento</th>
                  <th className="border border-color p-3 text-left text-secondary font-medium">Estado</th>
                </tr>
              </thead>
              <tbody>
                {datosReporte.map((item) => (
                  <tr key={item.id} className="hover:bg-secondary/20 transition-colors">
                    <td className="border border-color p-3 text-primary font-medium">{item.codigo}</td>
                    <td className="border border-color p-3 text-primary">{item.nombre}</td>
                    <td className="border border-color p-3 text-primary">
                      <span className="flex items-center">
                        {item.tipo_recurso === 'Medicamento' && <FontAwesomeIcon icon={faPills} className="mr-2 text-info" />}
                        {item.tipo_recurso === 'Material Médico' && <FontAwesomeIcon icon={faStethoscope} className="mr-2 text-success" />}
                        {item.tipo_recurso === 'Insumo' && <FontAwesomeIcon icon={faBoxes} className="mr-2 text-warning" />}
                        {item.tipo_recurso}
                      </span>
                    </td>
                    <td className="border border-color p-3 text-primary font-bold">
                      <span className={
                        item.stock_actual <= item.stock_minimo * 0.5 ? 'text-error' :
                        item.stock_actual <= item.stock_minimo ? 'text-warning' :
                        'text-success'
                      }>
                        {item.stock_actual}
                      </span>
                    </td>
                    <td className="border border-color p-3 text-primary">{item.stock_minimo}</td>
                    <td className="border border-color p-3 text-primary font-medium">
                      ${item.valor_total.toLocaleString()}
                    </td>
                    <td className="border border-color p-3 text-primary">{item.ubicacion}</td>
                    <td className="border border-color p-3 text-primary">
                      <span className={
                        new Date(item.fecha_vencimiento) < new Date(Date.now() + 30 * 24 * 60 * 60 * 1000)
                          ? 'text-warning' : 'text-primary'
                      }>
                        {new Date(item.fecha_vencimiento).toLocaleDateString()}
                      </span>
                    </td>
                    <td className="border border-color p-3">
                      <span className={`px-2 py-1 rounded-full text-xs font-medium flex items-center ${getEstadoColor(item.estado)}`}>
                        <FontAwesomeIcon icon={getEstadoIcon(item.estado)} className="mr-1" />
                        {item.estado}
                      </span>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>

          {/* Resumen del Reporte */}
          <div className="mt-6 grid grid-cols-1 md:grid-cols-4 gap-4">
            <div className="bg-secondary/30 p-4 rounded-lg">
              <h4 className="font-medium text-secondary mb-2">Total de Items</h4>
              <p className="text-2xl font-bold text-primary">{datosReporte.length}</p>
            </div>
            <div className="bg-secondary/30 p-4 rounded-lg">
              <h4 className="font-medium text-secondary mb-2">Valor Total</h4>
              <p className="text-2xl font-bold text-success">
                ${datosReporte.reduce((acc, item) => acc + item.valor_total, 0).toLocaleString()}
              </p>
            </div>
            <div className="bg-secondary/30 p-4 rounded-lg">
              <h4 className="font-medium text-secondary mb-2">Items Críticos</h4>
              <p className="text-2xl font-bold text-error">
                {datosReporte.filter(item => item.estado === 'Crítico').length}
              </p>
            </div>
            <div className="bg-secondary/30 p-4 rounded-lg">
              <h4 className="font-medium text-secondary mb-2">Items con Stock Bajo</h4>
              <p className="text-2xl font-bold text-warning">
                {datosReporte.filter(item => item.estado === 'Bajo').length}
              </p>
            </div>
          </div>

          {/* Gráfico de Distribución por Tipo */}
          <div className="mt-6 bg-secondary/30 p-4 rounded-lg">
            <h4 className="font-medium text-secondary mb-4">📊 Distribución por Tipo de Recurso</h4>
            <div className="grid grid-cols-1 md:grid-cols-5 gap-4">
              {['Medicamento', 'Material Médico', 'Insumo', 'Equipamiento', 'Otro'].map(tipo => {
                const count = datosReporte.filter(item => item.tipo_recurso === tipo).length;
                const percentage = datosReporte.length > 0 ? (count / datosReporte.length * 100).toFixed(1) : '0';
                return (
                  <div key={tipo} className="text-center">
                    <div className="text-2xl font-bold text-primary">{count}</div>
                    <div className="text-sm text-muted">{tipo}</div>
                    <div className="text-xs text-info">{percentage}%</div>
                  </div>
                );
              })}
            </div>
          </div>

          {/* Alertas y Recomendaciones */}
          <div className="mt-6 space-y-4">
            {datosReporte.filter(item => item.estado === 'Crítico').length > 0 && (
              <div className="bg-error/20 border border-error/30 rounded-lg p-4">
                <h5 className="font-medium text-error mb-2 flex items-center">
                  <FontAwesomeIcon icon={faExclamationTriangle} className="mr-2" />
                  🚨 Alertas Críticas
                </h5>
                <p className="text-sm text-muted">
                  Hay {datosReporte.filter(item => item.estado === 'Crítico').length} items con stock crítico que requieren reposición inmediata.
                </p>
              </div>
            )}

            {datosReporte.filter(item => item.estado === 'Bajo').length > 0 && (
              <div className="bg-warning/20 border border-warning/30 rounded-lg p-4">
                <h5 className="font-medium text-warning mb-2 flex items-center">
                  <FontAwesomeIcon icon={faClock} className="mr-2" />
                  ⚠️ Advertencias de Stock
                </h5>
                <p className="text-sm text-muted">
                  Hay {datosReporte.filter(item => item.estado === 'Bajo').length} items con stock bajo que deben ser monitoreados.
                </p>
              </div>
            )}

            <div className="bg-info/20 border border-info/30 rounded-lg p-4">
              <h5 className="font-medium text-info mb-2 flex items-center">
                <FontAwesomeIcon icon={faInfoCircle} className="mr-2" />
                💡 Recomendaciones
              </h5>
              <ul className="text-sm text-muted space-y-1">
                <li>• Revisar items próximos a vencer en los próximos 30 días</li>
                <li>• Establecer alertas automáticas para stock mínimo</li>
                <li>• Optimizar ubicaciones para items de alta rotación</li>
                <li>• Considerar ajustar stock mínimo según patrones de consumo</li>
              </ul>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};
