"use client";import{useFocusRing as Ce}from"@react-aria/focus";import{useHover as ve}from"@react-aria/interactions";import{useVirtualizer as Me}from"@tanstack/react-virtual";import D,{Fragment as Pe,createContext as Ee,use<PERSON>allback as re,useContext as Oe,useMemo as X,useRef as de,useState as Te}from"react";import{flushSync as te}from"react-dom";import{useActivePress as Le}from'../../hooks/use-active-press.js';import{useByComparator as Ve}from'../../hooks/use-by-comparator.js';import{useControllable as we}from'../../hooks/use-controllable.js';import{useDefaultValue as Be}from'../../hooks/use-default-value.js';import{useDisposables as Ne}from'../../hooks/use-disposables.js';import{useElementSize as he}from'../../hooks/use-element-size.js';import{useEvent as E}from'../../hooks/use-event.js';import{useId as be}from'../../hooks/use-id.js';import{useInertOthers as ke}from'../../hooks/use-inert-others.js';import{useIsoMorphicEffect as Q}from'../../hooks/use-iso-morphic-effect.js';import{useLatestValue as Ue}from'../../hooks/use-latest-value.js';import{useOnDisappear as He}from'../../hooks/use-on-disappear.js';import{useOutsideClick as Ge}from'../../hooks/use-outside-click.js';import{useOwnerDocument as xe}from'../../hooks/use-owner.js';import{useRefocusableInput as Ae}from'../../hooks/use-refocusable-input.js';import{useResolveButtonType as ze}from'../../hooks/use-resolve-button-type.js';import{useScrollLock as Ke}from'../../hooks/use-scroll-lock.js';import{useSyncRefs as me}from'../../hooks/use-sync-refs.js';import{useTrackedPointer as We}from'../../hooks/use-tracked-pointer.js';import{transitionDataAttributes as Xe,useTransition as $e}from'../../hooks/use-transition.js';import{useTreeWalker as Je}from'../../hooks/use-tree-walker.js';import{useWatch as Ie}from'../../hooks/use-watch.js';import{useDisabled as je}from'../../internal/disabled.js';import{FloatingProvider as qe,useFloatingPanel as Ye,useFloatingPanelProps as Qe,useFloatingReference as Ze,useResolvedAnchor as eo}from'../../internal/floating.js';import{FormFields as oo}from'../../internal/form-fields.js';import{Frozen as to,useFrozenData as Re}from'../../internal/frozen.js';import{useProvidedId as no}from'../../internal/id.js';import{OpenClosedProvider as ao,State as ce,useOpenClosed as ro}from'../../internal/open-closed.js';import{useSlice as R}from'../../react-glue.js';import{history as _e}from'../../utils/active-element-history.js';import{isDisabledReactIssue7711 as lo}from'../../utils/bugs.js';import{Focus as M}from'../../utils/calculate-active-index.js';import{disposables as io}from'../../utils/disposables.js';import{match as fe}from'../../utils/match.js';import{isMobile as so}from'../../utils/platform.js';import{RenderFeatures as De,forwardRefWithAs as le,mergeProps as ge,useRender as ie}from'../../utils/render.js';import{useDescribedBy as uo}from'../description/description.js';import{Keys as L}from'../keyboard.js';import{Label as po,useLabelledBy as ye,useLabels as bo}from'../label/label.js';import{MouseButton as Fe}from'../mouse.js';import{Portal as mo}from'../portal/portal.js';import{ActionTypes as co,ActivationTrigger as Z,ComboboxState as r,ValueMode as G}from'./combobox-machine.js';import{ComboboxContext as fo,useComboboxMachine as To,useComboboxMachineContext as se}from'./combobox-machine-glue.js';let ue=Ee(null);ue.displayName="ComboboxDataContext";function ne(y){let O=Oe(ue);if(O===null){let e=new Error(`<${y} /> is missing a parent <Combobox /> component.`);throw Error.captureStackTrace&&Error.captureStackTrace(e,ne),e}return O}let Se=Ee(null);function xo(y){let O=se("VirtualProvider"),e=ne("VirtualProvider"),{options:o}=e.virtual,V=R(O,a=>a.optionsElement),[A,C]=X(()=>{let a=V;if(!a)return[0,0];let g=window.getComputedStyle(a);return[parseFloat(g.paddingBlockStart||g.paddingTop),parseFloat(g.paddingBlockEnd||g.paddingBottom)]},[V]),b=Me({enabled:o.length!==0,scrollPaddingStart:A,scrollPaddingEnd:C,count:o.length,estimateSize(){return 40},getScrollElement(){return O.state.optionsElement},overscan:12}),[h,l]=Te(0);Q(()=>{l(a=>a+1)},[o]);let c=b.getVirtualItems(),n=R(O,a=>a.activationTrigger===Z.Pointer),x=R(O,O.selectors.activeOptionIndex);return c.length===0?null:D.createElement(Se.Provider,{value:b},D.createElement("div",{style:{position:"relative",width:"100%",height:`${b.getTotalSize()}px`},ref:a=>{a&&(n||x!==null&&o.length>x&&b.scrollToIndex(x))}},c.map(a=>{var g;return D.createElement(Pe,{key:a.key},D.cloneElement((g=y.children)==null?void 0:g.call(y,{...y.slot,option:o[a.index]}),{key:`${h}-${a.key}`,"data-index":a.index,"aria-setsize":o.length,"aria-posinset":a.index+1,style:{position:"absolute",top:0,left:0,transform:`translateY(${a.start}px)`,overflowAnchor:"none"}}))})))}let go=Pe;function yo(y,O){let e=je(),{value:o,defaultValue:V,onChange:A,form:C,name:b,by:h,invalid:l=!1,disabled:c=e||!1,onClose:n,__demoMode:x=!1,multiple:a=!1,immediate:g=!1,virtual:f=null,nullable:B,...w}=y,F=Be(V),[m=a?[]:void 0,P]=we(o,A,F),d=To({virtual:f,__demoMode:x}),_=de({static:!1,hold:!1}),I=Ve(h),N=E(p=>f?h===null?f.options.indexOf(p):f.options.findIndex(T=>I(T,p)):d.state.options.findIndex(T=>I(T.dataRef.current.value,p))),z=re(p=>fe(i.mode,{[G.Multi]:()=>m.some(T=>I(T,p)),[G.Single]:()=>I(m,p)}),[m]),K=R(d,p=>p.virtual),k=E(()=>n==null?void 0:n()),i=X(()=>({__demoMode:x,immediate:g,optionsPropsRef:_,value:m,defaultValue:F,disabled:c,invalid:l,mode:a?G.Multi:G.Single,virtual:f?K:null,onChange:P,isSelected:z,calculateIndex:N,compare:I,onClose:k}),[m,F,c,l,a,P,z,x,d,f,K,k]);Q(()=>{var p;f&&d.send({type:co.UpdateVirtualConfiguration,options:f.options,disabled:(p=f.disabled)!=null?p:null})},[f,f==null?void 0:f.options,f==null?void 0:f.disabled]),Q(()=>{d.state.dataRef.current=i},[i]);let[S,$,U,s]=R(d,p=>[p.comboboxState,p.buttonElement,p.inputElement,p.optionsElement]),W=S===r.Open;Ge(W,[$,U,s],()=>d.actions.closeCombobox());let ae=R(d,d.selectors.activeOptionIndex),J=R(d,d.selectors.activeOption),q=X(()=>({open:S===r.Open,disabled:c,invalid:l,activeIndex:ae,activeOption:J,value:m}),[i,c,m,l,J,S]),[j,pe]=bo(),Y=O===null?{}:{ref:O},ee=re(()=>{if(F!==void 0)return P==null?void 0:P(F)},[P,F]),t=ie();return D.createElement(pe,{value:j,props:{htmlFor:U==null?void 0:U.id},slot:{open:S===r.Open,disabled:c}},D.createElement(qe,null,D.createElement(ue.Provider,{value:i},D.createElement(fo.Provider,{value:d},D.createElement(ao,{value:fe(S,{[r.Open]:ce.Open,[r.Closed]:ce.Closed})},b!=null&&D.createElement(oo,{disabled:c,data:m!=null?{[b]:m}:{},form:C,onReset:ee}),t({ourProps:Y,theirProps:w,slot:q,defaultTag:go,name:"Combobox"}))))))}let Co="input";function vo(y,O){var Y,ee;let e=se("Combobox.Input"),o=ne("Combobox.Input"),V=be(),A=no(),{id:C=A||`headlessui-combobox-input-${V}`,onChange:b,displayValue:h,disabled:l=o.disabled||!1,autoFocus:c=!1,type:n="text",...x}=y,[a]=R(e,t=>[t.inputElement]),g=de(null),f=me(g,O,Ze(),e.actions.setInputElement),B=xe(a),[w,F]=R(e,t=>[t.comboboxState,t.isTyping]),m=Ne(),P=E(()=>{e.actions.onChange(null),e.state.optionsElement&&(e.state.optionsElement.scrollTop=0),e.actions.goToOption({focus:M.Nothing})}),d=X(()=>{var t;return typeof h=="function"&&o.value!==void 0?(t=h(o.value))!=null?t:"":typeof o.value=="string"?o.value:""},[o.value,h]);Ie(([t,p],[T,H])=>{if(e.state.isTyping)return;let v=g.current;v&&((H===r.Open&&p===r.Closed||t!==T)&&(v.value=t),requestAnimationFrame(()=>{if(e.state.isTyping||!v||(B==null?void 0:B.activeElement)!==v)return;let{selectionStart:u,selectionEnd:oe}=v;Math.abs((oe!=null?oe:0)-(u!=null?u:0))===0&&u===0&&v.setSelectionRange(v.value.length,v.value.length)}))},[d,w,B,F]),Ie(([t],[p])=>{if(t===r.Open&&p===r.Closed){if(e.state.isTyping)return;let T=g.current;if(!T)return;let H=T.value,{selectionStart:v,selectionEnd:u,selectionDirection:oe}=T;T.value="",T.value=H,oe!==null?T.setSelectionRange(v,u,oe):T.setSelectionRange(v,u)}},[w]);let _=de(!1),I=E(()=>{_.current=!0}),N=E(()=>{m.nextFrame(()=>{_.current=!1})}),z=E(t=>{switch(e.actions.setIsTyping(!0),t.key){case L.Enter:if(e.state.comboboxState!==r.Open||_.current)return;if(t.preventDefault(),t.stopPropagation(),e.selectors.activeOptionIndex(e.state)===null){e.actions.closeCombobox();return}e.actions.selectActiveOption(),o.mode===G.Single&&e.actions.closeCombobox();break;case L.ArrowDown:return t.preventDefault(),t.stopPropagation(),fe(e.state.comboboxState,{[r.Open]:()=>e.actions.goToOption({focus:M.Next}),[r.Closed]:()=>e.actions.openCombobox()});case L.ArrowUp:return t.preventDefault(),t.stopPropagation(),fe(e.state.comboboxState,{[r.Open]:()=>e.actions.goToOption({focus:M.Previous}),[r.Closed]:()=>{te(()=>e.actions.openCombobox()),o.value||e.actions.goToOption({focus:M.Last})}});case L.Home:if(t.shiftKey)break;return t.preventDefault(),t.stopPropagation(),e.actions.goToOption({focus:M.First});case L.PageUp:return t.preventDefault(),t.stopPropagation(),e.actions.goToOption({focus:M.First});case L.End:if(t.shiftKey)break;return t.preventDefault(),t.stopPropagation(),e.actions.goToOption({focus:M.Last});case L.PageDown:return t.preventDefault(),t.stopPropagation(),e.actions.goToOption({focus:M.Last});case L.Escape:return e.state.comboboxState!==r.Open?void 0:(t.preventDefault(),e.state.optionsElement&&!o.optionsPropsRef.current.static&&t.stopPropagation(),o.mode===G.Single&&o.value===null&&P(),e.actions.closeCombobox());case L.Tab:if(e.state.comboboxState!==r.Open)return;o.mode===G.Single&&e.state.activationTrigger!==Z.Focus&&e.actions.selectActiveOption(),e.actions.closeCombobox();break}}),K=E(t=>{b==null||b(t),o.mode===G.Single&&t.target.value===""&&P(),e.actions.openCombobox()}),k=E(t=>{var T,H,v;let p=(T=t.relatedTarget)!=null?T:_e.find(u=>u!==t.currentTarget);if(!((H=e.state.optionsElement)!=null&&H.contains(p))&&!((v=e.state.buttonElement)!=null&&v.contains(p))&&e.state.comboboxState===r.Open)return t.preventDefault(),o.mode===G.Single&&o.value===null&&P(),e.actions.closeCombobox()}),i=E(t=>{var T,H,v;let p=(T=t.relatedTarget)!=null?T:_e.find(u=>u!==t.currentTarget);(H=e.state.buttonElement)!=null&&H.contains(p)||(v=e.state.optionsElement)!=null&&v.contains(p)||o.disabled||o.immediate&&e.state.comboboxState!==r.Open&&m.microTask(()=>{te(()=>e.actions.openCombobox()),e.actions.setActivationTrigger(Z.Focus)})}),S=ye(),$=uo(),{isFocused:U,focusProps:s}=Ce({autoFocus:c}),{isHovered:W,hoverProps:ae}=ve({isDisabled:l}),J=R(e,t=>t.optionsElement),q=X(()=>({open:w===r.Open,disabled:l,invalid:o.invalid,hover:W,focus:U,autofocus:c}),[o,W,U,c,l,o.invalid]),j=ge({ref:f,id:C,role:"combobox",type:n,"aria-controls":J==null?void 0:J.id,"aria-expanded":w===r.Open,"aria-activedescendant":R(e,e.selectors.activeDescendantId),"aria-labelledby":S,"aria-describedby":$,"aria-autocomplete":"list",defaultValue:(ee=(Y=y.defaultValue)!=null?Y:o.defaultValue!==void 0?h==null?void 0:h(o.defaultValue):null)!=null?ee:o.defaultValue,disabled:l||void 0,autoFocus:c,onCompositionStart:I,onCompositionEnd:N,onKeyDown:z,onChange:K,onFocus:i,onBlur:k},s,ae);return ie()({ourProps:j,theirProps:x,slot:q,defaultTag:Co,name:"Combobox.Input"})}let Po="button";function Eo(y,O){let e=se("Combobox.Button"),o=ne("Combobox.Button"),[V,A]=Te(null),C=me(O,A,e.actions.setButtonElement),b=be(),{id:h=`headlessui-combobox-button-${b}`,disabled:l=o.disabled||!1,autoFocus:c=!1,...n}=y,x=R(e,i=>i.inputElement),a=Ae(x),g=E(i=>{switch(i.key){case L.Space:case L.Enter:i.preventDefault(),i.stopPropagation(),e.state.comboboxState===r.Closed&&te(()=>e.actions.openCombobox()),a();return;case L.ArrowDown:i.preventDefault(),i.stopPropagation(),e.state.comboboxState===r.Closed&&(te(()=>e.actions.openCombobox()),e.state.dataRef.current.value||e.actions.goToOption({focus:M.First})),a();return;case L.ArrowUp:i.preventDefault(),i.stopPropagation(),e.state.comboboxState===r.Closed&&(te(()=>e.actions.openCombobox()),e.state.dataRef.current.value||e.actions.goToOption({focus:M.Last})),a();return;case L.Escape:if(e.state.comboboxState!==r.Open)return;i.preventDefault(),e.state.optionsElement&&!o.optionsPropsRef.current.static&&i.stopPropagation(),te(()=>e.actions.closeCombobox()),a();return;default:return}}),f=E(i=>{i.preventDefault(),!lo(i.currentTarget)&&(i.button===Fe.Left&&(e.state.comboboxState===r.Open?e.actions.closeCombobox():e.actions.openCombobox()),a())}),B=ye([h]),{isFocusVisible:w,focusProps:F}=Ce({autoFocus:c}),{isHovered:m,hoverProps:P}=ve({isDisabled:l}),{pressed:d,pressProps:_}=Le({disabled:l}),[I,N]=R(e,i=>[i.comboboxState,i.optionsElement]),z=X(()=>({open:I===r.Open,active:d||I===r.Open,disabled:l,invalid:o.invalid,value:o.value,hover:m,focus:w}),[o,m,w,d,l,I]),K=ge({ref:C,id:h,type:ze(y,V),tabIndex:-1,"aria-haspopup":"listbox","aria-controls":N==null?void 0:N.id,"aria-expanded":I===r.Open,"aria-labelledby":B,disabled:l||void 0,autoFocus:c,onMouseDown:f,onKeyDown:g},F,P,_);return ie()({ourProps:K,theirProps:n,slot:z,defaultTag:Po,name:"Combobox.Button"})}let Oo="div",ho=De.RenderStrategy|De.Static;function Ao(y,O){var T,H,v;let e=be(),{id:o=`headlessui-combobox-options-${e}`,hold:V=!1,anchor:A,portal:C=!1,modal:b=!0,transition:h=!1,...l}=y,c=se("Combobox.Options"),n=ne("Combobox.Options"),x=eo(A);x&&(C=!0);let[a,g]=Ye(x),[f,B]=Te(null),w=Qe(),F=me(O,x?a:null,c.actions.setOptionsElement,B),[m,P,d,_,I]=R(c,u=>[u.comboboxState,u.inputElement,u.buttonElement,u.optionsElement,u.activationTrigger]),N=xe(P||d),z=xe(_),K=ro(),[k,i]=$e(h,f,K!==null?(K&ce.Open)===ce.Open:m===r.Open);He(k,P,c.actions.closeCombobox);let S=n.__demoMode?!1:b&&m===r.Open;Ke(S,z);let $=n.__demoMode?!1:b&&m===r.Open;ke($,{allowed:re(()=>[P,d,_],[P,d,_])}),Q(()=>{var u;n.optionsPropsRef.current.static=(u=y.static)!=null?u:!1},[n.optionsPropsRef,y.static]),Q(()=>{n.optionsPropsRef.current.hold=V},[n.optionsPropsRef,V]),Je(m===r.Open,{container:_,accept(u){return u.getAttribute("role")==="option"?NodeFilter.FILTER_REJECT:u.hasAttribute("role")?NodeFilter.FILTER_SKIP:NodeFilter.FILTER_ACCEPT},walk(u){u.setAttribute("role","none")}});let U=ye([d==null?void 0:d.id]),s=X(()=>({open:m===r.Open,option:void 0}),[m]),W=E(()=>{c.actions.setActivationTrigger(Z.Pointer)}),ae=E(u=>{u.preventDefault(),c.actions.setActivationTrigger(Z.Pointer)}),J=ge(x?w():{},{"aria-labelledby":U,role:"listbox","aria-multiselectable":n.mode===G.Multi?!0:void 0,id:o,ref:F,style:{...l.style,...g,"--input-width":he(P,!0).width,"--button-width":he(d,!0).width},onWheel:I===Z.Pointer?void 0:W,onMouseDown:ae,...Xe(i)}),q=k&&m===r.Closed,j=Re(q,(T=n.virtual)==null?void 0:T.options),pe=Re(q,n.value),Y=E(u=>n.compare(pe,u)),ee=X(()=>{if(!n.virtual)return n;if(j===void 0)throw new Error("Missing `options` in virtual mode");return j!==n.virtual.options?{...n,virtual:{...n.virtual,options:j}}:n},[n,j,(H=n.virtual)==null?void 0:H.options]);n.virtual&&Object.assign(l,{children:D.createElement(ue.Provider,{value:ee},D.createElement(xo,{slot:s},l.children))});let t=ie(),p=X(()=>n.mode===G.Multi?n:{...n,isSelected:Y},[n,Y]);return D.createElement(mo,{enabled:C?y.static||k:!1,ownerDocument:N},D.createElement(ue.Provider,{value:p},t({ourProps:J,theirProps:{...l,children:D.createElement(to,{freeze:q},typeof l.children=="function"?(v=l.children)==null?void 0:v.call(l,s):l.children)},slot:s,defaultTag:Oo,features:ho,visible:k,name:"Combobox.Options"})))}let Io="div";function Ro(y,O){var S,$,U;let e=ne("Combobox.Option"),o=se("Combobox.Option"),V=be(),{id:A=`headlessui-combobox-option-${V}`,value:C,disabled:b=(U=($=(S=e.virtual)==null?void 0:S.disabled)==null?void 0:$.call(S,C))!=null?U:!1,order:h=null,...l}=y,[c]=R(o,s=>[s.inputElement]),n=Ae(c),x=R(o,re(s=>o.selectors.isActive(s,C,A),[C,A])),a=e.isSelected(C),g=de(null),f=Ue({disabled:b,value:C,domRef:g,order:h}),B=Oe(Se),w=me(O,g,B?B.measureElement:null),F=E(()=>{o.actions.setIsTyping(!1),o.actions.onChange(C)});Q(()=>o.actions.registerOption(A,f),[f,A]);let m=R(o,re(s=>o.selectors.shouldScrollIntoView(s,C,A),[C,A]));Q(()=>{if(m)return io().requestAnimationFrame(()=>{var s,W;(W=(s=g.current)==null?void 0:s.scrollIntoView)==null||W.call(s,{block:"nearest"})})},[m,g]);let P=E(s=>{s.preventDefault(),s.button===Fe.Left&&(b||(F(),so()||requestAnimationFrame(()=>n()),e.mode===G.Single&&o.actions.closeCombobox()))}),d=E(()=>{if(b)return o.actions.goToOption({focus:M.Nothing});let s=e.calculateIndex(C);o.actions.goToOption({focus:M.Specific,idx:s})}),_=We(),I=E(s=>_.update(s)),N=E(s=>{if(!_.wasMoved(s)||b||x)return;let W=e.calculateIndex(C);o.actions.goToOption({focus:M.Specific,idx:W},Z.Pointer)}),z=E(s=>{_.wasMoved(s)&&(b||x&&(e.optionsPropsRef.current.hold||o.actions.goToOption({focus:M.Nothing})))}),K=X(()=>({active:x,focus:x,selected:a,disabled:b}),[x,a,b]),k={id:A,ref:w,role:"option",tabIndex:b===!0?void 0:-1,"aria-disabled":b===!0?!0:void 0,"aria-selected":a,disabled:void 0,onMouseDown:P,onFocus:d,onPointerEnter:I,onMouseEnter:I,onPointerMove:N,onMouseMove:N,onPointerLeave:z,onMouseLeave:z};return ie()({ourProps:k,theirProps:l,slot:K,defaultTag:Io,name:"Combobox.Option"})}let _o=le(yo),Do=le(Eo),Fo=le(vo),So=po,Mo=le(Ao),Lo=le(Ro),Dt=Object.assign(_o,{Input:Fo,Button:Do,Label:So,Options:Mo,Option:Lo});export{Dt as Combobox,Do as ComboboxButton,Fo as ComboboxInput,So as ComboboxLabel,Lo as ComboboxOption,Mo as ComboboxOptions};
