import React, { useState } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { Button } from '../../components/ui/Button';
import { Input } from '../../components/ui/Input';
import { toast } from 'react-hot-toast';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { 
  faFileInvoiceDollar, 
  faCalendarAlt, 
  faDownload, 
  faFilter,
  faChartBar,
  faFileExcel,
  faFilePdf,
  faSearch,
  faMoneyBillWave,
  faCheckCircle,
  faTimesCircle,
  faClock,
  faExclamationTriangle,
  faUser,
  faBuilding,
  faPercent,
  faChartLine,
  faInfoCircle,
  faCreditCard
} from '@fortawesome/free-solid-svg-icons';

// Schema de validación para filtros de reporte
const reporteFacturacionSchema = z.object({
  fecha_inicio: z.string().min(1, 'Fecha de inicio requerida'),
  fecha_fin: z.string().min(1, 'Fecha de fin requerida'),
  tipo_reporte: z.enum(['general', 'por_estado', 'por_paciente', 'ingresos', 'cartera', 'dian'], {
    required_error: 'Debe seleccionar un tipo de reporte'
  }),
  estado_factura: z.enum(['todas', 'PENDIENTE', 'PAGADA', 'ANULADA', 'VENCIDA']).default('todas'),
  paciente_id: z.string().optional(),
  incluir_anuladas: z.boolean().default(false),
  formato_exportacion: z.enum(['excel', 'pdf', 'csv']).default('excel'),
});

type ReporteFacturacionFormData = z.infer<typeof reporteFacturacionSchema>;

// Datos mock para el reporte
const datosFacturacion = [
  {
    id: 1,
    numero_factura: 'FACT-2024-001',
    paciente: 'Juan Pérez',
    documento: '12345678',
    fecha_emision: '2024-01-15',
    fecha_vencimiento: '2024-02-15',
    monto_total: 250000,
    monto_pagado: 250000,
    saldo_pendiente: 0,
    estado: 'PAGADA',
    tipo_servicio: 'Consulta médica',
    cufe: 'a1b2c3d4e5f6g7h8i9j0',
    metodo_pago: 'Tarjeta de crédito',
    dias_vencimiento: 0
  },
  {
    id: 2,
    numero_factura: 'FACT-2024-002',
    paciente: 'María González',
    documento: '87654321',
    fecha_emision: '2024-01-18',
    fecha_vencimiento: '2024-02-18',
    monto_total: 450000,
    monto_pagado: 0,
    saldo_pendiente: 450000,
    estado: 'PENDIENTE',
    tipo_servicio: 'Cirugía menor',
    cufe: 'b2c3d4e5f6g7h8i9j0k1',
    metodo_pago: null,
    dias_vencimiento: 15
  },
  {
    id: 3,
    numero_factura: 'FACT-2024-003',
    paciente: 'Carlos López',
    documento: '11223344',
    fecha_emision: '2024-01-10',
    fecha_vencimiento: '2024-02-10',
    monto_total: 180000,
    monto_pagado: 0,
    saldo_pendiente: 180000,
    estado: 'VENCIDA',
    tipo_servicio: 'Hospitalización',
    cufe: 'c3d4e5f6g7h8i9j0k1l2',
    metodo_pago: null,
    dias_vencimiento: -5
  }
];

const estadisticasFacturacion = {
  total_facturas: 1247,
  facturas_pagadas: 892,
  facturas_pendientes: 234,
  facturas_vencidas: 121,
  monto_total_facturado: 145678900,
  monto_total_recaudado: 98456700,
  cartera_pendiente: 47222200,
  promedio_dias_pago: 18.5,
  tasa_recaudo: 67.6
};

export const ReportesFacturacion: React.FC = () => {
  const [isGenerating, setIsGenerating] = useState(false);
  const [reporteGenerado, setReporteGenerado] = useState(false);
  const [datosReporte, setDatosReporte] = useState(datosFacturacion);

  const {
    register,
    handleSubmit,
    formState: { errors },
    watch
  } = useForm<ReporteFacturacionFormData>({
    resolver: zodResolver(reporteFacturacionSchema),
    defaultValues: {
      fecha_inicio: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
      fecha_fin: new Date().toISOString().split('T')[0],
      tipo_reporte: 'general',
      estado_factura: 'todas',
      incluir_anuladas: false,
      formato_exportacion: 'excel'
    }
  });

  const tipoReporte = watch('tipo_reporte');
  const estadoFactura = watch('estado_factura');

  const onSubmit = async (data: ReporteFacturacionFormData) => {
    setIsGenerating(true);
    try {
      console.log('Generando reporte de facturación con filtros:', data);
      
      // Simular generación de reporte
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      // Filtrar datos según criterios
      let datosFiltrados = datosFacturacion;
      
      if (data.estado_factura !== 'todas') {
        datosFiltrados = datosFiltrados.filter(item => item.estado === data.estado_factura);
      }
      
      if (data.paciente_id) {
        datosFiltrados = datosFiltrados.filter(item => 
          item.paciente.toLowerCase().includes(data.paciente_id!.toLowerCase())
        );
      }
      
      setDatosReporte(datosFiltrados);
      setReporteGenerado(true);
      toast.success('Reporte de facturación generado exitosamente');
    } catch (error) {
      console.error('Error al generar reporte:', error);
      toast.error('Error al generar el reporte');
    } finally {
      setIsGenerating(false);
    }
  };

  const exportarReporte = (formato: string) => {
    toast.success(`Exportando reporte en formato ${formato.toUpperCase()}`);
    // TODO: Implementar exportación real
  };

  const getEstadoColor = (estado: string) => {
    switch (estado) {
      case 'PAGADA':
        return 'bg-success/20 text-success';
      case 'PENDIENTE':
        return 'bg-warning/20 text-warning';
      case 'VENCIDA':
        return 'bg-error/20 text-error';
      case 'ANULADA':
        return 'bg-secondary/20 text-secondary';
      default:
        return 'bg-secondary/20 text-secondary';
    }
  };

  const getEstadoIcon = (estado: string) => {
    switch (estado) {
      case 'PAGADA':
        return faCheckCircle;
      case 'PENDIENTE':
        return faClock;
      case 'VENCIDA':
        return faExclamationTriangle;
      case 'ANULADA':
        return faTimesCircle;
      default:
        return faInfoCircle;
    }
  };

  const formatCurrency = (value: number) => {
    return new Intl.NumberFormat('es-CO', {
      style: 'currency',
      currency: 'COP',
      minimumFractionDigits: 0
    }).format(value);
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="bg-card p-6 rounded-lg shadow-lg">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-2xl font-bold text-primary flex items-center">
              <FontAwesomeIcon icon={faFileInvoiceDollar} className="mr-2" />
              Reportes de Facturación
            </h1>
            <p className="text-muted mt-1">
              Genere reportes detallados sobre facturación, recaudo, cartera y cumplimiento DIAN
            </p>
          </div>
          <div className="text-right">
            <div className="text-sm text-muted">Última actualización</div>
            <div className="text-primary font-medium">{new Date().toLocaleString()}</div>
          </div>
        </div>
      </div>

      {/* Estadísticas Rápidas */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-9 gap-4">
        <div className="bg-card p-4 rounded-lg shadow">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-muted">Total Facturas</p>
              <p className="text-2xl font-bold text-primary">{estadisticasFacturacion.total_facturas}</p>
            </div>
            <FontAwesomeIcon icon={faFileInvoiceDollar} className="text-primary text-xl" />
          </div>
        </div>

        <div className="bg-card p-4 rounded-lg shadow">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-muted">Pagadas</p>
              <p className="text-2xl font-bold text-success">{estadisticasFacturacion.facturas_pagadas}</p>
            </div>
            <FontAwesomeIcon icon={faCheckCircle} className="text-success text-xl" />
          </div>
        </div>

        <div className="bg-card p-4 rounded-lg shadow">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-muted">Pendientes</p>
              <p className="text-2xl font-bold text-warning">{estadisticasFacturacion.facturas_pendientes}</p>
            </div>
            <FontAwesomeIcon icon={faClock} className="text-warning text-xl" />
          </div>
        </div>

        <div className="bg-card p-4 rounded-lg shadow">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-muted">Vencidas</p>
              <p className="text-2xl font-bold text-error">{estadisticasFacturacion.facturas_vencidas}</p>
            </div>
            <FontAwesomeIcon icon={faExclamationTriangle} className="text-error text-xl" />
          </div>
        </div>

        <div className="bg-card p-4 rounded-lg shadow">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-muted">Total Facturado</p>
              <p className="text-xl font-bold text-primary">{formatCurrency(estadisticasFacturacion.monto_total_facturado)}</p>
            </div>
            <FontAwesomeIcon icon={faMoneyBillWave} className="text-primary text-xl" />
          </div>
        </div>

        <div className="bg-card p-4 rounded-lg shadow">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-muted">Recaudado</p>
              <p className="text-xl font-bold text-success">{formatCurrency(estadisticasFacturacion.monto_total_recaudado)}</p>
            </div>
            <FontAwesomeIcon icon={faChartBar} className="text-success text-xl" />
          </div>
        </div>

        <div className="bg-card p-4 rounded-lg shadow">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-muted">Cartera</p>
              <p className="text-xl font-bold text-warning">{formatCurrency(estadisticasFacturacion.cartera_pendiente)}</p>
            </div>
            <FontAwesomeIcon icon={faUser} className="text-warning text-xl" />
          </div>
        </div>

        <div className="bg-card p-4 rounded-lg shadow">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-muted">Días Pago Prom.</p>
              <p className="text-2xl font-bold text-info">{estadisticasFacturacion.promedio_dias_pago}</p>
            </div>
            <FontAwesomeIcon icon={faCalendarAlt} className="text-info text-xl" />
          </div>
        </div>

        <div className="bg-card p-4 rounded-lg shadow">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-muted">Tasa Recaudo</p>
              <p className="text-2xl font-bold text-success">{estadisticasFacturacion.tasa_recaudo}%</p>
            </div>
            <FontAwesomeIcon icon={faPercent} className="text-success text-xl" />
          </div>
        </div>
      </div>

      {/* Formulario de Filtros */}
      <div className="bg-card p-6 rounded-lg shadow-lg">
        <h3 className="text-lg font-semibold text-primary mb-4 flex items-center">
          <FontAwesomeIcon icon={faFilter} className="mr-2" />
          🔍 Filtros de Reporte
        </h3>

        <form onSubmit={handleSubmit(onSubmit)} className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            <div>
              <label className="block text-sm font-medium text-secondary mb-1">
                Fecha de Inicio *
              </label>
              <Input
                {...register('fecha_inicio')}
                type="date"
                error={errors.fecha_inicio?.message}
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-secondary mb-1">
                Fecha de Fin *
              </label>
              <Input
                {...register('fecha_fin')}
                type="date"
                error={errors.fecha_fin?.message}
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-secondary mb-1">
                Tipo de Reporte *
              </label>
              <select
                {...register('tipo_reporte')}
                className="w-full p-3 rounded-lg bg-secondary border border-color text-primary focus:ring-2 focus:ring-primary focus:border-primary transition-colors"
              >
                <option value="general">📊 Reporte General</option>
                <option value="por_estado">📈 Por Estado de Factura</option>
                <option value="por_paciente">👤 Por Paciente</option>
                <option value="ingresos">💰 Análisis de Ingresos</option>
                <option value="cartera">📋 Cartera de Clientes</option>
                <option value="dian">🏛️ Cumplimiento DIAN</option>
              </select>
              {errors.tipo_reporte && (
                <p className="mt-1 text-sm text-error">{errors.tipo_reporte.message}</p>
              )}
            </div>

            <div>
              <label className="block text-sm font-medium text-secondary mb-1">
                Estado de Factura
              </label>
              <select
                {...register('estado_factura')}
                className="w-full p-3 rounded-lg bg-secondary border border-color text-primary focus:ring-2 focus:ring-primary focus:border-primary transition-colors"
              >
                <option value="todas">Todas</option>
                <option value="PENDIENTE">⏳ Pendientes</option>
                <option value="PAGADA">✅ Pagadas</option>
                <option value="VENCIDA">⚠️ Vencidas</option>
                <option value="ANULADA">❌ Anuladas</option>
              </select>
            </div>

            <div>
              <label className="block text-sm font-medium text-secondary mb-1">
                Paciente (Opcional)
              </label>
              <Input
                {...register('paciente_id')}
                placeholder="Nombre del paciente"
                error={errors.paciente_id?.message}
              />
            </div>

            <div className="flex items-center space-x-3">
              <input
                {...register('incluir_anuladas')}
                type="checkbox"
                className="w-4 h-4 text-primary border-color rounded focus:ring-primary"
              />
              <label className="text-sm font-medium text-secondary">
                Incluir facturas anuladas
              </label>
            </div>
          </div>

          <div className="flex justify-between items-center pt-4">
            <div className="flex items-center gap-4">
              <label className="text-sm font-medium text-secondary">Formato de Exportación:</label>
              <div className="flex gap-2">
                <label className="flex items-center">
                  <input
                    {...register('formato_exportacion')}
                    type="radio"
                    value="excel"
                    className="mr-2"
                  />
                  <FontAwesomeIcon icon={faFileExcel} className="text-success mr-1" />
                  Excel
                </label>
                <label className="flex items-center">
                  <input
                    {...register('formato_exportacion')}
                    type="radio"
                    value="pdf"
                    className="mr-2"
                  />
                  <FontAwesomeIcon icon={faFilePdf} className="text-error mr-1" />
                  PDF
                </label>
              </div>
            </div>

            <Button
              type="submit"
              disabled={isGenerating}
              className="min-w-[150px]"
            >
              {isGenerating ? (
                <>
                  <span className="mr-2 inline-block w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"></span>
                  Generando...
                </>
              ) : (
                <>
                  <FontAwesomeIcon icon={faSearch} className="mr-2" />
                  Generar Reporte
                </>
              )}
            </Button>
          </div>
        </form>
      </div>

      {/* Información del Tipo de Reporte */}
      {tipoReporte && (
        <div className="bg-card p-4 rounded-lg shadow-lg">
          <h4 className="text-md font-semibold text-primary mb-3 flex items-center">
            <FontAwesomeIcon icon={faInfoCircle} className="mr-2" />
            ℹ️ Información del Reporte: {tipoReporte.replace('_', ' ').toUpperCase()}
          </h4>

          <div className="text-sm text-muted">
            {tipoReporte === 'general' && (
              <ul className="space-y-1">
                <li>• Vista completa de todas las facturas del período</li>
                <li>• Incluye montos, estados y fechas de vencimiento</li>
                <li>• Análisis de cartera y recaudo</li>
                <li>• Indicadores de gestión financiera</li>
              </ul>
            )}
            {tipoReporte === 'por_estado' && (
              <ul className="space-y-1">
                <li>• Clasificación de facturas por estado</li>
                <li>• Análisis de flujo de caja</li>
                <li>• Identificación de facturas críticas</li>
                <li>• Métricas de gestión de cobranza</li>
              </ul>
            )}
            {tipoReporte === 'por_paciente' && (
              <ul className="space-y-1">
                <li>• Historial de facturación por paciente</li>
                <li>• Análisis de comportamiento de pago</li>
                <li>• Identificación de clientes frecuentes</li>
                <li>• Gestión personalizada de cartera</li>
              </ul>
            )}
            {tipoReporte === 'ingresos' && (
              <ul className="space-y-1">
                <li>• Análisis de ingresos por período</li>
                <li>• Tendencias de facturación</li>
                <li>• Proyecciones financieras</li>
                <li>• Comparativos mensuales/anuales</li>
              </ul>
            )}
            {tipoReporte === 'cartera' && (
              <ul className="space-y-1">
                <li>• Estado de cartera por edades</li>
                <li>• Análisis de riesgo crediticio</li>
                <li>• Estrategias de cobranza</li>
                <li>• Provisiones y castigos</li>
              </ul>
            )}
            {tipoReporte === 'dian' && (
              <ul className="space-y-1">
                <li>• Cumplimiento normativo DIAN</li>
                <li>• Validación de CUFE y firmas digitales</li>
                <li>• Reporte de facturación electrónica</li>
                <li>• Auditoría fiscal</li>
              </ul>
            )}
          </div>
        </div>
      )}

      {/* Resultados del Reporte */}
      {reporteGenerado && (
        <div className="bg-card p-6 rounded-lg shadow-lg">
          <div className="flex justify-between items-center mb-4">
            <h3 className="text-lg font-semibold text-primary flex items-center">
              <FontAwesomeIcon icon={faChartBar} className="mr-2" />
              📋 Resultados del Reporte
            </h3>
            <div className="flex gap-2">
              <Button
                variant="outline"
                onClick={() => exportarReporte('excel')}
                className="text-success border-success hover:bg-success/10"
              >
                <FontAwesomeIcon icon={faFileExcel} className="mr-2" />
                Excel
              </Button>
              <Button
                variant="outline"
                onClick={() => exportarReporte('pdf')}
                className="text-error border-error hover:bg-error/10"
              >
                <FontAwesomeIcon icon={faFilePdf} className="mr-2" />
                PDF
              </Button>
            </div>
          </div>

          {/* Tabla de Resultados */}
          <div className="overflow-x-auto">
            <table className="w-full border-collapse">
              <thead>
                <tr className="bg-secondary/50">
                  <th className="border border-color p-3 text-left text-secondary font-medium">Número</th>
                  <th className="border border-color p-3 text-left text-secondary font-medium">Paciente</th>
                  <th className="border border-color p-3 text-left text-secondary font-medium">Fecha Emisión</th>
                  <th className="border border-color p-3 text-left text-secondary font-medium">Fecha Venc.</th>
                  <th className="border border-color p-3 text-left text-secondary font-medium">Monto Total</th>
                  <th className="border border-color p-3 text-left text-secondary font-medium">Pagado</th>
                  <th className="border border-color p-3 text-left text-secondary font-medium">Saldo</th>
                  <th className="border border-color p-3 text-left text-secondary font-medium">Estado</th>
                  <th className="border border-color p-3 text-left text-secondary font-medium">CUFE</th>
                </tr>
              </thead>
              <tbody>
                {datosReporte.map((item) => (
                  <tr key={item.id} className="hover:bg-secondary/20 transition-colors">
                    <td className="border border-color p-3 text-primary font-medium">{item.numero_factura}</td>
                    <td className="border border-color p-3 text-primary">
                      <div>
                        <div className="font-medium">{item.paciente}</div>
                        <div className="text-sm text-muted">{item.documento}</div>
                      </div>
                    </td>
                    <td className="border border-color p-3 text-primary">
                      {new Date(item.fecha_emision).toLocaleDateString()}
                    </td>
                    <td className="border border-color p-3 text-primary">
                      <span className={
                        item.dias_vencimiento < 0 ? 'text-error' :
                        item.dias_vencimiento <= 5 ? 'text-warning' :
                        'text-primary'
                      }>
                        {new Date(item.fecha_vencimiento).toLocaleDateString()}
                      </span>
                    </td>
                    <td className="border border-color p-3 text-primary font-bold">
                      {formatCurrency(item.monto_total)}
                    </td>
                    <td className="border border-color p-3 text-success font-medium">
                      {formatCurrency(item.monto_pagado)}
                    </td>
                    <td className="border border-color p-3 font-medium">
                      <span className={item.saldo_pendiente > 0 ? 'text-warning' : 'text-success'}>
                        {formatCurrency(item.saldo_pendiente)}
                      </span>
                    </td>
                    <td className="border border-color p-3">
                      <span className={`px-2 py-1 rounded-full text-xs font-medium flex items-center ${getEstadoColor(item.estado)}`}>
                        <FontAwesomeIcon icon={getEstadoIcon(item.estado)} className="mr-1" />
                        {item.estado}
                      </span>
                    </td>
                    <td className="border border-color p-3 text-primary">
                      {item.cufe ? (
                        <span className="text-success flex items-center">
                          <FontAwesomeIcon icon={faCheckCircle} className="mr-1" />
                          Sí
                        </span>
                      ) : (
                        <span className="text-warning flex items-center">
                          <FontAwesomeIcon icon={faClock} className="mr-1" />
                          Pendiente
                        </span>
                      )}
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>

          {/* Resumen del Reporte */}
          <div className="mt-6 grid grid-cols-1 md:grid-cols-4 gap-4">
            <div className="bg-secondary/30 p-4 rounded-lg">
              <h4 className="font-medium text-secondary mb-2">Total de Facturas</h4>
              <p className="text-2xl font-bold text-primary">{datosReporte.length}</p>
            </div>
            <div className="bg-secondary/30 p-4 rounded-lg">
              <h4 className="font-medium text-secondary mb-2">Monto Total</h4>
              <p className="text-2xl font-bold text-primary">
                {formatCurrency(datosReporte.reduce((acc, item) => acc + item.monto_total, 0))}
              </p>
            </div>
            <div className="bg-secondary/30 p-4 rounded-lg">
              <h4 className="font-medium text-secondary mb-2">Total Recaudado</h4>
              <p className="text-2xl font-bold text-success">
                {formatCurrency(datosReporte.reduce((acc, item) => acc + item.monto_pagado, 0))}
              </p>
            </div>
            <div className="bg-secondary/30 p-4 rounded-lg">
              <h4 className="font-medium text-secondary mb-2">Cartera Pendiente</h4>
              <p className="text-2xl font-bold text-warning">
                {formatCurrency(datosReporte.reduce((acc, item) => acc + item.saldo_pendiente, 0))}
              </p>
            </div>
          </div>

          {/* Análisis por Estado */}
          <div className="mt-6 bg-secondary/30 p-4 rounded-lg">
            <h4 className="font-medium text-secondary mb-4">📊 Distribución por Estado</h4>
            <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
              {['PAGADA', 'PENDIENTE', 'VENCIDA', 'ANULADA'].map(estado => {
                const count = datosReporte.filter(item => item.estado === estado).length;
                const monto = datosReporte.filter(item => item.estado === estado).reduce((acc, item) => acc + item.monto_total, 0);
                const percentage = datosReporte.length > 0 ? (count / datosReporte.length * 100).toFixed(1) : '0';
                return (
                  <div key={estado} className="text-center">
                    <div className="text-2xl font-bold text-primary">{count}</div>
                    <div className="text-sm text-muted">{estado}</div>
                    <div className="text-xs text-info">{percentage}%</div>
                    <div className="text-xs text-muted">{formatCurrency(monto)}</div>
                  </div>
                );
              })}
            </div>
          </div>

          {/* Análisis de Cartera por Edades */}
          <div className="mt-6 bg-secondary/30 p-4 rounded-lg">
            <h4 className="font-medium text-secondary mb-4">📅 Análisis de Cartera por Edades</h4>
            <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
              <div className="text-center">
                <div className="text-2xl font-bold text-success">
                  {datosReporte.filter(item => item.dias_vencimiento >= 0 && item.dias_vencimiento <= 30).length}
                </div>
                <div className="text-sm text-muted">0-30 días</div>
                <div className="text-xs text-success">Al día</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-warning">
                  {datosReporte.filter(item => item.dias_vencimiento > 30 && item.dias_vencimiento <= 60).length}
                </div>
                <div className="text-sm text-muted">31-60 días</div>
                <div className="text-xs text-warning">Atención</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-error">
                  {datosReporte.filter(item => item.dias_vencimiento > 60 && item.dias_vencimiento <= 90).length}
                </div>
                <div className="text-sm text-muted">61-90 días</div>
                <div className="text-xs text-error">Crítico</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-error">
                  {datosReporte.filter(item => item.dias_vencimiento > 90).length}
                </div>
                <div className="text-sm text-muted">+90 días</div>
                <div className="text-xs text-error">Muy crítico</div>
              </div>
            </div>
          </div>

          {/* Alertas y Recomendaciones */}
          <div className="mt-6 space-y-4">
            {datosReporte.filter(item => item.estado === 'VENCIDA').length > 0 && (
              <div className="bg-error/20 border border-error/30 rounded-lg p-4">
                <h5 className="font-medium text-error mb-2 flex items-center">
                  <FontAwesomeIcon icon={faExclamationTriangle} className="mr-2" />
                  🚨 Facturas Vencidas
                </h5>
                <p className="text-sm text-muted">
                  Hay {datosReporte.filter(item => item.estado === 'VENCIDA').length} facturas vencidas que requieren gestión de cobranza inmediata.
                </p>
              </div>
            )}

            {datosReporte.filter(item => item.estado === 'PENDIENTE' && item.dias_vencimiento <= 5).length > 0 && (
              <div className="bg-warning/20 border border-warning/30 rounded-lg p-4">
                <h5 className="font-medium text-warning mb-2 flex items-center">
                  <FontAwesomeIcon icon={faClock} className="mr-2" />
                  ⚠️ Próximas a Vencer
                </h5>
                <p className="text-sm text-muted">
                  Hay {datosReporte.filter(item => item.estado === 'PENDIENTE' && item.dias_vencimiento <= 5).length} facturas que vencen en los próximos 5 días.
                </p>
              </div>
            )}

            {datosReporte.filter(item => !item.cufe).length > 0 && (
              <div className="bg-info/20 border border-info/30 rounded-lg p-4">
                <h5 className="font-medium text-info mb-2 flex items-center">
                  <FontAwesomeIcon icon={faInfoCircle} className="mr-2" />
                  📋 Cumplimiento DIAN
                </h5>
                <p className="text-sm text-muted">
                  Hay {datosReporte.filter(item => !item.cufe).length} facturas sin CUFE que requieren procesamiento para cumplimiento DIAN.
                </p>
              </div>
            )}

            <div className="bg-success/20 border border-success/30 rounded-lg p-4">
              <h5 className="font-medium text-success mb-2 flex items-center">
                <FontAwesomeIcon icon={faChartLine} className="mr-2" />
                💡 Recomendaciones
              </h5>
              <ul className="text-sm text-muted space-y-1">
                <li>• Implementar recordatorios automáticos 5 días antes del vencimiento</li>
                <li>• Establecer descuentos por pronto pago para mejorar flujo de caja</li>
                <li>• Revisar políticas de crédito para clientes con facturas vencidas</li>
                <li>• Generar CUFE para todas las facturas pendientes</li>
                <li>• Considerar factoring para facturas de más de 60 días</li>
              </ul>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};
