import React, { useState } from 'react';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import {
  faClock,
  faPlus,
  faSearch,
  faEdit,
  faTrash,
  faCheckCircle,
  faTimesCircle,
  faExclamationTriangle,
  faMoneyBillWave,
  faUserClock,
  faPercent,
  faChartBar,
  faFilter
} from '@fortawesome/free-solid-svg-icons';
import { Button } from '../../../components/ui/Button';
import { Input } from '../../../components/ui/Input';
import { toast } from 'react-hot-toast';

// Datos mock de horas extras
const mockHorasExtras = [
  {
    id: 1,
    empleado_nombre: '<PERSON>',
    fecha: '2024-01-15',
    hora_inicio: '18:00',
    hora_fin: '22:00',
    total_horas: 4,
    tipo_hora: 'Nocturna',
    factor_multiplicador: 1.35,
    valor_hora: 20833,
    valor_total: 112500,
    motivo: 'Emergencia médica',
    aprobado_por_nombre: '<PERSON>. <PERSON>',
    estado: 'Aprobada',
    observaciones: 'Atención de emergencia en urgencias'
  },
  {
    id: 2,
    empleado_nombre: 'María Alejandra López',
    fecha: '2024-01-20',
    hora_inicio: '14:00',
    hora_fin: '18:00',
    total_horas: 4,
    tipo_hora: 'Dominical',
    factor_multiplicador: 1.75,
    valor_hora: 16667,
    valor_total: 116667,
    motivo: 'Cobertura de turno dominical',
    estado: 'Pendiente',
    observaciones: 'Cobertura por ausencia de colega'
  },
  {
    id: 3,
    empleado_nombre: 'Ana María González',
    fecha: '2024-01-18',
    hora_inicio: '08:00',
    hora_fin: '12:00',
    total_horas: 4,
    tipo_hora: 'Diurna',
    factor_multiplicador: 1.25,
    valor_hora: 25000,
    valor_total: 125000,
    motivo: 'Proyecto especial',
    aprobado_por_nombre: 'Dr. Carlos Rodríguez',
    estado: 'Pagada',
    observaciones: 'Trabajo en proyecto de mejora de procesos'
  },
  {
    id: 4,
    empleado_nombre: 'Luis Fernando Martínez',
    fecha: '2024-01-22',
    hora_inicio: '20:00',
    hora_fin: '06:00',
    total_horas: 10,
    tipo_hora: 'Festiva',
    factor_multiplicador: 2.0,
    valor_hora: 30000,
    valor_total: 600000,
    motivo: 'Cirugía de emergencia en día festivo',
    estado: 'Pendiente',
    observaciones: 'Cirugía compleja que requirió tiempo adicional'
  }
];

const GestionHorasExtrasSimple: React.FC = () => {
  const [horasExtras, setHorasExtras] = useState(mockHorasExtras);
  const [searchTerm, setSearchTerm] = useState('');
  const [filtroEstado, setFiltroEstado] = useState<string>('todos');
  const [filtroTipo, setFiltroTipo] = useState<string>('todos');

  const filteredHorasExtras = horasExtras.filter(horaExtra => {
    const matchesSearch = horaExtra.empleado_nombre?.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         horaExtra.motivo.toLowerCase().includes(searchTerm.toLowerCase());

    const matchesEstado = filtroEstado === 'todos' || horaExtra.estado === filtroEstado;
    const matchesTipo = filtroTipo === 'todos' || horaExtra.tipo_hora === filtroTipo;

    return matchesSearch && matchesEstado && matchesTipo;
  });

  const getEstadoColor = (estado: string) => {
    switch (estado) {
      case 'Aprobada':
        return 'bg-success/20 text-success';
      case 'Pendiente':
        return 'bg-warning/20 text-warning';
      case 'Rechazada':
        return 'bg-error/20 text-error';
      case 'Pagada':
        return 'bg-info/20 text-info';
      default:
        return 'bg-secondary/20 text-secondary';
    }
  };

  const getEstadoIcon = (estado: string) => {
    switch (estado) {
      case 'Aprobada':
        return faCheckCircle;
      case 'Pendiente':
        return faClock;
      case 'Rechazada':
        return faTimesCircle;
      case 'Pagada':
        return faMoneyBillWave;
      default:
        return faExclamationTriangle;
    }
  };

  const getTipoColor = (tipo: string) => {
    switch (tipo) {
      case 'Diurna':
        return 'text-blue-600';
      case 'Nocturna':
        return 'text-purple-600';
      case 'Dominical':
        return 'text-orange-600';
      case 'Festiva':
        return 'text-red-600';
      default:
        return 'text-gray-600';
    }
  };

  const formatCurrency = (value: number) => {
    return new Intl.NumberFormat('es-CO', {
      style: 'currency',
      currency: 'COP',
      minimumFractionDigits: 0
    }).format(value);
  };

  const handleApprove = (id: number) => {
    setHorasExtras(prev =>
      prev.map(he =>
        he.id === id
          ? { ...he, estado: 'Aprobada', aprobado_por_nombre: 'Usuario Actual' }
          : he
      )
    );
    toast.success('Hora extra aprobada exitosamente');
  };

  const handleReject = (id: number) => {
    setHorasExtras(prev =>
      prev.map(he =>
        he.id === id
          ? { ...he, estado: 'Rechazada' }
          : he
      )
    );
    toast.success('Hora extra rechazada');
  };

  const handleDelete = (id: number) => {
    if (window.confirm('¿Está seguro de eliminar esta hora extra?')) {
      setHorasExtras(prev => prev.filter(he => he.id !== id));
      toast.success('Hora extra eliminada exitosamente');
    }
  };

  const totalHorasExtras = horasExtras.reduce((sum, he) => sum + he.total_horas, 0);
  const horasAprobadas = horasExtras.filter(he => he.estado === 'Aprobada').reduce((sum, he) => sum + he.total_horas, 0);
  const horasPendientes = horasExtras.filter(he => he.estado === 'Pendiente').reduce((sum, he) => sum + he.total_horas, 0);
  const valorTotal = horasExtras.reduce((sum, he) => sum + he.valor_total, 0);

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="bg-card p-6 rounded-lg shadow-lg">
        <div className="flex justify-between items-center">
          <div>
            <h2 className="text-2xl font-bold text-primary flex items-center">
              <FontAwesomeIcon icon={faClock} className="mr-2" />
              Gestión de Horas Extras
            </h2>
            <p className="text-muted mt-1">
              Control y administración de horas extras del personal
            </p>
          </div>
          <Button onClick={() => toast.info('Modal de registro próximamente')}>
            <FontAwesomeIcon icon={faPlus} className="mr-2" />
            Registrar Horas Extras
          </Button>
        </div>
      </div>

      {/* Estadísticas */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <div className="bg-card p-6 rounded-lg shadow">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-muted">Total Horas</p>
              <p className="text-2xl font-bold text-primary">{totalHorasExtras}</p>
            </div>
            <FontAwesomeIcon icon={faUserClock} className="text-primary text-2xl" />
          </div>
        </div>

        <div className="bg-card p-6 rounded-lg shadow">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-muted">Aprobadas</p>
              <p className="text-2xl font-bold text-success">{horasAprobadas}</p>
            </div>
            <FontAwesomeIcon icon={faCheckCircle} className="text-success text-2xl" />
          </div>
        </div>

        <div className="bg-card p-6 rounded-lg shadow">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-muted">Pendientes</p>
              <p className="text-2xl font-bold text-warning">{horasPendientes}</p>
            </div>
            <FontAwesomeIcon icon={faClock} className="text-warning text-2xl" />
          </div>
        </div>

        <div className="bg-card p-6 rounded-lg shadow">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-muted">Valor Total</p>
              <p className="text-xl font-bold text-info">{formatCurrency(valorTotal)}</p>
            </div>
            <FontAwesomeIcon icon={faMoneyBillWave} className="text-info text-2xl" />
          </div>
        </div>
      </div>

      {/* Filtros */}
      <div className="bg-card p-6 rounded-lg shadow-lg">
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <div>
            <label className="block text-sm font-medium text-secondary mb-1">
              Buscar
            </label>
            <div className="relative">
              <FontAwesomeIcon icon={faSearch} className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted" />
              <Input
                type="text"
                placeholder="Empleado, motivo..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10"
              />
            </div>
          </div>

          <div>
            <label className="block text-sm font-medium text-secondary mb-1">
              Estado
            </label>
            <select
              value={filtroEstado}
              onChange={(e) => setFiltroEstado(e.target.value)}
              className="w-full p-3 rounded-lg bg-secondary border border-color text-primary focus:ring-2 focus:ring-primary"
            >
              <option value="todos">Todos los estados</option>
              <option value="Pendiente">Pendiente</option>
              <option value="Aprobada">Aprobada</option>
              <option value="Rechazada">Rechazada</option>
              <option value="Pagada">Pagada</option>
            </select>
          </div>

          <div>
            <label className="block text-sm font-medium text-secondary mb-1">
              Tipo de Hora
            </label>
            <select
              value={filtroTipo}
              onChange={(e) => setFiltroTipo(e.target.value)}
              className="w-full p-3 rounded-lg bg-secondary border border-color text-primary focus:ring-2 focus:ring-primary"
            >
              <option value="todos">Todos los tipos</option>
              <option value="Diurna">Diurna</option>
              <option value="Nocturna">Nocturna</option>
              <option value="Dominical">Dominical</option>
              <option value="Festiva">Festiva</option>
            </select>
          </div>

          <div className="flex items-end">
            <Button variant="outline" className="w-full" onClick={() => {
              setSearchTerm('');
              setFiltroEstado('todos');
              setFiltroTipo('todos');
            }}>
              <FontAwesomeIcon icon={faFilter} className="mr-2" />
              Limpiar Filtros
            </Button>
          </div>
        </div>
      </div>

      {/* Tabla de Horas Extras */}
      <div className="bg-card rounded-lg shadow-lg overflow-hidden">
        <div className="overflow-x-auto">
          <table className="w-full">
            <thead className="bg-secondary/50">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-secondary uppercase tracking-wider">
                  Empleado
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-secondary uppercase tracking-wider">
                  Fecha
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-secondary uppercase tracking-wider">
                  Horario
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-secondary uppercase tracking-wider">
                  Tipo
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-secondary uppercase tracking-wider">
                  Horas
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-secondary uppercase tracking-wider">
                  Valor
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-secondary uppercase tracking-wider">
                  Estado
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-secondary uppercase tracking-wider">
                  Acciones
                </th>
              </tr>
            </thead>
            <tbody className="bg-card divide-y divide-color">
              {filteredHorasExtras.map((horaExtra) => (
                <tr key={horaExtra.id} className="hover:bg-secondary/20 transition-colors">
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div>
                      <div className="text-sm font-medium text-primary">
                        {horaExtra.empleado_nombre}
                      </div>
                      <div className="text-sm text-muted">
                        {horaExtra.motivo}
                      </div>
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-primary">
                    {new Date(horaExtra.fecha).toLocaleDateString()}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-primary">
                    <div>
                      <div>{horaExtra.hora_inicio} - {horaExtra.hora_fin}</div>
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <span className={`text-sm font-medium ${getTipoColor(horaExtra.tipo_hora)}`}>
                      {horaExtra.tipo_hora}
                    </span>
                    <div className="text-xs text-muted">
                      Factor: {horaExtra.factor_multiplicador}x
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="text-sm font-medium text-primary">
                      {horaExtra.total_horas}h
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="text-sm font-medium text-primary">
                      {formatCurrency(horaExtra.valor_total)}
                    </div>
                    <div className="text-sm text-muted">
                      {formatCurrency(horaExtra.valor_hora)}/h
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getEstadoColor(horaExtra.estado)}`}>
                      <FontAwesomeIcon icon={getEstadoIcon(horaExtra.estado)} className="mr-1" />
                      {horaExtra.estado}
                    </span>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                    <div className="flex space-x-2">
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => toast.info(`Editando hora extra de ${horaExtra.empleado_nombre}`)}
                        className="text-blue-600 border-blue-600 hover:bg-blue-50"
                      >
                        <FontAwesomeIcon icon={faEdit} />
                      </Button>
                      {horaExtra.estado === 'Pendiente' && (
                        <>
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => handleApprove(horaExtra.id)}
                            className="text-green-600 border-green-600 hover:bg-green-50"
                          >
                            <FontAwesomeIcon icon={faCheckCircle} />
                          </Button>
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => handleReject(horaExtra.id)}
                            className="text-red-600 border-red-600 hover:bg-red-50"
                          >
                            <FontAwesomeIcon icon={faTimesCircle} />
                          </Button>
                        </>
                      )}
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => handleDelete(horaExtra.id)}
                        className="text-red-600 border-red-600 hover:bg-red-50"
                      >
                        <FontAwesomeIcon icon={faTrash} />
                      </Button>
                    </div>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>

        {filteredHorasExtras.length === 0 && (
          <div className="text-center py-12">
            <FontAwesomeIcon icon={faClock} className="text-muted text-4xl mb-4" />
            <p className="text-muted text-lg">No se encontraron horas extras</p>
            <p className="text-muted">Intenta ajustar los filtros de búsqueda</p>
          </div>
        )}
      </div>
    </div>
  );
};

export default GestionHorasExtrasSimple;
