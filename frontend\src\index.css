@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=JetBrains+Mono:wght@300;400;500;600;700&display=swap');

@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  font-family: 'Inter', system-ui, Avenir, Helvetica, Arial, sans-serif;
  line-height: 1.5;
  font-weight: 400;
  color-scheme: light dark;
  font-synthesis: none;
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;

  /* Tipografía */
  --font-sans: 'Inter', system-ui, sans-serif;
  --font-mono: 'JetBrains Mono', monospace;

  /* Tamaños de texto */
  --text-xs: 0.75rem;     /* 12px */
  --text-sm: 0.875rem;    /* 14px */
  --text-base: 1rem;      /* 16px */
  --text-lg: 1.125rem;    /* 18px */
  --text-xl: 1.25rem;     /* 20px */
  --text-2xl: 1.5rem;     /* 24px */
  --text-3xl: 1.875rem;   /* 30px */
  --text-4xl: 2.25rem;    /* 36px */

  /* Pesos de fuente */
  --font-light: 300;
  --font-normal: 400;
  --font-medium: 500;
  --font-semibold: 600;
  --font-bold: 700;

  /* Espaciado */
  --space-1: 0.25rem;     /* 4px */
  --space-2: 0.5rem;      /* 8px */
  --space-3: 0.75rem;     /* 12px */
  --space-4: 1rem;        /* 16px */
  --space-6: 1.5rem;      /* 24px */
  --space-8: 2rem;        /* 32px */
  --space-12: 3rem;       /* 48px */
  --space-16: 4rem;       /* 64px */

  /* Radios de borde */
  --radius-sm: 0.25rem;   /* 4px */
  --radius: 0.5rem;       /* 8px */
  --radius-lg: 0.75rem;   /* 12px */
  --radius-xl: 1rem;      /* 16px */

  /* Sombras */
  --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
  --shadow: 0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1);
  --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
  --shadow-xl: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);

  /* Variables para personalización de la interfaz */
  --background-contrast: 80%;
  --background-tint: 0;
  --background-tint-color: rgba(0, 0, 0, 0);
  --glassmorphism-opacity: 0.6;
  --glassmorphism-color: 26, 34, 54;
  --text-contrast: 90%;
}

/* Tema Claro */
:root[data-theme="light"],
:root.light {
  color-scheme: light;

  /* Colores principales */
  --primary: #3B82F6;
  --primary-dark: #1E40AF;
  --secondary: #10B981;
  --accent: #F59E0B;

  /* Fondos */
  --bg-primary: #FFFFFF;
  --bg-secondary: #F8FAFC;
  --bg-card: #FFFFFF;
  --bg-elegant: linear-gradient(135deg, #F8FAFC 0%, #F1F5F9 100%);

  /* Textos */
  --text-primary: #1F2937;
  --text-secondary: #6B7280;
  --text-muted: #9CA3AF;
  --text-color: var(--text-primary);

  /* Bordes */
  --border: #E5E7EB;
  --border-light: #F3F4F6;

  /* Glassmorphism */
  --glass-bg: rgba(255, 255, 255, 0.25);
  --glass-border: rgba(255, 255, 255, 0.18);
  --glass-backdrop: blur(10px);
  --glass-shadow: 0 8px 32px 0 rgba(31, 38, 135, 0.37);

  /* Estados */
  --success: #10B981;
  --warning: #F59E0B;
  --error: #EF4444;
  --info: #3B82F6;
}

/* Tema Oscuro */
:root[data-theme="dark"],
:root.dark {
  color-scheme: dark;

  /* Colores principales */
  --primary: #60A5FA;
  --primary-dark: #3B82F6;
  --secondary: #34D399;
  --accent: #FBBF24;

  /* Fondos */
  --bg-primary: #0F172A;
  --bg-secondary: #1E293B;
  --bg-card: #334155;
  --bg-elegant: linear-gradient(135deg, #1E293B 0%, #334155 100%);

  /* Textos */
  --text-primary: #F8FAFC;
  --text-secondary: #CBD5E1;
  --text-muted: #94A3B8;
  --text-color: var(--text-primary);

  /* Bordes */
  --border: #475569;
  --border-light: #334155;

  /* Glassmorphism */
  --glass-bg: rgba(15, 23, 42, 0.25);
  --glass-border: rgba(255, 255, 255, 0.18);
  --glass-backdrop: blur(10px);
  --glass-shadow: 0 8px 32px 0 rgba(0, 0, 0, 0.37);

  /* Estados */
  --success: #34D399;
  --warning: #FBBF24;
  --error: #F87171;
  --info: #60A5FA;
}

body {
  margin: 0;
  display: flex;
  min-width: 320px;
  min-height: 100vh;
  position: relative;
  color: var(--text-color);
  background-color: var(--bg-primary);
  transition: background-color 0.3s ease, color 0.3s ease;

  /* Aplicar el fondo directamente al body */
  background-image: url('/assets/background.jpg');
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
  background-attachment: fixed;

  /* Aplicar filtros */
  filter: contrast(var(--background-contrast));
}

/* Capa de tinte (blanco o negro según configuración) */
.background-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: var(--background-tint-color);
  z-index: 0;
  pointer-events: none;
}

#root {
  width: 100%;
}

/* Glassmorphism - Solo para header, sidebar, footer */
.glassmorphism {
  background: var(--glass-bg);
  backdrop-filter: var(--glass-backdrop);
  -webkit-backdrop-filter: var(--glass-backdrop);
  border-radius: var(--radius-lg);
  border: 1px solid var(--glass-border);
  box-shadow: var(--glass-shadow);
  color: var(--text-color);
  transition: all 0.3s ease;
}

/* Fondo elegante para contenido */
.bg-elegant {
  background: var(--bg-elegant);
  border-radius: var(--radius-lg);
  border: 1px solid var(--border-light);
  box-shadow: var(--shadow);
  transition: all 0.3s ease;
}

/* Fondo de card */
.bg-card {
  background: var(--bg-card);
  border-radius: var(--radius);
  border: 1px solid var(--border);
  box-shadow: var(--shadow-sm);
  transition: all 0.3s ease;
}

/* Clases de utilidad para temas */
.text-primary { color: var(--text-primary); }
.text-secondary { color: var(--text-secondary); }
.text-muted { color: var(--text-muted); }
.bg-primary { background-color: var(--bg-primary); }
.bg-secondary { background-color: var(--bg-secondary); }
.border-color { border-color: var(--border); }

/* Estados */
.text-success { color: var(--success); }
.text-warning { color: var(--warning); }
.text-error { color: var(--error); }
.text-info { color: var(--info); }
.bg-success { background-color: var(--success); }
.bg-warning { background-color: var(--warning); }
.bg-error { background-color: var(--error); }
.bg-info { background-color: var(--info); }

.tooltip-container {
  max-height: 300px;
  overflow-y: auto;
  transition: all 0.2s ease-in-out;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.5);
  pointer-events: none;
}

.tooltip-above {
  transform-origin: bottom left;
  animation: fadeInUp 0.2s ease-out forwards;
}

.tooltip-below {
  transform-origin: top left;
  animation: fadeInDown 0.2s ease-out forwards;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeInDown {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Estilos para la barra de desplazamiento personalizada */
.custom-scrollbar::-webkit-scrollbar {
  width: 8px;
}

.custom-scrollbar::-webkit-scrollbar-track {
  background: rgba(var(--glassmorphism-color), 0.3);
  border-radius: 10px;
}

.custom-scrollbar::-webkit-scrollbar-thumb {
  background: rgba(255, 255, 255, 0.3);
  border-radius: 10px;
}

.custom-scrollbar::-webkit-scrollbar-thumb:hover {
  background: rgba(255, 255, 255, 0.5);
}

/* Configuraciones de accesibilidad */
.high-contrast {
  --text-primary: #000000;
  --text-secondary: #333333;
  --bg-primary: #FFFFFF;
  --bg-secondary: #F0F0F0;
  --border: #000000;
  --primary: #0000FF;
  --secondary: #008000;
  --error: #FF0000;
  --warning: #FFA500;
}

.high-contrast.dark {
  --text-primary: #FFFFFF;
  --text-secondary: #CCCCCC;
  --bg-primary: #000000;
  --bg-secondary: #1A1A1A;
  --border: #FFFFFF;
  --primary: #00BFFF;
  --secondary: #00FF00;
  --error: #FF6B6B;
  --warning: #FFD700;
}

.reduced-motion,
.reduced-motion * {
  animation-duration: 0.01ms !important;
  animation-iteration-count: 1 !important;
  transition-duration: 0.01ms !important;
  scroll-behavior: auto !important;
}

/* Transiciones suaves para cambios de tema */
* {
  transition: background-color 0.3s ease,
              color 0.3s ease,
              border-color 0.3s ease,
              box-shadow 0.3s ease;
}

.reduced-motion * {
  transition: none !important;
}

/* Utilidades de responsive design */
@media (max-width: 640px) {
  .glassmorphism {
    backdrop-filter: blur(5px);
    -webkit-backdrop-filter: blur(5px);
  }

  .bg-elegant {
    background: var(--bg-card);
  }
}

/* Focus states mejorados para accesibilidad */
*:focus {
  outline: 2px solid var(--primary);
  outline-offset: 2px;
}

*:focus:not(:focus-visible) {
  outline: none;
}

*:focus-visible {
  outline: 2px solid var(--primary);
  outline-offset: 2px;
}

/* Mejoras para lectores de pantalla */
.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}

/* Indicadores de carga */
.loading {
  position: relative;
  overflow: hidden;
}

.loading::after {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(255, 255, 255, 0.2),
    transparent
  );
  animation: loading 1.5s infinite;
}

@keyframes loading {
  0% { left: -100%; }
  100% { left: 100%; }
}

.reduced-motion .loading::after {
  animation: none;
  opacity: 0.5;
}

/* Estilos para inputs de rango */
input[type="range"] {
  -webkit-appearance: none;
  appearance: none;
  background: transparent;
  cursor: pointer;
  outline: none;
}

/* Estilos específicos para sliders del UICustomizer */
.slider {
  -webkit-appearance: none;
  appearance: none;
  height: 8px;
  border-radius: 4px;
  outline: none;
  cursor: pointer;
  transition: all 0.2s ease;
}

input[type="range"]::-webkit-slider-track {
  background: var(--border);
  height: 4px;
  border-radius: 2px;
}

input[type="range"]::-webkit-slider-thumb {
  -webkit-appearance: none;
  appearance: none;
  background: var(--primary);
  height: 16px;
  width: 16px;
  border-radius: 50%;
  border: 2px solid var(--bg-card);
  box-shadow: var(--shadow);
  cursor: pointer;
  transition: all 0.2s ease;
}

.slider::-webkit-slider-thumb {
  -webkit-appearance: none;
  appearance: none;
  background: var(--primary);
  height: 20px;
  width: 20px;
  border-radius: 50%;
  border: 3px solid white;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.2);
  cursor: pointer;
  transition: all 0.2s ease;
}

input[type="range"]::-webkit-slider-thumb:hover {
  background: var(--primary-dark);
  transform: scale(1.1);
}

.slider::-webkit-slider-thumb:hover {
  background: var(--primary-dark);
  transform: scale(1.1);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
}

input[type="range"]::-moz-range-track {
  background: var(--border);
  height: 4px;
  border-radius: 2px;
  border: none;
}

input[type="range"]::-moz-range-thumb {
  background: var(--primary);
  height: 16px;
  width: 16px;
  border-radius: 50%;
  border: 2px solid var(--bg-card);
  box-shadow: var(--shadow);
  cursor: pointer;
}

/* Estilos para checkboxes */
input[type="checkbox"] {
  accent-color: var(--primary);
}

/* Estilos para radio buttons */
input[type="radio"] {
  accent-color: var(--primary);
}

/* Estilos para select */
select {
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
  background-position: right 0.5rem center;
  background-repeat: no-repeat;
  background-size: 1.5em 1.5em;
  padding-right: 2.5rem;
  -webkit-appearance: none;
  -moz-appearance: none;
  appearance: none;
}

select option {
  background-color: var(--bg-card);
  color: var(--text-primary);
  padding: 0.5rem;
}

/* Estilos para placeholders */
::placeholder {
  color: var(--text-muted);
  opacity: 0.7;
}

/* Estilos para textarea */
textarea {
  resize: vertical;
  min-height: 80px;
}

/* Asegurar que los inputs sean visibles */
input, select, textarea {
  color: var(--text-primary) !important;
  background-color: var(--bg-secondary) !important;
}

input:focus, select:focus, textarea:focus {
  outline: none;
  box-shadow: 0 0 0 2px var(--primary);
  border-color: var(--primary);
}

/* Animación para el botón de personalización */
@keyframes pulse-subtle {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.7; }
}

.ui-customizer-hint {
  animation: pulse-subtle 2s infinite;
}

.reduced-motion .ui-customizer-hint {
  animation: none;
}
