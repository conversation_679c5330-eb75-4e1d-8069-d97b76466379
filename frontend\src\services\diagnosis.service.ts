import axios from 'axios';

// Tipos para diagnósticos
export interface Diagnosis {
  id: string;
  code: string;
  title: string;
  description?: string;
  source: 'CIE-10' | 'CIE-11';
  language: string;
  category?: string;
  parent?: string;
  children?: string[];
  mappings?: {
    cie10?: string;
    cie11?: string;
  };
}

export interface DiagnosisSearchParams {
  query: string;
  version?: 'CIE-10' | 'CIE-11' | 'BOTH';
  language?: 'es' | 'en';
  limit?: number;
  category?: string;
}

export interface DiagnosisSearchResponse {
  results: Diagnosis[];
  total: number;
  hasMore: boolean;
  suggestions?: string[];
}

export interface TransitionMapping {
  cie10Code: string;
  cie10Title: string;
  cie11Code?: string;
  cie11Title?: string;
  mappingType: 'exact' | 'approximate' | 'multiple' | 'none';
  confidence: number;
  notes?: string;
}

class DiagnosisService {
  private baseURL = 'https://id.who.int/icd';
  private cache = new Map<string, any>();
  private cacheTimeout = 5 * 60 * 1000; // 5 minutos

  // Cliente HTTP configurado para la API de la OMS
  private apiClient = axios.create({
    baseURL: this.baseURL,
    timeout: 10000,
    headers: {
      'Accept': 'application/json',
      'Content-Type': 'application/json',
      'API-Version': 'v2',
      'Accept-Language': 'es'
    }
  });

  constructor() {
    // Interceptor para manejo de errores
    this.apiClient.interceptors.response.use(
      (response) => response,
      (error) => {
        console.error('Error en API de diagnósticos:', error);
        return Promise.reject(this.handleError(error));
      }
    );
  }

  private handleError(error: any) {
    if (error.response) {
      switch (error.response.status) {
        case 429:
          return new Error('Demasiadas solicitudes. Intente más tarde.');
        case 404:
          return new Error('Diagnóstico no encontrado.');
        case 500:
          return new Error('Error del servidor. Intente más tarde.');
        default:
          return new Error('Error al buscar diagnósticos.');
      }
    }
    return new Error('Error de conexión.');
  }

  private getCacheKey(params: any): string {
    return JSON.stringify(params);
  }

  private getFromCache<T>(key: string): T | null {
    const cached = this.cache.get(key);
    if (cached && Date.now() - cached.timestamp < this.cacheTimeout) {
      return cached.data;
    }
    return null;
  }

  private setCache<T>(key: string, data: T): void {
    this.cache.set(key, {
      data,
      timestamp: Date.now()
    });
  }

  /**
   * Buscar diagnósticos en CIE-10 y/o CIE-11
   */
  async search(params: DiagnosisSearchParams): Promise<DiagnosisSearchResponse> {
    const cacheKey = this.getCacheKey(params);
    const cached = this.getFromCache<DiagnosisSearchResponse>(cacheKey);
    
    if (cached) {
      return cached;
    }

    try {
      const results: Diagnosis[] = [];
      
      // Buscar en CIE-10 si se solicita
      if (params.version === 'CIE-10' || params.version === 'BOTH') {
        const cie10Results = await this.searchCIE10(params);
        results.push(...cie10Results);
      }

      // Buscar en CIE-11 si se solicita
      if (params.version === 'CIE-11' || params.version === 'BOTH') {
        const cie11Results = await this.searchCIE11(params);
        results.push(...cie11Results);
      }

      // Ordenar por relevancia
      const sortedResults = this.sortByRelevance(results, params.query);
      const limitedResults = params.limit ? sortedResults.slice(0, params.limit) : sortedResults;

      const response: DiagnosisSearchResponse = {
        results: limitedResults,
        total: sortedResults.length,
        hasMore: params.limit ? sortedResults.length > params.limit : false,
        suggestions: this.generateSuggestions(params.query, sortedResults)
      };

      this.setCache(cacheKey, response);
      return response;
    } catch (error) {
      console.error('Error en búsqueda de diagnósticos:', error);
      throw error;
    }
  }

  /**
   * Buscar específicamente en CIE-10
   */
  private async searchCIE10(params: DiagnosisSearchParams): Promise<Diagnosis[]> {
    // Simulación de búsqueda en CIE-10 (reemplazar con API real)
    const mockCIE10Results: Diagnosis[] = [
      {
        id: 'cie10-A00',
        code: 'A00',
        title: 'Cólera',
        description: 'Infección intestinal aguda causada por Vibrio cholerae',
        source: 'CIE-10',
        language: params.language || 'es',
        category: 'Enfermedades infecciosas'
      },
      {
        id: 'cie10-A01',
        code: 'A01',
        title: 'Fiebres tifoidea y paratifoidea',
        description: 'Infecciones sistémicas causadas por Salmonella',
        source: 'CIE-10',
        language: params.language || 'es',
        category: 'Enfermedades infecciosas'
      }
    ];

    return mockCIE10Results.filter(diagnosis =>
      diagnosis.title.toLowerCase().includes(params.query.toLowerCase()) ||
      diagnosis.code.toLowerCase().includes(params.query.toLowerCase())
    );
  }

  /**
   * Buscar específicamente en CIE-11
   */
  private async searchCIE11(params: DiagnosisSearchParams): Promise<Diagnosis[]> {
    try {
      // Intentar usar la API real de la OMS para CIE-11
      const response = await this.apiClient.get('/entity/search', {
        params: {
          q: params.query,
          subtreeFilterUsesFoundationDescendants: false,
          includeKeywordResult: true,
          useFlexisearch: false,
          flatResults: true,
          highlightingEnabled: false
        }
      });

      return response.data.destinationEntities?.map((entity: any) => ({
        id: entity.id,
        code: entity.code || entity.theCode,
        title: entity.title?.['@value'] || entity.title,
        description: entity.definition?.['@value'],
        source: 'CIE-11' as const,
        language: params.language || 'es',
        category: entity.parent?.title?.['@value']
      })) || [];
    } catch (error) {
      console.warn('Error al acceder a API CIE-11, usando datos mock:', error);
      
      // Fallback a datos mock
      const mockCIE11Results: Diagnosis[] = [
        {
          id: 'cie11-1A00',
          code: '1A00',
          title: 'Cólera',
          description: 'Infección intestinal aguda causada por Vibrio cholerae',
          source: 'CIE-11',
          language: params.language || 'es',
          category: 'Enfermedades infecciosas y parasitarias'
        }
      ];

      return mockCIE11Results.filter(diagnosis =>
        diagnosis.title.toLowerCase().includes(params.query.toLowerCase()) ||
        diagnosis.code.toLowerCase().includes(params.query.toLowerCase())
      );
    }
  }

  /**
   * Obtener diagnóstico por ID
   */
  async getById(id: string, version: 'CIE-10' | 'CIE-11' = 'CIE-11'): Promise<Diagnosis | null> {
    const cacheKey = `diagnosis-${id}-${version}`;
    const cached = this.getFromCache<Diagnosis>(cacheKey);
    
    if (cached) {
      return cached;
    }

    try {
      if (version === 'CIE-11') {
        const response = await this.apiClient.get(`/entity/${id}`);
        const entity = response.data;
        
        const diagnosis: Diagnosis = {
          id: entity.id,
          code: entity.code || entity.theCode,
          title: entity.title?.['@value'] || entity.title,
          description: entity.definition?.['@value'],
          source: 'CIE-11',
          language: 'es',
          category: entity.parent?.title?.['@value'],
          children: entity.child?.map((child: any) => child.id)
        };

        this.setCache(cacheKey, diagnosis);
        return diagnosis;
      }
      
      // Para CIE-10, usar datos mock o API específica
      return null;
    } catch (error) {
      console.error('Error al obtener diagnóstico:', error);
      return null;
    }
  }

  /**
   * Mapear código CIE-10 a CIE-11
   */
  async mapCie10ToCie11(cie10Code: string): Promise<TransitionMapping | null> {
    const cacheKey = `mapping-${cie10Code}`;
    const cached = this.getFromCache<TransitionMapping>(cacheKey);
    
    if (cached) {
      return cached;
    }

    try {
      // Simulación de mapeo (reemplazar con API real de mapeo)
      const mockMapping: TransitionMapping = {
        cie10Code,
        cie10Title: 'Diagnóstico CIE-10',
        cie11Code: '1A00',
        cie11Title: 'Diagnóstico equivalente CIE-11',
        mappingType: 'exact',
        confidence: 0.95,
        notes: 'Mapeo directo disponible'
      };

      this.setCache(cacheKey, mockMapping);
      return mockMapping;
    } catch (error) {
      console.error('Error en mapeo CIE-10 a CIE-11:', error);
      return null;
    }
  }

  /**
   * Obtener reporte de transición CIE-10 a CIE-11
   */
  async getTransitionReport(): Promise<{
    totalMappings: number;
    exactMappings: number;
    approximateMappings: number;
    noMappings: number;
    recommendations: string[];
  }> {
    // Simulación de reporte de transición
    return {
      totalMappings: 1000,
      exactMappings: 750,
      approximateMappings: 200,
      noMappings: 50,
      recommendations: [
        'Revisar diagnósticos sin mapeo directo',
        'Capacitar personal en nuevos códigos CIE-11',
        'Actualizar sistemas de información'
      ]
    };
  }

  /**
   * Ordenar resultados por relevancia
   */
  private sortByRelevance(results: Diagnosis[], query: string): Diagnosis[] {
    return results.sort((a, b) => {
      const aScore = this.calculateRelevanceScore(a, query);
      const bScore = this.calculateRelevanceScore(b, query);
      return bScore - aScore;
    });
  }

  /**
   * Calcular puntuación de relevancia
   */
  private calculateRelevanceScore(diagnosis: Diagnosis, query: string): number {
    const queryLower = query.toLowerCase();
    let score = 0;

    // Coincidencia exacta en código
    if (diagnosis.code.toLowerCase() === queryLower) score += 100;
    
    // Código comienza con la consulta
    if (diagnosis.code.toLowerCase().startsWith(queryLower)) score += 50;
    
    // Título comienza con la consulta
    if (diagnosis.title.toLowerCase().startsWith(queryLower)) score += 40;
    
    // Contiene la consulta en el título
    if (diagnosis.title.toLowerCase().includes(queryLower)) score += 20;
    
    // Contiene la consulta en la descripción
    if (diagnosis.description?.toLowerCase().includes(queryLower)) score += 10;

    return score;
  }

  /**
   * Generar sugerencias de búsqueda
   */
  private generateSuggestions(query: string, results: Diagnosis[]): string[] {
    const suggestions = new Set<string>();
    
    results.forEach(diagnosis => {
      // Agregar palabras del título como sugerencias
      const words = diagnosis.title.split(' ').filter(word => 
        word.length > 3 && !word.toLowerCase().includes(query.toLowerCase())
      );
      words.slice(0, 2).forEach(word => suggestions.add(word));
    });

    return Array.from(suggestions).slice(0, 5);
  }

  /**
   * Limpiar caché
   */
  clearCache(): void {
    this.cache.clear();
  }
}

export const diagnosisService = new DiagnosisService();
export default diagnosisService;
