import { useState } from 'react';
import { DiagnosticoCIE11, DiagnosticoPersonalizado } from '../../../services/cie11Service';
import { BuscadorCIE11 } from './BuscadorCIE11';
import { DetalleDiagnosticoCIE11 } from './DetalleDiagnosticoCIE11';
import { DiagnosticosPersonalizados } from './DiagnosticosPersonalizados';
// Importar nuevos componentes de diagnósticos
import { DiagnosticSearch } from '../../../components/diagnosticos/DiagnosticSearch';
import { TransitionHelper } from '../../../components/diagnosticos/TransitionHelper';
import { Diagnosis } from '../../../types/diagnosis.types';

interface DiagnosticoSelectorProps {
  onSelectDiagnostico: (codigo: string, descripcion?: string) => void;
  especialidadId?: number;
  initialCodigo?: string;
  initialDescripcion?: string;
}

/**
 * Componente selector de diagnósticos que integra búsqueda CIE-11 y diagnósticos personalizados
 */
export const DiagnosticoSelector = ({
  onSelectDiagnostico,
  especialidadId,
  initialCodigo = '',
  initialDescripcion = ''
}: DiagnosticoSelectorProps) => {
  const [mostrarDetalle, setMostrarDetalle] = useState(false);
  const [mostrarPersonalizados, setMostrarPersonalizados] = useState(false);
  const [codigoSeleccionado, setCodigoSeleccionado] = useState(initialCodigo);
  const [descripcionSeleccionada, setDescripcionSeleccionada] = useState(initialDescripcion);
  const [diagnosticoDetalle, setDiagnosticoDetalle] = useState<string>('');

  // Manejar selección de diagnóstico CIE-11
  const handleSelectDiagnostico = (diagnostico: DiagnosticoCIE11) => {
    setCodigoSeleccionado(diagnostico.codigo);
    setDescripcionSeleccionada(diagnostico.titulo);
    onSelectDiagnostico(diagnostico.codigo);
  };

  // Manejar selección de diagnóstico personalizado
  const handleSelectPersonalizado = (diagnostico: DiagnosticoPersonalizado) => {
    const codigo = diagnostico.codigo_personalizado || diagnostico.codigo_original;
    const descripcion = diagnostico.descripcion_personalizada || diagnostico.titulo;
    
    setCodigoSeleccionado(codigo);
    setDescripcionSeleccionada(descripcion);
    onSelectDiagnostico(codigo);
    setMostrarPersonalizados(false);
  };

  // Mostrar detalle de diagnóstico
  const verDetalle = (codigo: string) => {
    setDiagnosticoDetalle(codigo);
    setMostrarDetalle(true);
  };

  return (
    <div className="space-y-4">
      {/* Buscador principal */}
      <div className="space-y-2">
        <div className="flex justify-between items-center">
          <label className="block text-sm font-medium text-gray-700">Diagnóstico (CIE-11) *</label>
          
          <div className="flex space-x-2">
            <button
              type="button"
              onClick={() => setMostrarPersonalizados(true)}
              className="text-sm text-blue-600 hover:text-blue-800"
            >
              Diagnósticos personalizados
            </button>
          </div>
        </div>
        
        <BuscadorCIE11 
          onSelectDiagnostico={handleSelectDiagnostico}
          placeholder="Buscar diagnóstico por código o descripción..."
          mostrarRecientes={true}
        />
      </div>
      
      {/* Diagnóstico seleccionado */}
      {codigoSeleccionado && (
        <div className="p-3 bg-blue-50 rounded-md">
          <div className="flex justify-between items-center">
            <div>
              <div className="flex items-center">
                <span className="font-medium mr-2">{codigoSeleccionado}</span>
                <span className="text-sm">{descripcionSeleccionada}</span>
              </div>
            </div>
            
            <div className="space-x-2">
              <button
                type="button"
                onClick={() => verDetalle(codigoSeleccionado)}
                className="text-xs px-2 py-1 bg-blue-100 hover:bg-blue-200 text-blue-800 rounded"
              >
                Ver detalles
              </button>
              
              <button
                type="button"
                onClick={() => {
                  setCodigoSeleccionado('');
                  setDescripcionSeleccionada('');
                  onSelectDiagnostico('');
                }}
                className="text-xs px-2 py-1 bg-red-100 hover:bg-red-200 text-red-800 rounded"
              >
                Quitar
              </button>
            </div>
          </div>
        </div>
      )}
      
      {/* Modal de detalle de diagnóstico */}
      {mostrarDetalle && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex justify-center items-center z-50 p-4">
          <div className="max-w-3xl w-full">
            <DetalleDiagnosticoCIE11
              codigo={diagnosticoDetalle}
              onClose={() => setMostrarDetalle(false)}
              onSelectRelacionado={(diagnostico) => {
                handleSelectDiagnostico(diagnostico);
                setMostrarDetalle(false);
              }}
            />
          </div>
        </div>
      )}
      
      {/* Modal de diagnósticos personalizados */}
      {mostrarPersonalizados && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex justify-center items-center z-50 p-4">
          <div className="max-w-4xl w-full">
            <div className="bg-white rounded-lg shadow-xl overflow-hidden">
              <div className="p-4 bg-gray-50 border-b flex justify-between items-center">
                <h3 className="text-lg font-semibold text-gray-800">Diagnósticos Personalizados</h3>
                <button
                  onClick={() => setMostrarPersonalizados(false)}
                  className="text-gray-500 hover:text-gray-700 text-xl"
                >
                  &times;
                </button>
              </div>
              
              <div className="p-6 max-h-[80vh] overflow-y-auto">
                <DiagnosticosPersonalizados
                  onSelectDiagnostico={handleSelectPersonalizado}
                  especialidadId={especialidadId}
                />
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};
