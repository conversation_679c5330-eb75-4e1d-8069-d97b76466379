"use client";import{useFocusRing as me}from"@react-aria/focus";import{useHover as Te}from"@react-aria/interactions";import h,{Fragment as Y,useCallback as fe,useEffect as ce,useMemo as B,useRef as V,useState as ye}from"react";import{flushSync as X}from"react-dom";import{useActivePress as Ee}from'../../hooks/use-active-press.js';import{useDidElementMove as Pe}from'../../hooks/use-did-element-move.js';import{useDisposables as ge}from'../../hooks/use-disposables.js';import{useElementSize as Me}from'../../hooks/use-element-size.js';import{useEvent as y}from'../../hooks/use-event.js';import{useId as w}from'../../hooks/use-id.js';import{useInertOthers as be}from'../../hooks/use-inert-others.js';import{useIsoMorphicEffect as k}from'../../hooks/use-iso-morphic-effect.js';import{useOnDisappear as Ae}from'../../hooks/use-on-disappear.js';import{useOutsideClick as _e}from'../../hooks/use-outside-click.js';import{useOwnerDocument as Z}from'../../hooks/use-owner.js';import{useResolveButtonType as Ie}from'../../hooks/use-resolve-button-type.js';import{useScrollLock as Se}from'../../hooks/use-scroll-lock.js';import{useSyncRefs as K}from'../../hooks/use-sync-refs.js';import{useTextValue as De}from'../../hooks/use-text-value.js';import{useTrackedPointer as Re}from'../../hooks/use-tracked-pointer.js';import{transitionDataAttributes as Fe,useTransition as xe}from'../../hooks/use-transition.js';import{useTreeWalker as he}from'../../hooks/use-tree-walker.js';import{FloatingProvider as Ce,useFloatingPanel as ve,useFloatingPanelProps as Le,useFloatingReference as Oe,useFloatingReferenceProps as He,useResolvedAnchor as Ue}from'../../internal/floating.js';import{OpenClosedProvider as Ge,State as W,useOpenClosed as Ne}from'../../internal/open-closed.js';import{useSlice as D}from'../../react-glue.js';import{isDisabledReactIssue7711 as Be}from'../../utils/bugs.js';import{Focus as b}from'../../utils/calculate-active-index.js';import{disposables as we}from'../../utils/disposables.js';import{Focus as ee,FocusableMode as ke,focusFrom as Ke,isFocusableElement as We,restoreFocusIfNecessary as te}from'../../utils/focus-management.js';import{match as Je}from'../../utils/match.js';import{RenderFeatures as oe,forwardRefWithAs as C,mergeProps as ne,useRender as v}from'../../utils/render.js';import{useDescriptions as Ve}from'../description/description.js';import{Keys as d}from'../keyboard.js';import{useLabelContext as Xe,useLabels as re}from'../label/label.js';import{Portal as $e}from'../portal/portal.js';import{ActionTypes as r,ActivationTrigger as $,MenuState as m}from'./menu-machine.js';import{MenuContext as je,useMenuMachine as qe,useMenuMachineContext as j}from'./menu-machine-glue.js';let ze=Y;function Qe(T,E){let{__demoMode:i=!1,...a}=T,n=qe({__demoMode:i}),[s,o,P]=D(n,p=>[p.menuState,p.itemsElement,p.buttonElement]),c=K(E),_=s===m.Open;_e(_,[P,o],(p,F)=>{var A;n.send({type:r.CloseMenu}),We(F,ke.Loose)||(p.preventDefault(),(A=n.state.buttonElement)==null||A.focus())});let t=y(()=>{n.send({type:r.CloseMenu})}),R=B(()=>({open:s===m.Open,close:t}),[s,t]),I={ref:c},g=v();return h.createElement(Ce,null,h.createElement(je.Provider,{value:n},h.createElement(Ge,{value:Je(s,{[m.Open]:W.Open,[m.Closed]:W.Closed})},g({ourProps:I,theirProps:a,slot:R,defaultTag:ze,name:"Menu"}))))}let Ye="button";function Ze(T,E){let i=j("Menu.Button"),a=w(),{id:n=`headlessui-menu-button-${a}`,disabled:s=!1,autoFocus:o=!1,...P}=T,c=V(null),_=He(),t=K(E,c,Oe(),y(l=>i.send({type:r.SetButtonElement,element:l}))),R=y(l=>{switch(l.key){case d.Space:case d.Enter:case d.ArrowDown:l.preventDefault(),l.stopPropagation(),i.send({type:r.OpenMenu,focus:{focus:b.First}});break;case d.ArrowUp:l.preventDefault(),l.stopPropagation(),i.send({type:r.OpenMenu,focus:{focus:b.Last}});break}}),I=y(l=>{switch(l.key){case d.Space:l.preventDefault();break}}),[g,p]=D(i,l=>[l.menuState,l.itemsElement]),F=y(l=>{var H;if(l.button===0){if(Be(l.currentTarget))return l.preventDefault();s||(g===m.Open?(X(()=>i.send({type:r.CloseMenu})),(H=c.current)==null||H.focus({preventScroll:!0})):(l.preventDefault(),i.send({type:r.OpenMenu,focus:{focus:b.Nothing},trigger:$.Pointer})))}}),{isFocusVisible:A,focusProps:f}=me({autoFocus:o}),{isHovered:M,hoverProps:L}=Te({isDisabled:s}),{pressed:S,pressProps:O}=Ee({disabled:s}),x=B(()=>({open:g===m.Open,active:S||g===m.Open,disabled:s,hover:M,focus:A,autofocus:o}),[g,M,A,S,s,o]),U=ne(_(),{ref:t,id:n,type:Ie(T,c.current),"aria-haspopup":"menu","aria-controls":p==null?void 0:p.id,"aria-expanded":g===m.Open,disabled:s||void 0,autoFocus:o,onKeyDown:R,onKeyUp:I,onMouseDown:F},f,L,O);return v()({ourProps:U,theirProps:P,slot:x,defaultTag:Ye,name:"Menu.Button"})}let et="div",tt=oe.RenderStrategy|oe.Static;function ot(T,E){let i=w(),{id:a=`headlessui-menu-items-${i}`,anchor:n,portal:s=!1,modal:o=!0,transition:P=!1,...c}=T,_=Ue(n),t=j("Menu.Items"),[R,I]=ve(_),g=Le(),[p,F]=ye(null),A=K(E,_?R:null,y(e=>t.send({type:r.SetItemsElement,element:e})),F),[f,M]=D(t,e=>[e.menuState,e.buttonElement]),L=Z(M),S=Z(p);_&&(s=!0);let O=Ne(),[x,U]=xe(P,p,O!==null?(O&W.Open)===W.Open:f===m.Open);Ae(x,M,()=>{t.send({type:r.CloseMenu})});let G=D(t,e=>e.__demoMode),l=G?!1:o&&f===m.Open;Se(l,S);let H=G?!1:o&&f===m.Open;be(H,{allowed:fe(()=>[M,p],[M,p])});let u=f!==m.Open,ae=Pe(u,M)?!1:x;ce(()=>{let e=p;e&&f===m.Open&&e!==(S==null?void 0:S.activeElement)&&e.focus({preventScroll:!0})},[f,p,S]),he(f===m.Open,{container:p,accept(e){return e.getAttribute("role")==="menuitem"?NodeFilter.FILTER_REJECT:e.hasAttribute("role")?NodeFilter.FILTER_SKIP:NodeFilter.FILTER_ACCEPT},walk(e){e.setAttribute("role","none")}});let q=ge(),se=y(e=>{var N,z,Q;switch(q.dispose(),e.key){case d.Space:if(t.state.searchQuery!=="")return e.preventDefault(),e.stopPropagation(),t.send({type:r.Search,value:e.key});case d.Enter:if(e.preventDefault(),e.stopPropagation(),t.state.activeItemIndex!==null){let{dataRef:de}=t.state.items[t.state.activeItemIndex];(z=(N=de.current)==null?void 0:N.domRef.current)==null||z.click()}t.send({type:r.CloseMenu}),te(t.state.buttonElement);break;case d.ArrowDown:return e.preventDefault(),e.stopPropagation(),t.send({type:r.GoToItem,focus:b.Next});case d.ArrowUp:return e.preventDefault(),e.stopPropagation(),t.send({type:r.GoToItem,focus:b.Previous});case d.Home:case d.PageUp:return e.preventDefault(),e.stopPropagation(),t.send({type:r.GoToItem,focus:b.First});case d.End:case d.PageDown:return e.preventDefault(),e.stopPropagation(),t.send({type:r.GoToItem,focus:b.Last});case d.Escape:e.preventDefault(),e.stopPropagation(),X(()=>t.send({type:r.CloseMenu})),(Q=t.state.buttonElement)==null||Q.focus({preventScroll:!0});break;case d.Tab:e.preventDefault(),e.stopPropagation(),X(()=>t.send({type:r.CloseMenu})),Ke(t.state.buttonElement,e.shiftKey?ee.Previous:ee.Next);break;default:e.key.length===1&&(t.send({type:r.Search,value:e.key}),q.setTimeout(()=>t.send({type:r.ClearSearch}),350));break}}),le=y(e=>{switch(e.key){case d.Space:e.preventDefault();break}}),pe=B(()=>({open:f===m.Open}),[f]),ie=ne(_?g():{},{"aria-activedescendant":D(t,t.selectors.activeDescendantId),"aria-labelledby":D(t,e=>{var N;return(N=e.buttonElement)==null?void 0:N.id}),id:a,onKeyDown:se,onKeyUp:le,role:"menu",tabIndex:f===m.Open?0:void 0,ref:A,style:{...c.style,...I,"--button-width":Me(M,!0).width},...Fe(U)}),ue=v();return h.createElement($e,{enabled:s?T.static||x:!1,ownerDocument:L},ue({ourProps:ie,theirProps:c,slot:pe,defaultTag:et,features:tt,visible:ae,name:"Menu.Items"}))}let nt=Y;function rt(T,E){let i=w(),{id:a=`headlessui-menu-item-${i}`,disabled:n=!1,...s}=T,o=j("Menu.Item"),P=D(o,u=>o.selectors.isActive(u,a)),c=V(null),_=K(E,c),t=D(o,u=>o.selectors.shouldScrollIntoView(u,a));k(()=>{if(t)return we().requestAnimationFrame(()=>{var u,J;(J=(u=c.current)==null?void 0:u.scrollIntoView)==null||J.call(u,{block:"nearest"})})},[t,c]);let R=De(c),I=V({disabled:n,domRef:c,get textValue(){return R()}});k(()=>{I.current.disabled=n},[I,n]),k(()=>(o.actions.registerItem(a,I),()=>o.actions.unregisterItem(a)),[I,a]);let g=y(()=>{o.send({type:r.CloseMenu})}),p=y(u=>{if(n)return u.preventDefault();o.send({type:r.CloseMenu}),te(o.state.buttonElement)}),F=y(()=>{if(n)return o.send({type:r.GoToItem,focus:b.Nothing});o.send({type:r.GoToItem,focus:b.Specific,id:a})}),A=Re(),f=y(u=>{A.update(u),!n&&(P||o.send({type:r.GoToItem,focus:b.Specific,id:a,trigger:$.Pointer}))}),M=y(u=>{A.wasMoved(u)&&(n||P||o.send({type:r.GoToItem,focus:b.Specific,id:a,trigger:$.Pointer}))}),L=y(u=>{A.wasMoved(u)&&(n||P&&o.send({type:r.GoToItem,focus:b.Nothing}))}),[S,O]=re(),[x,U]=Ve(),G=B(()=>({active:P,focus:P,disabled:n,close:g}),[P,n,g]),l={id:a,ref:_,role:"menuitem",tabIndex:n===!0?void 0:-1,"aria-disabled":n===!0?!0:void 0,"aria-labelledby":S,"aria-describedby":x,disabled:void 0,onClick:p,onFocus:F,onPointerEnter:f,onMouseEnter:f,onPointerMove:M,onMouseMove:M,onPointerLeave:L,onMouseLeave:L},H=v();return h.createElement(O,null,h.createElement(U,null,H({ourProps:l,theirProps:s,slot:G,defaultTag:nt,name:"Menu.Item"})))}let at="div";function st(T,E){let[i,a]=re(),n=T,s={ref:E,"aria-labelledby":i,role:"group"},o=v();return h.createElement(a,null,o({ourProps:s,theirProps:n,slot:{},defaultTag:at,name:"Menu.Section"}))}let lt="header";function pt(T,E){let i=w(),{id:a=`headlessui-menu-heading-${i}`,...n}=T,s=Xe();k(()=>s.register(a),[a,s.register]);let o={id:a,ref:E,role:"presentation",...s.props};return v()({ourProps:o,theirProps:n,slot:{},defaultTag:lt,name:"Menu.Heading"})}let it="div";function ut(T,E){let i=T,a={ref:E,role:"separator"};return v()({ourProps:a,theirProps:i,slot:{},defaultTag:it,name:"Menu.Separator"})}let dt=C(Qe),mt=C(Ze),Tt=C(ot),ft=C(rt),ct=C(st),yt=C(pt),Et=C(ut),to=Object.assign(dt,{Button:mt,Items:Tt,Item:ft,Section:ct,Heading:yt,Separator:Et});export{to as Menu,mt as MenuButton,yt as MenuHeading,ft as MenuItem,Tt as MenuItems,ct as MenuSection,Et as MenuSeparator};
