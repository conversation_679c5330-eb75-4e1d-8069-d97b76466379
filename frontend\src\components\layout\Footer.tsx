import { CSSProperties } from 'react';

export interface FooterProps {
  glassStyle?: CSSProperties;
}

export const Footer = ({ glassStyle }: FooterProps = {}) => {
  // Información del usuario (temporal)
  const user = { username: 'Usuario de prueba', hospital_id: '1' };
  const logout = () => console.log('Logout');

  return (
    <footer
      className="glass-footer py-3 px-6 flex-shrink-0"
      style={glassStyle}
    >
      <div className="flex flex-col sm:flex-row justify-between items-center">
        {/* Información del usuario */}
        <div className="flex items-center space-x-4 mb-2 sm:mb-0">
          <div className="flex items-center space-x-3">
            <div className="w-8 h-8 rounded-full bg-gradient-to-br from-blue-500 via-purple-600 to-indigo-700 flex items-center justify-center text-white font-bold shadow-lg">
              {user?.username.charAt(0).toUpperCase()}
            </div>
            <div className="hidden sm:block">
              <p className="text-adaptive font-semibold text-sm">{user?.username}</p>
              <p className="text-adaptive-subtle text-xs">Hospital ID: {user?.hospital_id}</p>
            </div>
          </div>
          <div className="flex items-center space-x-2">
            <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
            <span className="text-sm text-adaptive-muted">En línea</span>
          </div>
        </div>

        {/* Información del sistema y botón de logout */}
        <div className="flex items-center space-x-4">
          <div className="flex items-center space-x-3 text-xs text-adaptive-subtle">
            <span>&copy; {new Date().getFullYear()} Sistema Hipócrates</span>
            <span className="hidden sm:inline">v2.1.0</span>
            <div className="flex items-center space-x-1">
              <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
              <span>Estable</span>
            </div>
          </div>

          {/* Botón de cerrar sesión */}
          <button
            onClick={logout}
            className="glass-card flex items-center py-2 px-3 rounded-lg hover:shadow-lg transition-all duration-300 transform hover:scale-105 text-red-400 hover:text-red-300"
          >
            <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1"></path>
            </svg>
            <span className="text-sm font-medium hidden sm:inline">Cerrar sesión</span>
          </button>
        </div>
      </div>
    </footer>
  );
};
