import { ReactNode, useEffect, useState } from 'react';
import { Sidebar } from './Sidebar';
import { Header } from './Header';
import { Footer } from './Footer';
import { useSettingsStore } from '../../store/useSettingsStore';
import { useTheme } from '../../hooks/useTheme';

export interface LayoutProps {
  children: ReactNode;
}

export const Layout = ({ children }: LayoutProps) => {
  const [isSidebarOpen, setIsSidebarOpen] = useState(false);

  const {
    glassmorphismOpacity,
    glassmorphismColor,
    textContrast,
    backgroundTint,
    contentBackground
  } = useSettingsStore();

  const { theme, getThemeStyles } = useTheme();

  // Aplicar estilos de tema al cargar
  useEffect(() => {
    const styles = getThemeStyles();
    const root = document.documentElement;

    Object.entries(styles).forEach(([property, value]) => {
      root.style.setProperty(property, value as string);
    });
  }, [theme, getThemeStyles]);

  // Calcular el color del texto basado en el tinte del fondo
  const textColor = backgroundTint < 0
    ? `rgba(0, 0, 0, ${textContrast / 100})`
    : `rgba(255, 255, 255, ${textContrast / 100})`;

  // Definimos el estilo glassmorphism basado en la configuración
  const glassStyle = {
    backgroundColor: `rgba(${glassmorphismColor || '17, 25, 40'}, ${glassmorphismOpacity / 100})`,
    backdropFilter: `blur(${glassmorphismOpacity / 10 + 5}px) saturate(180%)`,
    WebkitBackdropFilter: `blur(${glassmorphismOpacity / 10 + 5}px) saturate(180%)`,
    borderRadius: '12px',
    border: '1px solid rgba(255, 255, 255, 0.125)',
    color: textColor
  };

  // Determinar la clase de fondo para el contenido
  const getContentBackgroundClass = () => {
    switch (contentBackground) {
      case 'glass':
        return 'glassmorphism';
      case 'solid':
        return 'bg-card';
      case 'elegant':
      default:
        return 'bg-elegant';
    }
  };

  return (
    <div className="h-screen overflow-hidden bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-50 dark:from-gray-900 dark:via-blue-900 dark:to-indigo-900 transition-all duration-500">
      {/* Header fijo en la parte superior */}
      <Header
        glassStyle={glassStyle}
        onToggleSidebar={() => setIsSidebarOpen(!isSidebarOpen)}
      />

      {/* Container principal con sidebar y contenido */}
      <div className="flex h-full pt-16">
        {/* Sidebar fijo a la izquierda */}
        <Sidebar
          glassStyle={glassStyle}
          isOpen={isSidebarOpen}
          onClose={() => setIsSidebarOpen(false)}
        />

        {/* Área de contenido con scroll independiente */}
        <div className={`flex flex-col flex-1 h-full transition-all duration-300 ${
          isSidebarOpen ? 'lg:ml-64' : 'lg:ml-64'
        }`}>
          {/* Contenido principal con scroll */}
          <main className="flex-1 overflow-y-auto custom-scrollbar">
            <div className="p-6">
              <div className="max-w-7xl mx-auto">
                {children}
              </div>
            </div>
          </main>

          {/* Footer fijo en la parte inferior del área de contenido */}
          <Footer glassStyle={glassStyle} />
        </div>
      </div>
    </div>
  );
};
