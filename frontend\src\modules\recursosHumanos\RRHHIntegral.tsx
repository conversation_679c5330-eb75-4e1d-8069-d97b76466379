import React, { useState } from 'react';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import {
  faUsers,
  faBriefcase,
  faSitemap,
  faMoneyBill,
  faFileContract,
  faPlane,
  faCalendarCheck,
  faSearch,
  faPlus,
  faClock,
  faGraduationCap,
  faStarHalfAlt,
  faEdit,
  faTrash,
  faUserMd,
  faClipboardList,
  faChartLine,
  faFileInvoiceDollar,
  faShieldAlt,
  faTools,
  faHeart,
  faExclamationTriangle,
  faCheckCircle,
  faTimesCircle,
  faCalendarAlt,
  faUserTie,
  faHandHoldingUsd,
  faAward,
  faUserGraduate
} from '@fortawesome/free-solid-svg-icons';
import { useAuth } from '../../services/authService';
import { useQuery } from '@tanstack/react-query';
import * as recursosHumanosService from '../../services/recursosHumanosService';

// Importar componentes específicos
import DashboardRRHH from './components/DashboardRRHH';
import GestionEmpleados from './components/GestionEmpleados';
import GestionDepartamentos from './components/GestionDepartamentos';
import GestionCargos from './components/GestionCargos';
import GestionNomina from './components/GestionNomina';
import GestionContratos from './components/GestionContratos';
import GestionVacaciones from './components/GestionVacaciones';
import GestionPermisos from './components/GestionPermisos';
import GestionTurnos from './components/GestionTurnos';
import GestionIncapacidades from './components/GestionIncapacidades';
import GestionCapacitaciones from './components/GestionCapacitaciones';
import GestionEvaluaciones from './components/GestionEvaluaciones';
import GestionHorasExtras from './components/GestionHorasExtras';
import GestionPrestaciones from './components/GestionPrestaciones';
import GestionDotaciones from './components/GestionDotaciones';
import ReportesRRHH from './components/ReportesRRHH';

type TabType = 
  | 'dashboard'
  | 'empleados'
  | 'departamentos'
  | 'cargos'
  | 'nomina'
  | 'contratos'
  | 'vacaciones'
  | 'permisos'
  | 'turnos'
  | 'incapacidades'
  | 'capacitaciones'
  | 'evaluaciones'
  | 'horas_extras'
  | 'prestaciones'
  | 'dotaciones'
  | 'reportes';

export const RRHHIntegral: React.FC = () => {
  const [activeTab, setActiveTab] = useState<TabType>('dashboard');
  const { user } = useAuth();
  const hospitalId = user?.hospital_id || 1;

  // Cargar datos del resumen para el dashboard
  const { data: resumen, isLoading: isLoadingResumen } = useQuery({
    queryKey: ['resumenRRHH', hospitalId],
    queryFn: () => recursosHumanosService.getResumenRecursosHumanos(hospitalId),
    enabled: !!hospitalId,
  });

  const tabs = [
    { id: 'dashboard', label: 'Dashboard', icon: faChartLine, color: 'text-primary' },
    { id: 'empleados', label: 'Empleados', icon: faUsers, color: 'text-blue-600' },
    { id: 'departamentos', label: 'Departamentos', icon: faSitemap, color: 'text-green-600' },
    { id: 'cargos', label: 'Cargos', icon: faBriefcase, color: 'text-purple-600' },
    { id: 'nomina', label: 'Nómina', icon: faMoneyBill, color: 'text-yellow-600' },
    { id: 'contratos', label: 'Contratos', icon: faFileContract, color: 'text-indigo-600' },
    { id: 'vacaciones', label: 'Vacaciones', icon: faPlane, color: 'text-cyan-600' },
    { id: 'permisos', label: 'Permisos', icon: faCalendarCheck, color: 'text-orange-600' },
    { id: 'turnos', label: 'Turnos', icon: faClock, color: 'text-pink-600' },
    { id: 'incapacidades', label: 'Incapacidades', icon: faHeart, color: 'text-red-600' },
    { id: 'capacitaciones', label: 'Capacitaciones', icon: faGraduationCap, color: 'text-emerald-600' },
    { id: 'evaluaciones', label: 'Evaluaciones', icon: faStarHalfAlt, color: 'text-amber-600' },
    { id: 'horas_extras', label: 'Horas Extras', icon: faClipboardList, color: 'text-teal-600' },
    { id: 'prestaciones', label: 'Prestaciones', icon: faHandHoldingUsd, color: 'text-lime-600' },
    { id: 'dotaciones', label: 'Dotaciones', icon: faTools, color: 'text-slate-600' },
    { id: 'reportes', label: 'Reportes', icon: faFileInvoiceDollar, color: 'text-rose-600' }
  ];

  const renderTabContent = () => {
    switch (activeTab) {
      case 'dashboard':
        return <DashboardRRHH resumen={resumen} isLoading={isLoadingResumen} />;
      case 'empleados':
        return <GestionEmpleados />;
      case 'departamentos':
        return <GestionDepartamentos />;
      case 'cargos':
        return <GestionCargos />;
      case 'nomina':
        return <GestionNomina />;
      case 'contratos':
        return <GestionContratos />;
      case 'vacaciones':
        return <GestionVacaciones />;
      case 'permisos':
        return <GestionPermisos />;
      case 'turnos':
        return <GestionTurnos />;
      case 'incapacidades':
        return <GestionIncapacidades />;
      case 'capacitaciones':
        return <GestionCapacitaciones />;
      case 'evaluaciones':
        return <GestionEvaluaciones />;
      case 'horas_extras':
        return <GestionHorasExtras />;
      case 'prestaciones':
        return <GestionPrestaciones />;
      case 'dotaciones':
        return <GestionDotaciones />;
      case 'reportes':
        return <ReportesRRHH />;
      default:
        return <DashboardRRHH resumen={resumen} isLoading={isLoadingResumen} />;
    }
  };

  return (
    <div className="min-h-screen bg-background">
      {/* Header */}
      <div className="bg-card shadow-lg">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center py-6">
            <div>
              <h1 className="text-3xl font-bold text-primary flex items-center">
                <FontAwesomeIcon icon={faUsers} className="mr-3" />
                Recursos Humanos Integral
              </h1>
              <p className="text-muted mt-1">
                Sistema completo de gestión del talento humano
              </p>
            </div>
            <div className="flex items-center space-x-4">
              <div className="text-right">
                <div className="text-sm text-muted">Hospital</div>
                <div className="text-primary font-medium">{user?.hospital_nombre || 'Hospital Principal'}</div>
              </div>
              <div className="text-right">
                <div className="text-sm text-muted">Total Empleados</div>
                <div className="text-2xl font-bold text-primary">{resumen?.total_empleados || 0}</div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Navigation Tabs */}
      <div className="bg-card border-b border-color">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex space-x-1 overflow-x-auto py-2">
            {tabs.map((tab) => (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id as TabType)}
                className={`flex items-center px-4 py-3 rounded-lg text-sm font-medium transition-all duration-200 whitespace-nowrap ${
                  activeTab === tab.id
                    ? 'bg-primary text-white shadow-lg transform scale-105'
                    : 'text-secondary hover:bg-secondary/20 hover:text-primary'
                }`}
              >
                <FontAwesomeIcon 
                  icon={tab.icon} 
                  className={`mr-2 ${activeTab === tab.id ? 'text-white' : tab.color}`} 
                />
                {tab.label}
              </button>
            ))}
          </div>
        </div>
      </div>

      {/* Content */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {renderTabContent()}
      </div>

      {/* Quick Stats Footer */}
      {resumen && (
        <div className="bg-card border-t border-color mt-8">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4">
            <div className="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-8 gap-4">
              <div className="text-center">
                <div className="text-2xl font-bold text-success">{resumen.empleados_activos}</div>
                <div className="text-xs text-muted">Activos</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-warning">{resumen.empleados_vacaciones}</div>
                <div className="text-xs text-muted">Vacaciones</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-info">{resumen.empleados_licencia}</div>
                <div className="text-xs text-muted">Licencias</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-error">{resumen.empleados_inactivos}</div>
                <div className="text-xs text-muted">Inactivos</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-primary">{Object.keys(resumen.por_departamento).length}</div>
                <div className="text-xs text-muted">Departamentos</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-secondary">{Object.keys(resumen.por_cargo).length}</div>
                <div className="text-xs text-muted">Cargos</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-purple-600">{Object.values(resumen.por_tipo_contrato).reduce((a, b) => a + b, 0)}</div>
                <div className="text-xs text-muted">Contratos</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-green-600">
                  {resumen.nomina_actual ? '✓' : '✗'}
                </div>
                <div className="text-xs text-muted">Nómina Actual</div>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default RRHHIntegral;
