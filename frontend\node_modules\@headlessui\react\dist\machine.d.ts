export declare abstract class Machine<State, Event extends {
    type: number | string;
}> {
    #private;
    constructor(initialState: State);
    get state(): Readonly<State>;
    abstract reduce(state: Readonly<State>, event: Event): Readonly<State>;
    subscribe<Slice>(selector: (state: Readonly<State>) => Slice, callback: (state: Slice) => void): () => void;
    on(type: Event['type'], callback: (state: State, event: Event) => void): () => void;
    send(event: Event): void;
}
export declare function shallowEqual(a: any, b: any): boolean;
export declare function batch<F extends (...args: any[]) => void, P extends any[] = Parameters<F>>(setup: () => [callback: F, handle: () => void]): (...args: P) => void;
