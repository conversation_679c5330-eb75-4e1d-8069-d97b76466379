import React, { useEffect, useRef, useState } from 'react';
import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, useMap, Circle } from 'react-leaflet';
import { Icon, DivIcon } from 'leaflet';
import { 
  Truck, 
  MapPin, 
  Navigation, 
  Clock, 
  AlertTriangle,
  Phone,
  User,
  Activity
} from 'lucide-react';
import { useAmbulanceStore, Ambulance } from '../../store/ambulance.store';
import { wsService } from '../../services/websocket.service';
import 'leaflet/dist/leaflet.css';

// Configuración de iconos personalizados
const createAmbulanceIcon = (status: Ambulance['status'], isSelected: boolean = false) => {
  const statusColors = {
    available: '#10B981', // Verde
    en_route: '#F59E0B',  // Amarillo
    on_scene: '#EF4444',  // Rojo
    returning: '#3B82F6', // Azul
    offline: '#6B7280',   // Gris
    maintenance: '#8B5CF6' // Púrpura
  };

  const color = statusColors[status];
  const size = isSelected ? 40 : 32;
  const borderWidth = isSelected ? 3 : 2;

  return new DivIcon({
    html: `
      <div style="
        width: ${size}px;
        height: ${size}px;
        background: ${color};
        border: ${borderWidth}px solid white;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        box-shadow: 0 2px 8px rgba(0,0,0,0.3);
        ${isSelected ? 'animation: pulse 2s infinite;' : ''}
      ">
        <svg width="16" height="16" fill="white" viewBox="0 0 24 24">
          <path d="M4 16v4a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2v-4M4 8v8h16V8M8 8V6a2 2 0 0 1 2-2h4a2 2 0 0 1 2 2v2"/>
        </svg>
      </div>
      <style>
        @keyframes pulse {
          0% { transform: scale(1); }
          50% { transform: scale(1.1); }
          100% { transform: scale(1); }
        }
      </style>
    `,
    className: 'ambulance-marker',
    iconSize: [size, size],
    iconAnchor: [size / 2, size / 2]
  });
};

const emergencyIcon = new DivIcon({
  html: `
    <div style="
      width: 32px;
      height: 32px;
      background: #DC2626;
      border: 2px solid white;
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      box-shadow: 0 2px 8px rgba(0,0,0,0.3);
      animation: emergency-pulse 1s infinite;
    ">
      <svg width="16" height="16" fill="white" viewBox="0 0 24 24">
        <path d="M12 2L2 7v10c0 5.55 3.84 9.74 9 11 5.16-1.26 9-5.45 9-11V7l-10-5z"/>
        <path d="M12 8v4M12 16h.01"/>
      </svg>
    </div>
    <style>
      @keyframes emergency-pulse {
        0% { transform: scale(1); opacity: 1; }
        50% { transform: scale(1.2); opacity: 0.7; }
        100% { transform: scale(1); opacity: 1; }
      }
    </style>
  `,
  className: 'emergency-marker',
  iconSize: [32, 32],
  iconAnchor: [16, 16]
});

// Componente para actualizar el centro del mapa
const MapController: React.FC = () => {
  const map = useMap();
  const mapCenter = useAmbulanceStore(state => state.mapCenter);
  const mapZoom = useAmbulanceStore(state => state.mapZoom);

  useEffect(() => {
    map.setView([mapCenter.latitude, mapCenter.longitude], mapZoom);
  }, [map, mapCenter, mapZoom]);

  return null;
};

// Componente principal del mapa
export const AmbulanceMap: React.FC = () => {
  const {
    ambulances,
    emergencyServices,
    selectedAmbulanceId,
    mapCenter,
    mapZoom,
    showAllAmbulances,
    filterStatus,
    selectAmbulance,
    setMapCenter,
    setMapZoom
  } = useAmbulanceStore();

  const [userLocation, setUserLocation] = useState<{lat: number, lng: number} | null>(null);
  const mapRef = useRef<any>(null);

  // Obtener ubicación del usuario
  useEffect(() => {
    if (navigator.geolocation) {
      navigator.geolocation.getCurrentPosition(
        (position) => {
          setUserLocation({
            lat: position.coords.latitude,
            lng: position.coords.longitude
          });
        },
        (error) => {
          console.warn('No se pudo obtener la ubicación del usuario:', error);
        }
      );
    }
  }, []);

  // Filtrar ambulancias según criterios
  const filteredAmbulances = ambulances.filter(ambulance => {
    if (!showAllAmbulances && ambulance.status === 'offline') return false;
    if (filterStatus.length > 0 && !filterStatus.includes(ambulance.status)) return false;
    return true;
  });

  // Servicios de emergencia activos
  const activeEmergencies = emergencyServices.filter(service => 
    ['pending', 'assigned', 'en_route'].includes(service.status)
  );

  // Manejar clic en ambulancia
  const handleAmbulanceClick = (ambulance: Ambulance) => {
    selectAmbulance(ambulance.id);
    setMapCenter({
      latitude: ambulance.location.latitude,
      longitude: ambulance.location.longitude
    });
  };

  // Manejar clic en emergencia
  const handleEmergencyClick = (emergency: any) => {
    setMapCenter({
      latitude: emergency.location.latitude,
      longitude: emergency.location.longitude
    });
  };

  // Renderizar información de ambulancia
  const renderAmbulancePopup = (ambulance: Ambulance) => (
    <Popup>
      <div className="p-2 min-w-64">
        <div className="flex items-center gap-2 mb-2">
          <Truck className="h-5 w-5 text-primary" />
          <h3 className="font-semibold text-lg">{ambulance.code}</h3>
          <span className={`px-2 py-1 rounded-full text-xs font-medium ${
            ambulance.status === 'available' ? 'bg-success/20 text-success' :
            ambulance.status === 'en_route' ? 'bg-warning/20 text-warning' :
            ambulance.status === 'on_scene' ? 'bg-error/20 text-error' :
            'bg-muted/20 text-muted'
          }`}>
            {ambulance.status.replace('_', ' ').toUpperCase()}
          </span>
        </div>

        <div className="space-y-2 text-sm">
          <div className="flex items-center gap-2">
            <MapPin className="h-4 w-4 text-muted" />
            <span>{ambulance.location.address || 'Ubicación no disponible'}</span>
          </div>

          <div className="flex items-center gap-2">
            <User className="h-4 w-4 text-muted" />
            <span>Conductor: {ambulance.crew.driver}</span>
          </div>

          {ambulance.crew.paramedic && (
            <div className="flex items-center gap-2">
              <Activity className="h-4 w-4 text-muted" />
              <span>Paramédico: {ambulance.crew.paramedic}</span>
            </div>
          )}

          <div className="flex items-center gap-2">
            <Clock className="h-4 w-4 text-muted" />
            <span>Última actualización: {new Date(ambulance.location.lastUpdate).toLocaleTimeString()}</span>
          </div>

          {ambulance.currentService && (
            <div className="mt-3 p-2 bg-info/10 rounded">
              <div className="flex items-center gap-2 mb-1">
                <AlertTriangle className="h-4 w-4 text-info" />
                <span className="font-medium text-info">Servicio Activo</span>
              </div>
              <p className="text-xs">ID: {ambulance.currentService.emergencyId}</p>
              <p className="text-xs">Inicio: {new Date(ambulance.currentService.startTime).toLocaleTimeString()}</p>
            </div>
          )}
        </div>

        <div className="flex gap-2 mt-3">
          <button
            onClick={() => handleAmbulanceClick(ambulance)}
            className="flex-1 bg-primary text-white px-3 py-1 rounded text-sm hover:bg-primary-dark transition-colors"
          >
            Seleccionar
          </button>
          <button
            onClick={() => {/* Implementar llamada */}}
            className="px-3 py-1 border border-border rounded text-sm hover:bg-border/50 transition-colors"
          >
            <Phone className="h-4 w-4" />
          </button>
        </div>
      </div>
    </Popup>
  );

  // Renderizar información de emergencia
  const renderEmergencyPopup = (emergency: any) => (
    <Popup>
      <div className="p-2 min-w-64">
        <div className="flex items-center gap-2 mb-2">
          <AlertTriangle className="h-5 w-5 text-error" />
          <h3 className="font-semibold text-lg">Emergencia</h3>
          <span className={`px-2 py-1 rounded-full text-xs font-medium ${
            emergency.priority === 'critical' ? 'bg-error/20 text-error' :
            emergency.priority === 'high' ? 'bg-warning/20 text-warning' :
            'bg-info/20 text-info'
          }`}>
            {emergency.priority.toUpperCase()}
          </span>
        </div>

        <div className="space-y-2 text-sm">
          <div className="flex items-center gap-2">
            <MapPin className="h-4 w-4 text-muted" />
            <span>{emergency.location.address}</span>
          </div>

          <div>
            <span className="font-medium">Tipo:</span> {emergency.type}
          </div>

          <div>
            <span className="font-medium">Descripción:</span>
            <p className="mt-1">{emergency.description}</p>
          </div>

          <div className="flex items-center gap-2">
            <Clock className="h-4 w-4 text-muted" />
            <span>Reportado: {new Date(emergency.createdAt).toLocaleTimeString()}</span>
          </div>

          {emergency.assignedAmbulanceId && (
            <div className="mt-2 p-2 bg-success/10 rounded">
              <span className="text-success font-medium">
                Ambulancia asignada: {emergency.assignedAmbulanceId}
              </span>
            </div>
          )}
        </div>

        <div className="flex gap-2 mt-3">
          <button
            onClick={() => handleEmergencyClick(emergency)}
            className="flex-1 bg-error text-white px-3 py-1 rounded text-sm hover:bg-error/90 transition-colors"
          >
            Ver Detalles
          </button>
        </div>
      </div>
    </Popup>
  );

  return (
    <div className="h-full w-full relative">
      <MapContainer
        ref={mapRef}
        center={[mapCenter.latitude, mapCenter.longitude]}
        zoom={mapZoom}
        className="h-full w-full"
        zoomControl={false}
      >
        <MapController />
        
        {/* Capa base del mapa */}
        <TileLayer
          attribution='&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors'
          url="https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png"
        />

        {/* Marcadores de ambulancias */}
        {filteredAmbulances.map((ambulance) => (
          <Marker
            key={ambulance.id}
            position={[ambulance.location.latitude, ambulance.location.longitude]}
            icon={createAmbulanceIcon(ambulance.status, ambulance.id === selectedAmbulanceId)}
            eventHandlers={{
              click: () => handleAmbulanceClick(ambulance)
            }}
          >
            {renderAmbulancePopup(ambulance)}
          </Marker>
        ))}

        {/* Marcadores de emergencias */}
        {activeEmergencies.map((emergency) => (
          <Marker
            key={emergency.id}
            position={[emergency.location.latitude, emergency.location.longitude]}
            icon={emergencyIcon}
            eventHandlers={{
              click: () => handleEmergencyClick(emergency)
            }}
          >
            {renderEmergencyPopup(emergency)}
          </Marker>
        ))}

        {/* Ubicación del usuario */}
        {userLocation && (
          <>
            <Marker
              position={[userLocation.lat, userLocation.lng]}
              icon={new DivIcon({
                html: `
                  <div style="
                    width: 16px;
                    height: 16px;
                    background: #3B82F6;
                    border: 3px solid white;
                    border-radius: 50%;
                    box-shadow: 0 2px 8px rgba(0,0,0,0.3);
                  "></div>
                `,
                className: 'user-location-marker',
                iconSize: [16, 16],
                iconAnchor: [8, 8]
              })}
            >
              <Popup>
                <div className="text-center">
                  <Navigation className="h-5 w-5 mx-auto mb-1 text-primary" />
                  <p className="font-medium">Su ubicación</p>
                </div>
              </Popup>
            </Marker>
            <Circle
              center={[userLocation.lat, userLocation.lng]}
              radius={100}
              pathOptions={{
                color: '#3B82F6',
                fillColor: '#3B82F6',
                fillOpacity: 0.1,
                weight: 2
              }}
            />
          </>
        )}
      </MapContainer>

      {/* Controles del mapa */}
      <div className="absolute top-4 right-4 z-[1000] space-y-2">
        <button
          onClick={() => setMapZoom(mapZoom + 1)}
          className="block w-10 h-10 bg-bg-card border border-border rounded shadow-lg hover:bg-border/50 transition-colors"
          title="Acercar"
        >
          +
        </button>
        <button
          onClick={() => setMapZoom(mapZoom - 1)}
          className="block w-10 h-10 bg-bg-card border border-border rounded shadow-lg hover:bg-border/50 transition-colors"
          title="Alejar"
        >
          -
        </button>
        {userLocation && (
          <button
            onClick={() => setMapCenter({ latitude: userLocation.lat, longitude: userLocation.lng })}
            className="block w-10 h-10 bg-bg-card border border-border rounded shadow-lg hover:bg-border/50 transition-colors"
            title="Mi ubicación"
          >
            <Navigation className="h-4 w-4 mx-auto text-primary" />
          </button>
        )}
      </div>

      {/* Leyenda */}
      <div className="absolute bottom-4 left-4 z-[1000] bg-bg-card border border-border rounded-lg p-3 shadow-lg">
        <h4 className="font-semibold mb-2 text-sm">Leyenda</h4>
        <div className="space-y-1 text-xs">
          <div className="flex items-center gap-2">
            <div className="w-3 h-3 bg-success rounded-full"></div>
            <span>Disponible</span>
          </div>
          <div className="flex items-center gap-2">
            <div className="w-3 h-3 bg-warning rounded-full"></div>
            <span>En ruta</span>
          </div>
          <div className="flex items-center gap-2">
            <div className="w-3 h-3 bg-error rounded-full"></div>
            <span>En escena</span>
          </div>
          <div className="flex items-center gap-2">
            <div className="w-3 h-3 bg-primary rounded-full"></div>
            <span>Regresando</span>
          </div>
        </div>
      </div>
    </div>
  );
};

export default AmbulanceMap;
