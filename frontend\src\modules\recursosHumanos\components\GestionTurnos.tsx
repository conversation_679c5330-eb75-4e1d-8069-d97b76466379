import React from 'react';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faClock, faPlus } from '@fortawesome/free-solid-svg-icons';
import { Button } from '../../../components/ui/Button';

const GestionTurnos: React.FC = () => {
  return (
    <div className="space-y-6">
      <div className="bg-card p-6 rounded-lg shadow-lg">
        <div className="flex justify-between items-center">
          <div>
            <h2 className="text-2xl font-bold text-primary flex items-center">
              <FontAwesomeIcon icon={faClock} className="mr-2" />
              Gestión de Turnos
            </h2>
            <p className="text-muted mt-1">
              Programación y asignación de turnos
            </p>
          </div>
          <Button>
            <FontAwesomeIcon icon={faPlus} className="mr-2" />
            Nuevo Turno
          </Button>
        </div>
      </div>
      
      <div className="bg-card p-6 rounded-lg shadow-lg">
        <p className="text-center text-muted">Componente en desarrollo...</p>
      </div>
    </div>
  );
};

export default GestionTurnos;
