import{createContext as o,useContext as r,useMemo as i}from"react";import{ListboxMachine as s}from'./listbox-machine.js';const c=o(null);function l(t){let e=r(c);if(e===null){let n=new Error(`<${t} /> is missing a parent <Listbox /> component.`);throw Error.captureStackTrace&&Error.captureStackTrace(n,a),n}return e}function a({__demoMode:t=!1}={}){return i(()=>s.new({__demoMode:t}),[])}export{c as ListboxContext,a as useListboxMachine,l as useListboxMachineContext};
