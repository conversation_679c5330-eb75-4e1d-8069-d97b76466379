"use client";import{useFocusRing as Ie}from"@react-aria/focus";import{useHover as De}from"@react-aria/interactions";import E,{createContext as re,createRef as ce,useContext as ne,useEffect as le,use<PERSON>emo as D,useReducer as he,useRef as te,useState as ae}from"react";import{useActivePress as ke}from'../../hooks/use-active-press.js';import{useElementSize as Ge}from'../../hooks/use-element-size.js';import{useEvent as b}from'../../hooks/use-event.js';import{useEventListener as He}from'../../hooks/use-event-listener.js';import{useId as pe}from'../../hooks/use-id.js';import{useIsoMorphicEffect as Ue}from'../../hooks/use-iso-morphic-effect.js';import{useLatestValue as Ce}from'../../hooks/use-latest-value.js';import{useOnDisappear as Ne}from'../../hooks/use-on-disappear.js';import{useOutsideClick as we}from'../../hooks/use-outside-click.js';import{useOwnerDocument as se}from'../../hooks/use-owner.js';import{useResolveButtonType as Ke}from'../../hooks/use-resolve-button-type.js';import{MainTreeProvider as Re,useMainTreeNode as We,useRootContainers as je}from'../../hooks/use-root-containers.js';import{useScrollLock as Ve}from'../../hooks/use-scroll-lock.js';import{optionalRef as $e,useSyncRefs as z}from'../../hooks/use-sync-refs.js';import{Direction as G,useTabDirection as Be}from'../../hooks/use-tab-direction.js';import{transitionDataAttributes as _e,useTransition as Fe}from'../../hooks/use-transition.js';import{CloseProvider as Je}from'../../internal/close-provider.js';import{FloatingProvider as Xe,useFloatingPanel as qe,useFloatingPanelProps as ze,useFloatingReference as Ye,useResolvedAnchor as Qe}from'../../internal/floating.js';import{Hidden as ve,HiddenFeatures as Te}from'../../internal/hidden.js';import{OpenClosedProvider as Ze,ResetOpenClosedProvider as et,State as Y,useOpenClosed as xe}from'../../internal/open-closed.js';import{isDisabledReactIssue7711 as Me}from'../../utils/bugs.js';import{Focus as H,FocusResult as me,FocusableMode as tt,focusIn as K,getFocusableElements as ye,isFocusableElement as ot}from'../../utils/focus-management.js';import{match as W}from'../../utils/match.js';import'../../utils/micro-task.js';import{getOwnerDocument as rt}from'../../utils/owner.js';import{RenderFeatures as ue,forwardRefWithAs as Q,mergeProps as Ee,useRender as oe}from'../../utils/render.js';import{Keys as j}from'../keyboard.js';import{Portal as nt,useNestedPortals as lt}from'../portal/portal.js';var at=(P=>(P[P.Open=0]="Open",P[P.Closed=1]="Closed",P))(at||{}),pt=(s=>(s[s.TogglePopover=0]="TogglePopover",s[s.ClosePopover=1]="ClosePopover",s[s.SetButton=2]="SetButton",s[s.SetButtonId=3]="SetButtonId",s[s.SetPanel=4]="SetPanel",s[s.SetPanelId=5]="SetPanelId",s))(pt||{});let st={[0]:t=>({...t,popoverState:W(t.popoverState,{[0]:1,[1]:0}),__demoMode:!1}),[1](t){return t.popoverState===1?t:{...t,popoverState:1,__demoMode:!1}},[2](t,l){return t.button===l.button?t:{...t,button:l.button}},[3](t,l){return t.buttonId===l.buttonId?t:{...t,buttonId:l.buttonId}},[4](t,l){return t.panel===l.panel?t:{...t,panel:l.panel}},[5](t,l){return t.panelId===l.panelId?t:{...t,panelId:l.panelId}}},be=re(null);be.displayName="PopoverContext";function ie(t){let l=ne(be);if(l===null){let P=new Error(`<${t} /> is missing a parent <Popover /> component.`);throw Error.captureStackTrace&&Error.captureStackTrace(P,ie),P}return l}let de=re(null);de.displayName="PopoverAPIContext";function ge(t){let l=ne(de);if(l===null){let P=new Error(`<${t} /> is missing a parent <Popover /> component.`);throw Error.captureStackTrace&&Error.captureStackTrace(P,ge),P}return l}let Se=re(null);Se.displayName="PopoverGroupContext";function Oe(){return ne(Se)}let Pe=re(null);Pe.displayName="PopoverPanelContext";function ut(){return ne(Pe)}function it(t,l){return W(l.type,st,t,l)}let dt="div";function Pt(t,l){var q;let{__demoMode:P=!1,...C}=t,m=te(null),A=z(l,$e(a=>{m.current=a})),s=te([]),n=he(it,{__demoMode:P,popoverState:P?0:1,buttons:s,button:null,buttonId:null,panel:null,panelId:null,beforePanelSentinel:ce(),afterPanelSentinel:ce(),afterButtonSentinel:ce()}),[{popoverState:v,button:i,buttonId:o,panel:u,panelId:R,beforePanelSentinel:y,afterPanelSentinel:h,afterButtonSentinel:d},r]=n,T=se((q=m.current)!=null?q:i),g=D(()=>{if(!i||!u)return!1;for(let S of document.querySelectorAll("body > *"))if(Number(S==null?void 0:S.contains(i))^Number(S==null?void 0:S.contains(u)))return!0;let a=ye(),e=a.indexOf(i),p=(e+a.length-1)%a.length,f=(e+1)%a.length,c=a[p],O=a[f];return!u.contains(c)&&!u.contains(O)},[i,u]),_=Ce(o),L=Ce(R),I=D(()=>({buttonId:_,panelId:L,close:()=>r({type:1})}),[_,L,r]),M=Oe(),k=M==null?void 0:M.registerPopover,V=b(()=>{var a;return(a=M==null?void 0:M.isFocusWithinPopoverGroup())!=null?a:(T==null?void 0:T.activeElement)&&((i==null?void 0:i.contains(T.activeElement))||(u==null?void 0:u.contains(T.activeElement)))});le(()=>k==null?void 0:k(I),[k,I]);let[B,U]=lt(),F=We(i),N=je({mainTreeNode:F,portals:B,defaultContainers:[i,u]});He(T==null?void 0:T.defaultView,"focus",a=>{var e,p,f,c,O,S;a.target!==window&&a.target instanceof HTMLElement&&v===0&&(V()||i&&u&&(N.contains(a.target)||(p=(e=y.current)==null?void 0:e.contains)!=null&&p.call(e,a.target)||(c=(f=h.current)==null?void 0:f.contains)!=null&&c.call(f,a.target)||(S=(O=d.current)==null?void 0:O.contains)!=null&&S.call(O,a.target)||r({type:1})))},!0),we(v===0,N.resolveContainers,(a,e)=>{r({type:1}),ot(e,tt.Loose)||(a.preventDefault(),i==null||i.focus())});let x=b(a=>{r({type:1});let e=(()=>a?a instanceof HTMLElement?a:"current"in a&&a.current instanceof HTMLElement?a.current:i:i)();e==null||e.focus()}),ee=D(()=>({close:x,isPortalled:g}),[x,g]),$=D(()=>({open:v===0,close:x}),[v,x]),J={ref:A},X=oe();return E.createElement(Re,{node:F},E.createElement(Xe,null,E.createElement(Pe.Provider,{value:null},E.createElement(be.Provider,{value:n},E.createElement(de.Provider,{value:ee},E.createElement(Je,{value:x},E.createElement(Ze,{value:W(v,{[0]:Y.Open,[1]:Y.Closed})},E.createElement(U,null,X({ourProps:J,theirProps:C,slot:$,defaultTag:dt,name:"Popover"})))))))))}let ft="button";function ct(t,l){let P=pe(),{id:C=`headlessui-popover-button-${P}`,disabled:m=!1,autoFocus:A=!1,...s}=t,[n,v]=ie("Popover.Button"),{isPortalled:i}=ge("Popover.Button"),o=te(null),u=`headlessui-focus-sentinel-${pe()}`,R=Oe(),y=R==null?void 0:R.closeOthers,d=ut()!==null;le(()=>{if(!d)return v({type:3,buttonId:C}),()=>{v({type:3,buttonId:null})}},[d,C,v]);let[r]=ae(()=>Symbol()),T=z(o,l,Ye(),b(e=>{if(!d){if(e)n.buttons.current.push(r);else{let p=n.buttons.current.indexOf(r);p!==-1&&n.buttons.current.splice(p,1)}n.buttons.current.length>1&&console.warn("You are already using a <Popover.Button /> but only 1 <Popover.Button /> is supported."),e&&v({type:2,button:e})}})),g=z(o,l),_=se(o),L=b(e=>{var p,f,c;if(d){if(n.popoverState===1)return;switch(e.key){case j.Space:case j.Enter:e.preventDefault(),(f=(p=e.target).click)==null||f.call(p),v({type:1}),(c=n.button)==null||c.focus();break}}else switch(e.key){case j.Space:case j.Enter:e.preventDefault(),e.stopPropagation(),n.popoverState===1&&(y==null||y(n.buttonId)),v({type:0});break;case j.Escape:if(n.popoverState!==0)return y==null?void 0:y(n.buttonId);if(!o.current||_!=null&&_.activeElement&&!o.current.contains(_.activeElement))return;e.preventDefault(),e.stopPropagation(),v({type:1});break}}),I=b(e=>{d||e.key===j.Space&&e.preventDefault()}),M=b(e=>{var p,f;Me(e.currentTarget)||m||(d?(v({type:1}),(p=n.button)==null||p.focus()):(e.preventDefault(),e.stopPropagation(),n.popoverState===1&&(y==null||y(n.buttonId)),v({type:0}),(f=n.button)==null||f.focus()))}),k=b(e=>{e.preventDefault(),e.stopPropagation()}),{isFocusVisible:V,focusProps:B}=Ie({autoFocus:A}),{isHovered:U,hoverProps:F}=De({isDisabled:m}),{pressed:N,pressProps:Z}=ke({disabled:m}),x=n.popoverState===0,ee=D(()=>({open:x,active:N||x,disabled:m,hover:U,focus:V,autofocus:A}),[x,U,V,N,m,A]),$=Ke(t,n.button),J=d?Ee({ref:g,type:$,onKeyDown:L,onClick:M,disabled:m||void 0,autoFocus:A},B,F,Z):Ee({ref:T,id:n.buttonId,type:$,"aria-expanded":n.popoverState===0,"aria-controls":n.panel?n.panelId:void 0,disabled:m||void 0,autoFocus:A,onKeyDown:L,onKeyUp:I,onClick:M,onMouseDown:k},B,F,Z),X=Be(),q=b(()=>{let e=n.panel;if(!e)return;function p(){W(X.current,{[G.Forwards]:()=>K(e,H.First),[G.Backwards]:()=>K(e,H.Last)})===me.Error&&K(ye().filter(c=>c.dataset.headlessuiFocusGuard!=="true"),W(X.current,{[G.Forwards]:H.Next,[G.Backwards]:H.Previous}),{relativeTo:n.button})}p()}),a=oe();return E.createElement(E.Fragment,null,a({ourProps:J,theirProps:s,slot:ee,defaultTag:ft,name:"Popover.Button"}),x&&!d&&i&&E.createElement(ve,{id:u,ref:n.afterButtonSentinel,features:Te.Focusable,"data-headlessui-focus-guard":!0,as:"button",type:"button",onFocus:q}))}let vt="div",Tt=ue.RenderStrategy|ue.Static;function Le(t,l){let P=pe(),{id:C=`headlessui-popover-backdrop-${P}`,transition:m=!1,...A}=t,[{popoverState:s},n]=ie("Popover.Backdrop"),[v,i]=ae(null),o=z(l,i),u=xe(),[R,y]=Fe(m,v,u!==null?(u&Y.Open)===Y.Open:s===0),h=b(g=>{if(Me(g.currentTarget))return g.preventDefault();n({type:1})}),d=D(()=>({open:s===0}),[s]),r={ref:o,id:C,"aria-hidden":!0,onClick:h,..._e(y)};return oe()({ourProps:r,theirProps:A,slot:d,defaultTag:vt,features:Tt,visible:R,name:"Popover.Backdrop"})}let mt="div",yt=ue.RenderStrategy|ue.Static;function Et(t,l){let P=pe(),{id:C=`headlessui-popover-panel-${P}`,focus:m=!1,anchor:A,portal:s=!1,modal:n=!1,transition:v=!1,...i}=t,[o,u]=ie("Popover.Panel"),{close:R,isPortalled:y}=ge("Popover.Panel"),h=`headlessui-focus-sentinel-before-${P}`,d=`headlessui-focus-sentinel-after-${P}`,r=te(null),T=Qe(A),[g,_]=qe(T),L=ze();T&&(s=!0);let[I,M]=ae(null),k=z(r,l,T?g:null,b(e=>u({type:4,panel:e})),M),V=se(o.button),B=se(r);Ue(()=>(u({type:5,panelId:C}),()=>{u({type:5,panelId:null})}),[C,u]);let U=xe(),[F,N]=Fe(v,I,U!==null?(U&Y.Open)===Y.Open:o.popoverState===0);Ne(F,o.button,()=>{u({type:1})});let Z=o.__demoMode?!1:n&&F;Ve(Z,B);let x=b(e=>{var p;switch(e.key){case j.Escape:if(o.popoverState!==0||!r.current||B!=null&&B.activeElement&&!r.current.contains(B.activeElement))return;e.preventDefault(),e.stopPropagation(),u({type:1}),(p=o.button)==null||p.focus();break}});le(()=>{var e;t.static||o.popoverState===1&&((e=t.unmount)==null||e)&&u({type:4,panel:null})},[o.popoverState,t.unmount,t.static,u]),le(()=>{if(o.__demoMode||!m||o.popoverState!==0||!r.current)return;let e=B==null?void 0:B.activeElement;r.current.contains(e)||K(r.current,H.First)},[o.__demoMode,m,r.current,o.popoverState]);let ee=D(()=>({open:o.popoverState===0,close:R}),[o.popoverState,R]),$=Ee(T?L():{},{ref:k,id:C,onKeyDown:x,onBlur:m&&o.popoverState===0?e=>{var f,c,O,S,w;let p=e.relatedTarget;p&&r.current&&((f=r.current)!=null&&f.contains(p)||(u({type:1}),((O=(c=o.beforePanelSentinel.current)==null?void 0:c.contains)!=null&&O.call(c,p)||(w=(S=o.afterPanelSentinel.current)==null?void 0:S.contains)!=null&&w.call(S,p))&&p.focus({preventScroll:!0})))}:void 0,tabIndex:-1,style:{...i.style,..._,"--button-width":Ge(o.button,!0).width},..._e(N)}),J=Be(),X=b(()=>{let e=r.current;if(!e)return;function p(){W(J.current,{[G.Forwards]:()=>{var c;K(e,H.First)===me.Error&&((c=o.afterPanelSentinel.current)==null||c.focus())},[G.Backwards]:()=>{var f;(f=o.button)==null||f.focus({preventScroll:!0})}})}p()}),q=b(()=>{let e=r.current;if(!e)return;function p(){W(J.current,{[G.Forwards]:()=>{if(!o.button)return;let f=ye(),c=f.indexOf(o.button),O=f.slice(0,c+1),w=[...f.slice(c+1),...O];for(let fe of w.slice())if(fe.dataset.headlessuiFocusGuard==="true"||I!=null&&I.contains(fe)){let Ae=w.indexOf(fe);Ae!==-1&&w.splice(Ae,1)}K(w,H.First,{sorted:!1})},[G.Backwards]:()=>{var c;K(e,H.Previous)===me.Error&&((c=o.button)==null||c.focus())}})}p()}),a=oe();return E.createElement(et,null,E.createElement(Pe.Provider,{value:C},E.createElement(de.Provider,{value:{close:R,isPortalled:y}},E.createElement(nt,{enabled:s?t.static||F:!1,ownerDocument:V},F&&y&&E.createElement(ve,{id:h,ref:o.beforePanelSentinel,features:Te.Focusable,"data-headlessui-focus-guard":!0,as:"button",type:"button",onFocus:X}),a({ourProps:$,theirProps:i,slot:ee,defaultTag:mt,features:yt,visible:F,name:"Popover.Panel"}),F&&y&&E.createElement(ve,{id:d,ref:o.afterPanelSentinel,features:Te.Focusable,"data-headlessui-focus-guard":!0,as:"button",type:"button",onFocus:q})))))}let bt="div";function gt(t,l){let P=te(null),C=z(P,l),[m,A]=ae([]),s=b(d=>{A(r=>{let T=r.indexOf(d);if(T!==-1){let g=r.slice();return g.splice(T,1),g}return r})}),n=b(d=>(A(r=>[...r,d]),()=>s(d))),v=b(()=>{var T;let d=rt(P);if(!d)return!1;let r=d.activeElement;return(T=P.current)!=null&&T.contains(r)?!0:m.some(g=>{var _,L;return((_=d.getElementById(g.buttonId.current))==null?void 0:_.contains(r))||((L=d.getElementById(g.panelId.current))==null?void 0:L.contains(r))})}),i=b(d=>{for(let r of m)r.buttonId.current!==d&&r.close()}),o=D(()=>({registerPopover:n,unregisterPopover:s,isFocusWithinPopoverGroup:v,closeOthers:i}),[n,s,v,i]),u=D(()=>({}),[]),R=t,y={ref:C},h=oe();return E.createElement(Re,null,E.createElement(Se.Provider,{value:o},h({ourProps:y,theirProps:R,slot:u,defaultTag:bt,name:"Popover.Group"})))}let St=Q(Pt),At=Q(ct),Ct=Q(Le),Rt=Q(Le),Bt=Q(Et),_t=Q(gt),ao=Object.assign(St,{Button:At,Backdrop:Rt,Overlay:Ct,Panel:Bt,Group:_t});export{ao as Popover,Rt as PopoverBackdrop,At as PopoverButton,_t as PopoverGroup,Ct as PopoverOverlay,Bt as PopoverPanel};
