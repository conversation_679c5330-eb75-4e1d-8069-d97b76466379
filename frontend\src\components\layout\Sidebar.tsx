import { useState, CSSProperties } from 'react';
import { Link, useLocation } from 'react-router-dom';
// import { useAuth } from '../../context/AuthContext';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import {
  faStethoscope,
  faAmbulance,
  faProcedures,
  faBed,
  faBoxes,
  faMedkit,
  faPills,
  faStethoscope as faStethoscopeIcon,
  faChevronDown,
  faChevronRight,
  faHospital,
  faUserMd,
  faCog,
  faWarehouse,
  faMoneyBill,
  faChartBar,
  faChartLine,
  faRecycle,
  faTruck,
  faCheckCircle,
  faExclamationTriangle,
  faDatabase,
  faSync,
  faChartPie,
  faScissors,
} from '@fortawesome/free-solid-svg-icons';

export interface SidebarProps {
  glassStyle?: CSSProperties;
}

export const Sidebar = ({ glassStyle }: SidebarProps) => {
  // Temporalmente, no usar el contexto de autenticación para depuración
  // const { user, logout } = useAuth();
  const user = { username: 'Usuario de prueba', hospital_id: '1' };
  const logout = () => console.log('Logout');
  const location = useLocation();
  const [collapsed, setCollapsed] = useState(false);
  const [expandedCategories, setExpandedCategories] = useState<string[]>(['atencion-medica']);

  // Categorías del menú
  const menuCategories = [
    {
      id: 'principal',
      label: 'Principal',
      icon: faHospital,
      items: [
        { path: '/dashboard', label: 'Dashboard', icon: 'home' },
        { path: '/pacientes', label: 'Pacientes', icon: 'user' },
        { path: '/citas', label: 'Citas', icon: 'calendar' },
      ]
    },
    {
      id: 'atencion-medica',
      label: 'Atención Médica',
      icon: faUserMd,
      items: [
        { path: '/consultas', label: 'Consultas', icon: 'stethoscope' },
        { path: '/urgencias', label: 'Urgencias', icon: 'ambulance' },
        { path: '/ambulancias', label: 'Ambulancias', icon: 'ambulance' },
        { 
          path: '/quirofanos', 
          label: 'Quirófanos', 
          icon: 'procedures',
          subitems: [
            { path: '/quirofanos/cirugias', label: 'Cirugías', icon: 'scissors' }
          ]
        },
        { path: '/hospitalizaciones', label: 'Hospitalizaciones', icon: 'bed' },
        { path: '/historias-clinicas', label: 'Historias Clínicas', icon: 'clipboard' },
        { path: '/teleconsultas', label: 'Teleconsultas', icon: 'video' },
      ]
    },
    {
      id: 'farmacia-inventario',
      label: 'Farmacia e Inventario',
      icon: faMedkit,
      items: [
        { path: '/medicamentos', label: 'Medicamentos', icon: 'medkit' },
        { path: '/dispensaciones', label: 'Dispensaciones', icon: 'pills' },
        { path: '/activos', label: 'Activos', icon: 'boxes' },
        { path: '/inventario', label: 'Inventario General', icon: 'warehouse' },
      ]
    },
    {
      id: 'gestion',
      label: 'Gestión',
      icon: faCog,
      items: [
        { path: '/facturacion', label: 'Facturación', icon: 'file-invoice' },
        { path: '/contabilidad', label: 'Contabilidad', icon: 'file-invoice-dollar' },
        { path: '/presupuesto', label: 'Presupuesto', icon: 'money-bill' },
        { path: '/reportes', label: 'Reportes', icon: 'chart-bar' },
        { path: '/analitica-predictiva', label: 'Analítica Predictiva', icon: 'chart-line' },
        { path: '/residuos-hospitalarios', label: 'Residuos Hospitalarios', icon: 'recycle' },
        { path: '/proveedores', label: 'Proveedores', icon: 'truck' },
        { path: '/recursos-humanos', label: 'Recursos Humanos', icon: 'users' },
        { path: '/autorizaciones', label: 'Autorizaciones', icon: 'check-circle' },
        { path: '/incidentes-adversos', label: 'Incidentes Adversos', icon: 'exclamation-triangle' },
      ]
    },
    {
      id: 'cie11',
      label: 'CIE-11',
      icon: faDatabase,
      items: [
        { path: '/configuraciones/cie11', label: 'Configuración CIE-11', icon: 'sync' },
        { path: '/reportes/cie11', label: 'Estadísticas CIE-11', icon: 'chart-pie' },
      ]
    },
    {
      id: 'admin',
      label: 'Administración',
      icon: faCog,
      items: [
        { path: '/admin/users', label: 'Usuarios', icon: 'users' },
        { path: '/admin/roles', label: 'Roles', icon: 'shield' },
        { path: '/admin/settings', label: 'Configuración', icon: 'settings' },
      ]
    },
  ];

  // Función para expandir/colapsar categorías
  const toggleCategory = (categoryId: string) => {
    setExpandedCategories(prev =>
      prev.includes(categoryId)
        ? prev.filter(id => id !== categoryId)
        : [...prev, categoryId]
    );
  };

  const getIcon = (iconName: string) => {
    switch (iconName) {
      case 'home':
        return (
          <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6"></path>
          </svg>
        );
      case 'users':
        return (
          <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197M13 7a4 4 0 11-8 0 4 4 0 018 0z"></path>
          </svg>
        );
      case 'shield':
        return (
          <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z"></path>
          </svg>
        );
      case 'settings':
        return (
          <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"></path>
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
          </svg>
        );
      case 'user':
        return (
          <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
          </svg>
        );
      case 'calendar':
        return (
          <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
          </svg>
        );
      case 'file-invoice':
        return (
          <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
          </svg>
        );
      case 'clipboard':
        return (
          <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2"></path>
          </svg>
        );
      case 'boxes':
        return <FontAwesomeIcon icon={faBoxes} className="w-5 h-5" />;
      case 'warehouse':
        return <FontAwesomeIcon icon={faWarehouse} className="w-5 h-5" />;
      case 'video':
        return (
          <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M15 10l4.553-2.276A1 1 0 0121 8.618v6.764a1 1 0 01-1.447.894L15 14M5 18h8a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v8a2 2 0 002 2z"></path>
          </svg>
        );
      case 'stethoscope':
        return <FontAwesomeIcon icon={faStethoscope} className="w-5 h-5" />;
      case 'ambulance':
        return <FontAwesomeIcon icon={faAmbulance} className="w-5 h-5" />;
      case 'scalpel':
        return <FontAwesomeIcon icon={faProcedures} className="w-5 h-5" />;
      case 'medical-equipment':
        return <FontAwesomeIcon icon={faStethoscopeIcon} className="w-5 h-5" />;
      case 'medkit':
        return <FontAwesomeIcon icon={faMedkit} className="w-5 h-5" />;
      case 'pills':
        return <FontAwesomeIcon icon={faPills} className="w-5 h-5" />;
      case 'bed':
        return <FontAwesomeIcon icon={faBed} className="w-5 h-5" />;
      case 'money-bill':
        return <FontAwesomeIcon icon={faMoneyBill} className="w-5 h-5" />;
      case 'chart-bar':
        return <FontAwesomeIcon icon={faChartBar} className="w-5 h-5" />;
      case 'chart-line':
        return <FontAwesomeIcon icon={faChartLine} className="w-5 h-5" />;
      case 'recycle':
        return <FontAwesomeIcon icon={faRecycle} className="w-5 h-5" />;
      case 'sync':
        return <FontAwesomeIcon icon={faSync} className="w-5 h-5" />;
      case 'chart-pie':
        return <FontAwesomeIcon icon={faChartPie} className="w-5 h-5" />;
      case 'truck':
        return <FontAwesomeIcon icon={faTruck} className="w-5 h-5" />;
      case 'file-invoice-dollar':
        return (
          <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M15 5v2m0 4v2m0 4v2M5 5a2 2 0 00-2 2v3a2 2 0 110 4v3a2 2 0 002 2h14a2 2 0 002-2v-3a2 2 0 110-4V7a2 2 0 00-2-2H5z"></path>
          </svg>
        );
      case 'check-circle':
        return <FontAwesomeIcon icon={faCheckCircle} className="w-5 h-5" />;
      case 'exclamation-triangle':
        return <FontAwesomeIcon icon={faExclamationTriangle} className="w-5 h-5" />;
      case 'scissors':
        return <FontAwesomeIcon icon={faScissors} className="w-5 h-5" />;
      case 'procedures':
        return <FontAwesomeIcon icon={faProcedures} className="w-5 h-5" />;
      default:
        return null;
    }
  };

  return (
    <div
      className={`glass-sidebar fixed top-16 left-0 bottom-0 transition-all duration-300 overflow-hidden z-40 flex flex-col ${
        collapsed ? 'w-16' : 'w-64'
      }`}
      style={glassStyle}
    >
      <div className="flex items-center justify-between p-4 border-b border-white/10 dark:border-black/10 flex-shrink-0">
        {!collapsed && (
          <div className="flex items-center space-x-3">
            <div className="w-10 h-10 bg-gradient-to-br from-blue-500 via-purple-600 to-indigo-700 rounded-xl flex items-center justify-center shadow-lg">
              <span className="text-white font-bold text-lg">H</span>
            </div>
            <div>
              <h1 className="text-adaptive text-lg font-bold tracking-tight">Hipócrates</h1>
              <p className="text-adaptive-subtle text-xs">Sistema Integral</p>
            </div>
          </div>
        )}
        {collapsed && (
          <div className="w-10 h-10 bg-gradient-to-br from-blue-500 via-purple-600 to-indigo-700 rounded-xl flex items-center justify-center shadow-lg mx-auto">
            <span className="text-white font-bold text-lg">H</span>
          </div>
        )}
        <button
          onClick={() => setCollapsed(!collapsed)}
          className="glass-card p-2 rounded-lg text-adaptive-muted hover:text-adaptive focus:outline-none transition-all duration-200 transform hover:scale-105"
        >
          {collapsed ? (
            <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M13 5l7 7-7 7M5 5l7 7-7 7"></path>
            </svg>
          ) : (
            <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M11 19l-7-7 7-7m8 14l-7-7 7-7"></path>
            </svg>
          )}
        </button>
      </div>

      <div className="flex-1 py-4 overflow-y-auto custom-scrollbar">
        <ul className="space-y-2 px-3">
          {menuCategories.map((category) => (
            <li key={category.id} className="mb-3">
              <button
                onClick={() => toggleCategory(category.id)}
                className={`w-full flex items-center justify-between py-3 px-4 rounded-xl glass-card hover:shadow-lg transition-all duration-300 transform hover:scale-[1.02] text-adaptive`}
              >
                <div className="flex items-center space-x-3">
                  <div className="w-8 h-8 rounded-lg bg-gradient-to-br from-blue-500/20 to-purple-600/20 flex items-center justify-center">
                    <FontAwesomeIcon icon={category.icon} className="w-4 h-4 text-blue-500 dark:text-blue-400" />
                  </div>
                  {!collapsed && <span className="font-medium">{category.label}</span>}
                </div>
                {!collapsed && (
                  <FontAwesomeIcon
                    icon={expandedCategories.includes(category.id) ? faChevronDown : faChevronRight}
                    className={`w-3 h-3 text-adaptive-muted transition-transform duration-200 ${
                      expandedCategories.includes(category.id) ? 'rotate-0' : 'rotate-0'
                    }`}
                  />
                )}
              </button>

              {(expandedCategories.includes(category.id) || collapsed) && (
                <ul className={`${collapsed ? '' : 'ml-2'} mt-2 space-y-1`}>
                  {category.items.map((item) => (
                    <li key={item.path}>
                      {/* Si el item tiene subitems, lo manejamos diferente */}
                      {item.subitems ? (
                        <div>
                          <Link
                            to={item.path}
                            className={`flex items-center py-2.5 px-3 rounded-lg ${collapsed ? 'justify-center' : ''} ${
                              location.pathname === item.path
                                ? 'bg-gradient-to-r from-blue-500/20 to-purple-600/20 text-blue-600 dark:text-blue-400 shadow-lg'
                                : 'text-adaptive-muted hover:text-adaptive hover:bg-white/5 dark:hover:bg-black/5'
                            } transition-all duration-200 transform hover:scale-[1.02]`}
                          >
                            <div className="w-6 h-6 rounded-md bg-gradient-to-br from-gray-500/10 to-gray-600/10 flex items-center justify-center mr-3">
                              {getIcon(item.icon)}
                            </div>
                            {!collapsed && <span className="text-sm font-medium">{item.label}</span>}
                            {!collapsed && item.subitems && (
                              <FontAwesomeIcon
                                icon={location.pathname.startsWith(item.path) ? faChevronDown : faChevronRight}
                                className="w-3 h-3 ml-auto text-adaptive-subtle"
                              />
                            )}
                          </Link>
                          
                          {/* Renderizar subitems si estamos en la ruta principal */}
                          {location.pathname.startsWith(item.path) && item.subitems && (
                            <ul className="pl-6 mt-2 space-y-1">
                              {item.subitems.map(subitem => (
                                <li key={subitem.path}>
                                  <Link
                                    to={subitem.path}
                                    className={`flex items-center py-2 px-3 rounded-lg ${collapsed ? 'justify-center' : ''} ${
                                      location.pathname === subitem.path || location.pathname.startsWith(`${subitem.path}/`)
                                        ? 'bg-gradient-to-r from-blue-500/15 to-purple-600/15 text-blue-600 dark:text-blue-400'
                                        : 'text-adaptive-subtle hover:text-adaptive hover:bg-white/5 dark:hover:bg-black/5'
                                    } transition-all duration-200 transform hover:scale-[1.02]`}
                                  >
                                    <div className="w-5 h-5 rounded-md bg-gradient-to-br from-gray-500/10 to-gray-600/10 flex items-center justify-center mr-3">
                                      {getIcon(subitem.icon)}
                                    </div>
                                    {!collapsed && <span className="text-xs font-medium">{subitem.label}</span>}
                                  </Link>
                                </li>
                              ))}
                            </ul>
                          )}
                        </div>
                      ) : (
                        <Link
                          to={item.path}
                          className={`flex items-center py-2.5 px-3 rounded-lg ${collapsed ? 'justify-center' : ''} ${
                            location.pathname === item.path || location.pathname.startsWith(`${item.path}/`)
                              ? 'bg-gradient-to-r from-blue-500/20 to-purple-600/20 text-blue-600 dark:text-blue-400 shadow-lg'
                              : 'text-adaptive-muted hover:text-adaptive hover:bg-white/5 dark:hover:bg-black/5'
                          } transition-all duration-200 transform hover:scale-[1.02]`}
                        >
                          <div className="w-6 h-6 rounded-md bg-gradient-to-br from-gray-500/10 to-gray-600/10 flex items-center justify-center mr-3">
                            {getIcon(item.icon)}
                          </div>
                          {!collapsed && <span className="text-sm font-medium">{item.label}</span>}
                        </Link>
                      )}
                    </li>
                  ))}
                </ul>
              )}
            </li>
          ))}
        </ul>
      </div>

      <div className="border-t border-white/10 dark:border-black/10 p-4 flex-shrink-0">
        {!collapsed && (
          <div className="glass-card p-3 rounded-xl mb-4">
            <div className="flex items-center space-x-3">
              <div className="w-10 h-10 rounded-full bg-gradient-to-br from-blue-500 via-purple-600 to-indigo-700 flex items-center justify-center text-white font-bold shadow-lg">
                {user?.username.charAt(0).toUpperCase()}
              </div>
              <div className="flex-1">
                <p className="text-adaptive font-semibold text-sm">{user?.username}</p>
                <p className="text-adaptive-subtle text-xs">Hospital ID: {user?.hospital_id}</p>
                <div className="flex items-center space-x-1 mt-1">
                  <div className="w-2 h-2 bg-green-400 rounded-full animate-pulse"></div>
                  <span className="text-adaptive-subtle text-xs">En línea</span>
                </div>
              </div>
            </div>
          </div>
        )}
        <button
          onClick={logout}
          className="glass-card flex items-center w-full py-3 px-4 rounded-xl hover:shadow-lg transition-all duration-300 transform hover:scale-[1.02] text-red-400 hover:text-red-300"
        >
          <div className="w-6 h-6 rounded-lg bg-red-500/10 flex items-center justify-center mr-3">
            <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1"></path>
            </svg>
          </div>
          {!collapsed && <span className="text-sm font-medium">Cerrar sesión</span>}
        </button>
      </div>
    </div>
  );
};
