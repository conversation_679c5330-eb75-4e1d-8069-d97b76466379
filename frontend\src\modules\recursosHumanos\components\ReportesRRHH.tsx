import React from 'react';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faFileInvoiceDollar, faDownload } from '@fortawesome/free-solid-svg-icons';
import { Button } from '../../../components/ui/Button';

const ReportesRRHH: React.FC = () => {
  return (
    <div className="space-y-6">
      <div className="bg-card p-6 rounded-lg shadow-lg">
        <div className="flex justify-between items-center">
          <div>
            <h2 className="text-2xl font-bold text-primary flex items-center">
              <FontAwesomeIcon icon={faFileInvoiceDollar} className="mr-2" />
              Reportes de RRHH
            </h2>
            <p className="text-muted mt-1">
              Reportes y análisis de recursos humanos
            </p>
          </div>
          <Button>
            <FontAwesomeIcon icon={faDownload} className="mr-2" />
            Generar Reporte
          </Button>
        </div>
      </div>
      
      <div className="bg-card p-6 rounded-lg shadow-lg">
        <p className="text-center text-muted">Componente en desarrollo...</p>
      </div>
    </div>
  );
};

export default ReportesRRHH;
