export interface Empleado {
  id: number;
  hospital_id: number;
  tipo_documento: string;
  numero_documento: string;
  nombres: string;
  apellidos: string;
  fecha_nacimiento: string;
  genero: 'M' | 'F' | 'O';
  direccion: string;
  telefono: string;
  email: string;
  departamento_id: number;
  departamento_nombre?: string;
  cargo_id: number;
  cargo_nombre?: string;
  fecha_ingreso: string;
  fecha_salida?: string;
  estado: 'Activo' | 'Inactivo' | 'Vacaciones' | 'Licencia';
  tipo_contrato: 'Indefinido' | 'Fijo' | 'Prestación de Servicios' | 'Aprendizaje';
  salario_base: number;
  eps: string;
  arl: string;
  fondo_pension: string;
  cuenta_bancaria?: string;
  banco?: string;
  observaciones?: string;
  created_at: string;
  updated_at: string;
}

export interface EmpleadoFormData {
  hospital_id: number;
  tipo_documento: string;
  numero_documento: string;
  nombres: string;
  apellidos: string;
  fecha_nacimiento: string;
  genero: 'M' | 'F' | 'O';
  direccion: string;
  telefono: string;
  email: string;
  departamento_id: number;
  cargo_id: number;
  fecha_ingreso: string;
  fecha_salida?: string;
  estado: 'Activo' | 'Inactivo' | 'Vacaciones' | 'Licencia';
  tipo_contrato: 'Indefinido' | 'Fijo' | 'Prestación de Servicios' | 'Aprendizaje';
  salario_base: number;
  eps: string;
  arl: string;
  fondo_pension: string;
  cuenta_bancaria?: string;
  banco?: string;
  observaciones?: string;
}

export interface Departamento {
  id: number;
  hospital_id: number;
  nombre: string;
  descripcion?: string;
  responsable_id?: number;
  responsable_nombre?: string;
  created_at: string;
  updated_at: string;
}

export interface DepartamentoFormData {
  hospital_id: number;
  nombre: string;
  descripcion?: string;
  responsable_id?: number;
}

export interface Cargo {
  id: number;
  hospital_id: number;
  nombre: string;
  descripcion?: string;
  departamento_id: number;
  departamento_nombre?: string;
  salario_minimo?: number;
  salario_maximo?: number;
  created_at: string;
  updated_at: string;
}

export interface CargoFormData {
  hospital_id: number;
  nombre: string;
  descripcion?: string;
  departamento_id: number;
  salario_minimo?: number;
  salario_maximo?: number;
}

export interface Nomina {
  id: number;
  hospital_id: number;
  periodo: string;
  fecha_inicio: string;
  fecha_fin: string;
  fecha_pago: string;
  estado: 'Borrador' | 'Calculada' | 'Aprobada' | 'Pagada';
  total_bruto: number;
  total_deducciones: number;
  total_neto: number;
  observaciones?: string;
  created_at: string;
  updated_at: string;
}

export interface NominaFormData {
  hospital_id: number;
  periodo: string;
  fecha_inicio: string;
  fecha_fin: string;
  fecha_pago: string;
  observaciones?: string;
}

export interface DetalleNomina {
  id: number;
  nomina_id: number;
  empleado_id: number;
  empleado_nombre?: string;
  salario_base: number;
  dias_trabajados: number;
  horas_extra: number;
  valor_horas_extra: number;
  incapacidades: number;
  valor_incapacidades: number;
  otros_ingresos: number;
  total_devengado: number;
  salud: number;
  pension: number;
  retencion: number;
  otras_deducciones: number;
  total_deducciones: number;
  neto_pagar: number;
  observaciones?: string;
  created_at: string;
  updated_at: string;
}

export interface DetalleNominaFormData {
  empleado_id: number;
  dias_trabajados: number;
  horas_extra: number;
  incapacidades: number;
  otros_ingresos: number;
  otras_deducciones: number;
  observaciones?: string;
}

export interface ResumenRecursosHumanos {
  total_empleados: number;
  empleados_activos: number;
  empleados_inactivos: number;
  empleados_vacaciones: number;
  empleados_licencia: number;
  por_departamento: {
    [key: string]: number;
  };
  por_cargo: {
    [key: string]: number;
  };
  por_tipo_contrato: {
    [key: string]: number;
  };
  nomina_actual?: {
    periodo: string;
    estado: string;
    total_bruto: number;
    total_neto: number;
  };
}

export interface Contrato {
  id: number;
  hospital_id: number;
  empleado_id: number;
  empleado_nombre?: string;
  tipo_contrato: 'Indefinido' | 'Fijo' | 'Prestación de Servicios' | 'Aprendizaje';
  fecha_inicio: string;
  fecha_fin?: string;
  salario_base: number;
  estado: 'Activo' | 'Finalizado' | 'Renovado' | 'Cancelado';
  documento_url?: string;
  terminos_condiciones?: string;
  observaciones?: string;
  created_at: string;
  updated_at: string;
}

export interface ContratoFormData {
  hospital_id: number;
  empleado_id: number;
  tipo_contrato: 'Indefinido' | 'Fijo' | 'Prestación de Servicios' | 'Aprendizaje';
  fecha_inicio: string;
  fecha_fin?: string;
  salario_base: number;
  estado: 'Activo' | 'Finalizado' | 'Renovado' | 'Cancelado';
  documento_url?: string;
  terminos_condiciones?: string;
  observaciones?: string;
}

export interface Vacaciones {
  id: number;
  hospital_id: number;
  empleado_id: number;
  empleado_nombre?: string;
  fecha_solicitud: string;
  fecha_inicio: string;
  fecha_fin: string;
  dias_totales: number;
  estado: 'Solicitada' | 'Aprobada' | 'Rechazada' | 'Cancelada' | 'Completada';
  aprobador_id?: number;
  aprobador_nombre?: string;
  fecha_aprobacion?: string;
  observaciones?: string;
  created_at: string;
  updated_at: string;
}

export interface VacacionesFormData {
  hospital_id: number;
  empleado_id: number;
  fecha_solicitud: string;
  fecha_inicio: string;
  fecha_fin: string;
  dias_totales: number;
  estado: 'Solicitada' | 'Aprobada' | 'Rechazada' | 'Cancelada' | 'Completada';
  aprobador_id?: number;
  aprobador_nombre?: string;
  fecha_aprobacion?: string;
  observaciones?: string;
}

export interface Permiso {
  id: number;
  hospital_id: number;
  empleado_id: number;
  empleado_nombre?: string;
  tipo_permiso: 'Enfermedad' | 'Calamidad Doméstica' | 'Licencia Remunerada' | 'Licencia No Remunerada' | 'Estudio' | 'Otro';
  fecha_solicitud: string;
  fecha_inicio: string;
  fecha_fin: string;
  dias_totales: number;
  estado: 'Solicitado' | 'Aprobado' | 'Rechazado' | 'Cancelado' | 'Completado';
  aprobador_id?: number;
  aprobador_nombre?: string;
  fecha_aprobacion?: string;
  documento_soporte?: string;
  observaciones?: string;
  created_at: string;
  updated_at: string;
}

export interface PermisoFormData {
  hospital_id: number;
  empleado_id: number;
  tipo_permiso: 'Enfermedad' | 'Calamidad Doméstica' | 'Licencia Remunerada' | 'Licencia No Remunerada' | 'Estudio' | 'Otro';
  fecha_solicitud: string;
  fecha_inicio: string;
  fecha_fin: string;
  dias_totales: number;
  estado: 'Solicitado' | 'Aprobado' | 'Rechazado' | 'Cancelado' | 'Completado';
  aprobador_id?: number;
  aprobador_nombre?: string;
  fecha_aprobacion?: string;
  documento_soporte?: string;
  observaciones?: string;
}

export interface Turno {
  id: number;
  hospital_id: number;
  profesional_id: number;
  profesional_nombre?: string;
  fecha_inicio: string;
  fecha_fin: string;
  tipo_turno: 'Diurno' | 'Nocturno' | 'Mixto' | 'Extra';
  horas: number;
  estado: 'Programado' | 'En Curso' | 'Finalizado';
  created_at: string;
}

export interface TurnoFormData {
  hospital_id: number;
  profesional_id: number;
  fecha_inicio: string;
  fecha_fin: string;
  tipo_turno: 'Diurno' | 'Nocturno' | 'Mixto' | 'Extra';
  horas: number;
  estado: 'Programado' | 'En Curso' | 'Finalizado';
}

// Incapacidades
export interface Incapacidad {
  id: number;
  hospital_id: number;
  empleado_id: number;
  empleado_nombre?: string;
  tipo_incapacidad: 'Enfermedad General' | 'Accidente Trabajo' | 'Enfermedad Profesional' | 'Maternidad' | 'Paternidad';
  fecha_inicio: string;
  fecha_fin: string;
  dias_incapacidad: number;
  entidad_emisora: 'EPS' | 'ARL' | 'Medicina Laboral';
  numero_incapacidad: string;
  diagnostico: string;
  porcentaje_pago: number;
  valor_dia: number;
  valor_total: number;
  estado: 'Activa' | 'Finalizada' | 'Cancelada';
  observaciones?: string;
  archivo_soporte?: string;
  created_at: string;
  updated_at: string;
}

export interface IncapacidadFormData {
  hospital_id: number;
  empleado_id: number;
  tipo_incapacidad: 'Enfermedad General' | 'Accidente Trabajo' | 'Enfermedad Profesional' | 'Maternidad' | 'Paternidad';
  fecha_inicio: string;
  fecha_fin: string;
  entidad_emisora: 'EPS' | 'ARL' | 'Medicina Laboral';
  numero_incapacidad: string;
  diagnostico: string;
  porcentaje_pago: number;
  observaciones?: string;
  archivo_soporte?: string;
}

// Liquidación de Nómina
export interface LiquidacionNomina {
  id: number;
  hospital_id: number;
  empleado_id: number;
  empleado_nombre?: string;
  periodo: string;
  salario_base: number;
  horas_extras: number;
  valor_horas_extras: number;
  bonificaciones: number;
  auxilio_transporte: number;
  otros_ingresos: number;
  total_devengado: number;
  salud_empleado: number;
  pension_empleado: number;
  fondo_solidaridad: number;
  retencion_fuente: number;
  prestamos: number;
  otros_descuentos: number;
  total_descuentos: number;
  neto_pagar: number;
  salud_empleador: number;
  pension_empleador: number;
  arl: number;
  caja_compensacion: number;
  icbf: number;
  sena: number;
  total_aportes_empleador: number;
  estado: 'Borrador' | 'Calculada' | 'Aprobada' | 'Pagada';
  fecha_pago?: string;
  created_at: string;
  updated_at: string;
}

// Horas Extras
export interface HoraExtra {
  id: number;
  hospital_id: number;
  empleado_id: number;
  empleado_nombre?: string;
  fecha: string;
  hora_inicio: string;
  hora_fin: string;
  total_horas: number;
  tipo_hora: 'Diurna' | 'Nocturna' | 'Dominical' | 'Festiva';
  factor_multiplicador: number;
  valor_hora: number;
  valor_total: number;
  motivo: string;
  aprobado_por?: number;
  aprobado_por_nombre?: string;
  estado: 'Pendiente' | 'Aprobada' | 'Rechazada' | 'Pagada';
  observaciones?: string;
  created_at: string;
  updated_at: string;
}

export interface HoraExtraFormData {
  hospital_id: number;
  empleado_id: number;
  fecha: string;
  hora_inicio: string;
  hora_fin: string;
  tipo_hora: 'Diurna' | 'Nocturna' | 'Dominical' | 'Festiva';
  motivo: string;
  observaciones?: string;
}

// Prestaciones Sociales
export interface PrestacionSocial {
  id: number;
  hospital_id: number;
  empleado_id: number;
  empleado_nombre?: string;
  año: number;
  prima_servicios: number;
  cesantias: number;
  intereses_cesantias: number;
  vacaciones: number;
  total_prestaciones: number;
  fecha_calculo: string;
  estado: 'Calculada' | 'Pagada' | 'Pendiente';
  observaciones?: string;
  created_at: string;
  updated_at: string;
}

// Dotaciones
export interface Dotacion {
  id: number;
  hospital_id: number;
  empleado_id: number;
  empleado_nombre?: string;
  tipo_dotacion: 'Uniforme' | 'Calzado' | 'EPP' | 'Herramientas' | 'Tecnología';
  descripcion: string;
  cantidad: number;
  valor_unitario: number;
  valor_total: number;
  fecha_entrega: string;
  fecha_devolucion?: string;
  estado: 'Entregada' | 'En Uso' | 'Devuelta' | 'Perdida' | 'Dañada';
  observaciones?: string;
  created_at: string;
  updated_at: string;
}

export interface DotacionFormData {
  hospital_id: number;
  empleado_id: number;
  tipo_dotacion: 'Uniforme' | 'Calzado' | 'EPP' | 'Herramientas' | 'Tecnología';
  descripcion: string;
  cantidad: number;
  valor_unitario: number;
  fecha_entrega: string;
  observaciones?: string;
}

export interface Capacitacion {
  id: number;
  hospital_id: number;
  profesional_id: number;
  profesional_nombre?: string;
  nombre_curso: string;
  institucion?: string;
  fecha_inicio: string;
  fecha_fin: string;
  certificado?: string;
  created_at: string;
}

export interface CapacitacionFormData {
  hospital_id: number;
  profesional_id: number;
  nombre_curso: string;
  institucion?: string;
  fecha_inicio: string;
  fecha_fin: string;
  certificado?: string;
}

export interface EvaluacionDesempeno {
  id: number;
  hospital_id: number;
  profesional_id: number;
  profesional_nombre?: string;
  fecha_evaluacion: string;
  calificacion: number;
  comentarios?: string;
  created_at: string;
}

export interface EvaluacionDesempenoFormData {
  hospital_id: number;
  profesional_id: number;
  fecha_evaluacion: string;
  calificacion: number;
  comentarios?: string;
}
