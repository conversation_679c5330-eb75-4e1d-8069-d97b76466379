import{createContext as r,use<PERSON>ontext as o,use<PERSON>emo as u}from"react";import{MenuMachine as c}from'./menu-machine.js';const a=r(null);function l(e){let n=o(a);if(n===null){let t=new Error(`<${e} /> is missing a parent <Menu /> component.`);throw Error.captureStackTrace&&Error.captureStackTrace(t,i),t}return n}function i({__demoMode:e=!1}={}){return u(()=>c.new({__demoMode:e}),[])}export{a as MenuContext,i as useMenuMachine,l as useMenuMachineContext};
