import { useState } from 'react';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { Button } from '../../components/ui/Button';
import {
  faUsers,
  faBriefcase,
  faSitemap,
  faMoneyBill,
  faFileContract,
  faPlane,
  faCalendarCheck,
  faSearch,
  faPlus,
  faClock,
  faGraduationCap,
  faStarHalfAlt,
  faEdit,
  faTrash
} from '@fortawesome/free-solid-svg-icons';
import * as recursosHumanosService from '../../services/recursosHumanosService';
import { useQuery, useQueryClient } from '@tanstack/react-query';
import { useAuth } from '../../services/authService';
import EmpleadoModal from './components/EmpleadoModal';
import DepartamentoModal from './components/DepartamentoModal';
import CargoModal from './components/CargoModal';
import NominaModal from './components/NominaModal';
import ContratoModal from './components/ContratoModal';
import VacacionesModal from './components/VacacionesModal';
import PermisoModal from './components/PermisoModal';
import TurnoModal from './components/TurnoModal';
import CapacitacionModal from './components/CapacitacionModal';
import EvaluacionDesempenoModal from './components/EvaluacionDesempenoModal';
import TabTurnos from './components/TabTurnos';
import TabCapacitaciones from './components/TabCapacitaciones';
import TabEvaluaciones from './components/TabEvaluaciones';
import { toast } from 'react-hot-toast';

export default function RRHH() {
  const [activeTab, setActiveTab] = useState('empleados');
  const { user } = useAuth();
  const hospitalId = user?.hospital_id || 1;
  const [searchTerm, setSearchTerm] = useState('');
  const [filterEstado, setFilterEstado] = useState('');
  const queryClient = useQueryClient();

  const [showEmpleadoModal, setShowEmpleadoModal] = useState(false);
  const [showDepartamentoModal, setShowDepartamentoModal] = useState(false);
  const [showCargoModal, setShowCargoModal] = useState(false);
  const [showNominaModal, setShowNominaModal] = useState(false);
  const [showContratoModal, setShowContratoModal] = useState(false);
  const [showVacacionesModal, setShowVacacionesModal] = useState(false);
  const [showPermisoModal, setShowPermisoModal] = useState(false);
  const [showTurnoModal, setShowTurnoModal] = useState(false);
  const [showCapacitacionModal, setShowCapacitacionModal] = useState(false);
  const [showEvaluacionModal, setShowEvaluacionModal] = useState(false);

  const [selectedEmpleado, setSelectedEmpleado] = useState<any>(null);
  const [selectedDepartamento, setSelectedDepartamento] = useState<any>(null);
  const [selectedCargo, setSelectedCargo] = useState<any>(null);
  const [selectedNomina, setSelectedNomina] = useState<any>(null);
  const [selectedContrato, setSelectedContrato] = useState<any>(null);
  const [selectedVacaciones, setSelectedVacaciones] = useState<any>(null);
  const [selectedPermiso, setSelectedPermiso] = useState<any>(null);
  const [selectedTurno, setSelectedTurno] = useState<any>(null);
  const [selectedCapacitacion, setSelectedCapacitacion] = useState<any>(null);
  const [selectedEvaluacion, setSelectedEvaluacion] = useState<any>(null);

  const { data: empleados, isLoading: isLoadingEmpleados, error: errorEmpleados } = useQuery({
    queryKey: ['empleados', hospitalId],
    queryFn: () => recursosHumanosService.getEmpleados(hospitalId),
    enabled: !!hospitalId,
  });

  const { data: departamentos, isLoading: isLoadingDepartamentos, error: errorDepartamentos } = useQuery({
    queryKey: ['departamentos', hospitalId],
    queryFn: () => recursosHumanosService.getDepartamentos(hospitalId),
    enabled: !!hospitalId,
  });

  const { data: cargos, isLoading: isLoadingCargos, error: errorCargos } = useQuery({
    queryKey: ['cargos', hospitalId],
    queryFn: () => recursosHumanosService.getCargos(hospitalId),
    enabled: !!hospitalId,
  });

  const { data: nominas, isLoading: isLoadingNominas, error: errorNominas } = useQuery({
    queryKey: ['nominas', hospitalId],
    queryFn: () => recursosHumanosService.getNominas(hospitalId),
    enabled: !!hospitalId,
  });

  const { data: contratos, isLoading: isLoadingContratos, error: errorContratos } = useQuery({
    queryKey: ['contratos', hospitalId],
    queryFn: () => recursosHumanosService.getContratos(hospitalId),
    enabled: !!hospitalId,
  });

  const { data: vacaciones, isLoading: isLoadingVacaciones, error: errorVacaciones } = useQuery({
    queryKey: ['vacaciones', hospitalId],
    queryFn: () => recursosHumanosService.getVacaciones(hospitalId),
    enabled: !!hospitalId,
  });

  const { data: permisos, isLoading: isLoadingPermisos, error: errorPermisos } = useQuery({
    queryKey: ['permisos', hospitalId],
    queryFn: () => recursosHumanosService.getPermisos(hospitalId),
    enabled: !!hospitalId,
  });

  const { data: turnos, isLoading: isLoadingTurnos, error: errorTurnos } = useQuery({
    queryKey: ['turnos', hospitalId],
    queryFn: () => recursosHumanosService.getTurnos(hospitalId),
    enabled: !!hospitalId,
  });

  const { data: capacitaciones, isLoading: isLoadingCapacitaciones, error: errorCapacitaciones } = useQuery({
    queryKey: ['capacitaciones', hospitalId],
    queryFn: () => recursosHumanosService.getCapacitaciones(hospitalId),
    enabled: !!hospitalId,
  });

  const { data: evaluaciones, isLoading: isLoadingEvaluaciones, error: errorEvaluaciones } = useQuery({
    queryKey: ['evaluaciones', hospitalId],
    queryFn: () => recursosHumanosService.getEvaluacionesDesempeno(hospitalId),
    enabled: !!hospitalId,
  });

  const { data: resumenRRHH, isLoading: isLoadingResumenRRHH, error: errorResumenRRHH } = useQuery({
    queryKey: ['resumenRRHH', hospitalId],
    queryFn: () => recursosHumanosService.getResumenRecursosHumanos(hospitalId),
    enabled: !!hospitalId,
  });

  const handleOpenTurnoModal = (turno?: any) => {
    setSelectedTurno(turno);
    setShowTurnoModal(true);
  };

  const handleDeleteTurno = (id: number) => {
    if (window.confirm('¿Está seguro de eliminar este turno?')) {
      recursosHumanosService.deleteTurno(id)
        .then(() => {
          toast.success('Turno eliminado exitosamente.');
          queryClient.invalidateQueries({ queryKey: ['turnos', hospitalId] });
        })
        .catch(error => {
          toast.error('Error al eliminar turno.');
          console.error('Error al eliminar turno:', error)
        });
    }
  };

  const handleOpenCapacitacionModal = (capacitacion?: any) => {
    setSelectedCapacitacion(capacitacion);
    setShowCapacitacionModal(true);
  };

  const handleDeleteCapacitacion = (id: number) => {
    if (window.confirm('¿Está seguro de eliminar esta capacitación?')) {
      recursosHumanosService.deleteCapacitacion(id)
        .then(() => {
          toast.success('Capacitación eliminada exitosamente.');
          queryClient.invalidateQueries({ queryKey: ['capacitaciones', hospitalId] });
        })
        .catch(error => {
          toast.error('Error al eliminar capacitación.');
          console.error('Error al eliminar capacitación:', error)
        });
    }
  };

  const handleOpenEvaluacionModal = (evaluacion?: any) => {
    setSelectedEvaluacion(evaluacion);
    setShowEvaluacionModal(true);
  };

  const handleDeleteEvaluacion = (id: number) => {
    if (window.confirm('¿Está seguro de eliminar esta evaluación?')) {
      recursosHumanosService.deleteEvaluacionDesempeno(id)
        .then(() => {
          toast.success('Evaluación eliminada exitosamente.');
          queryClient.invalidateQueries({ queryKey: ['evaluaciones', hospitalId] });
        })
        .catch(error => {
          toast.error('Error al eliminar evaluación.');
          console.error('Error al eliminar evaluación:', error)
        });
    }
  };

  const handleDeleteEmpleado = async (id: number) => {
    if (window.confirm('¿Estás seguro de que deseas eliminar este empleado? Esta acción no se puede deshacer.')) {
      try {
        await recursosHumanosService.deleteEmpleado(id);
        toast.success('Empleado eliminado exitosamente.');
        queryClient.invalidateQueries({ queryKey: ['empleados', hospitalId] });
      } catch (error) {
        toast.error('Error al eliminar el empleado.');
        console.error("Error deleting empleado:", error);
      }
    }
  };

  if (isLoadingResumenRRHH || isLoadingEmpleados || isLoadingDepartamentos || isLoadingCargos || isLoadingNominas || isLoadingContratos || isLoadingVacaciones || isLoadingPermisos || isLoadingTurnos || isLoadingCapacitaciones || isLoadingEvaluaciones) {
    return <div className="flex justify-center items-center h-screen"><p className="text-white text-xl">Cargando datos de Recursos Humanos...</p></div>;
  }

  if (errorResumenRRHH || errorEmpleados || errorDepartamentos || errorCargos || errorNominas || errorContratos || errorVacaciones || errorPermisos || errorTurnos || errorCapacitaciones || errorEvaluaciones) {
    console.error("Errores de carga:", {
        errorResumenRRHH, errorEmpleados, errorDepartamentos, errorCargos, errorNominas, errorContratos, errorVacaciones, errorPermisos, errorTurnos, errorCapacitaciones, errorEvaluaciones
    });
    return <div className="flex justify-center items-center h-screen"><p className="text-red-500 text-xl">Error al cargar algunos datos. Por favor, intente más tarde.</p></div>;
  }

  const filteredEmpleados = (empleados || []).filter(empleado => {
    const nombreCompleto = `${empleado.nombres} ${empleado.apellidos}`.toLowerCase();
    const cargo = empleado.cargo_nombre?.toLowerCase() || '';
    const departamento = empleado.departamento_nombre?.toLowerCase() || '';
    const searchTermLower = searchTerm.toLowerCase();

    return (
      (nombreCompleto.includes(searchTermLower) ||
       cargo.includes(searchTermLower) ||
       departamento.includes(searchTermLower)) &&
      (filterEstado ? empleado.estado === filterEstado : true)
    );
  });

  return (
    <div className="p-6">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold text-white">Recursos Humanos</h1>
        <div className="flex space-x-2">
          <Button variant="outline" onClick={() => setActiveTab('departamentos')}>
            <FontAwesomeIcon icon={faSitemap} className="mr-2" />
            Departamentos
          </Button>
          <Button variant="outline" onClick={() => setActiveTab('cargos')}>
            <FontAwesomeIcon icon={faBriefcase} className="mr-2" />
            Cargos
          </Button>
          <Button variant="outline" onClick={() => setActiveTab('nominas')}>
            <FontAwesomeIcon icon={faMoneyBill} className="mr-2" />
            Nóminas
          </Button>
        </div>
      </div>

      {/* Resumen */}
      {!isLoadingResumenRRHH && resumenRRHH && (
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
          <div className="bg-gray-800 rounded-lg p-4">
            <h3 className="text-lg font-semibold text-white mb-2">Total Empleados</h3>
            <p className="text-2xl text-white">{resumenRRHH.total_empleados}</p>
          </div>
          <div className="bg-gray-800 rounded-lg p-4">
            <h3 className="text-lg font-semibold text-white mb-2">Activos</h3>
            <p className="text-2xl text-green-500">{resumenRRHH.empleados_activos}</p>
          </div>
          <div className="bg-gray-800 rounded-lg p-4">
            <h3 className="text-lg font-semibold text-white mb-2">Inactivos</h3>
            <p className="text-2xl text-red-500">{resumenRRHH.empleados_inactivos}</p>
          </div>
          <div className="bg-gray-800 rounded-lg p-4">
            <h3 className="text-lg font-semibold text-white mb-2">En Vacaciones/Licencia</h3>
            <p className="text-2xl text-yellow-500">{(resumenRRHH.empleados_vacaciones || 0) + (resumenRRHH.empleados_licencia || 0)}</p>
          </div>
        </div>
      )}

      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <div className="md:col-span-2">
          <div className="bg-gray-800 rounded-lg shadow-lg overflow-hidden">
            {/* Filtros */}
            <div className="p-4 border-b border-gray-700">
              <div className="flex flex-col md:flex-row gap-4">
                <div className="relative flex-1">
                  <input
                    type="text"
                    placeholder="Buscar empleado..."
                    className="w-full pl-10 pr-4 py-2 rounded-md bg-gray-700 text-white border border-gray-600 focus:outline-none focus:border-blue-500"
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                  />
                  <FontAwesomeIcon icon={faSearch} className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
                </div>
                <select
                  className="px-3 py-2 rounded-md bg-gray-700 text-white border border-gray-600 focus:outline-none focus:border-blue-500"
                  value={filterEstado}
                  onChange={(e) => setFilterEstado(e.target.value)}
                >
                  <option value="">Todos los estados</option>
                  <option value="Activo">Activo</option>
                  <option value="Inactivo">Inactivo</option>
                  <option value="Vacaciones">Vacaciones</option>
                  <option value="Licencia">Licencia</option>
                </select>
              </div>
            </div>

            {/* Tabla */}
            {isLoadingEmpleados ? (
              <p className="text-white text-center py-4">Cargando empleados...</p>
            ) : filteredEmpleados.length > 0 ? (
              <div className="overflow-x-auto">
                <table className="min-w-full divide-y divide-gray-700">
                  <thead className="bg-gray-700">
                    <tr>
                      <th className="px-4 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">ID</th>
                      <th className="px-4 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">Nombre Completo</th>
                      <th className="px-4 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">Cargo</th>
                      <th className="px-4 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">Departamento</th>
                      <th className="px-4 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">Estado</th>
                      <th className="px-4 py-3 text-right text-xs font-medium text-gray-300 uppercase tracking-wider">Acciones</th>
                    </tr>
                  </thead>
                  <tbody className="bg-gray-800 divide-y divide-gray-700">
                    {filteredEmpleados.map(empleado => (
                      <tr key={empleado.id} className="hover:bg-gray-700">
                        <td className="px-4 py-3 whitespace-nowrap text-sm text-white">{empleado.id}</td>
                        <td className="px-4 py-3 whitespace-nowrap text-sm text-white">{empleado.nombres} {empleado.apellidos}</td>
                        <td className="px-4 py-3 whitespace-nowrap text-sm text-white">{empleado.cargo_nombre}</td>
                        <td className="px-4 py-3 whitespace-nowrap text-sm text-white">{empleado.departamento_nombre}</td>
                        <td className="px-4 py-3 whitespace-nowrap text-sm">
                          <span
                            className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${
                              empleado.estado === 'Activo'
                                ? 'bg-green-100 text-green-800'
                                : empleado.estado === 'Inactivo'
                                ? 'bg-red-100 text-red-800'
                                : empleado.estado === 'Vacaciones'
                                ? 'bg-blue-100 text-blue-800'
                                : empleado.estado === 'Licencia'
                                ? 'bg-yellow-100 text-yellow-800'
                                : 'bg-gray-100 text-gray-800'
                            }`}
                          >
                            {empleado.estado}
                          </span>
                        </td>
                        <td className="px-4 py-3 whitespace-nowrap text-sm text-right">
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => {
                              setSelectedEmpleado(empleado);
                              setShowEmpleadoModal(true);
                            }}
                            className="mr-1"
                            title="Editar"
                          >
                            <FontAwesomeIcon icon={faEdit} />
                          </Button>
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => handleDeleteEmpleado(empleado.id)}
                            title="Eliminar"
                          >
                            <FontAwesomeIcon icon={faTrash} />
                          </Button>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            ) : (
              <p className="text-white text-center py-4">
                No se encontraron empleados que coincidan con los filtros.
              </p>
            )}
          </div>
        </div>

        <div>
          <div className="bg-gray-800 rounded-lg shadow-lg p-6">
            <h2 className="text-xl font-semibold text-white mb-4">Nuevo Empleado</h2>
            <Button
              onClick={() => {
                setSelectedEmpleado(null);
                setShowEmpleadoModal(true);
              }}
              className="w-full"
            >
              <FontAwesomeIcon icon={faPlus} className="mr-2" />
              Agregar Empleado
            </Button>
          </div>
        </div>
      </div>

      {/* Modales */}
      <EmpleadoModal
        isOpen={showEmpleadoModal}
        onClose={() => {
          setShowEmpleadoModal(false);
          setSelectedEmpleado(null);
        }}
        empleado={selectedEmpleado}
        departamentos={departamentos || []}
        cargos={cargos || []}
      />

      <DepartamentoModal
        isOpen={showDepartamentoModal}
        onClose={() => {
          setShowDepartamentoModal(false);
          setSelectedDepartamento(null);
        }}
        departamento={selectedDepartamento}
        empleados={empleados || []}
      />

      <CargoModal
        isOpen={showCargoModal}
        onClose={() => {
          setShowCargoModal(false);
          setSelectedCargo(null);
        }}
        cargo={selectedCargo}
        departamentos={departamentos || []}
      />

      <NominaModal
        isOpen={showNominaModal}
        onClose={() => {
          setShowNominaModal(false);
          setSelectedNomina(null);
        }}
        nomina={selectedNomina}
      />

      <ContratoModal
        isOpen={showContratoModal}
        onClose={() => {
          setShowContratoModal(false);
          setSelectedContrato(null);
        }}
        contrato={selectedContrato}
        empleados={empleados || []}
      />

      <VacacionesModal
        isOpen={showVacacionesModal}
        onClose={() => {
          setShowVacacionesModal(false);
          setSelectedVacaciones(null);
        }}
        vacaciones={selectedVacaciones}
        empleados={empleados || []}
      />

      <PermisoModal
        isOpen={showPermisoModal}
        onClose={() => {
          setShowPermisoModal(false);
          setSelectedPermiso(null);
        }}
        permiso={selectedPermiso}
        empleados={empleados || []}
      />

      <TurnoModal
        isOpen={showTurnoModal}
        onClose={() => {
          setShowTurnoModal(false);
          setSelectedTurno(null);
        }}
        turno={selectedTurno}
        empleados={empleados || []}
      />

      <CapacitacionModal
        isOpen={showCapacitacionModal}
        onClose={() => {
          setShowCapacitacionModal(false);
          setSelectedCapacitacion(null);
        }}
        capacitacion={selectedCapacitacion}
        empleados={empleados || []}
      />

      <EvaluacionDesempenoModal
        isOpen={showEvaluacionModal}
        onClose={() => {
          setShowEvaluacionModal(false);
          setSelectedEvaluacion(null);
        }}
        evaluacion={selectedEvaluacion}
        empleados={empleados || []}
      />
    </div>
  );
}
