import{createContext as t,useContext as r,useMemo as a}from"react";import{ComboboxMachine as c}from'./combobox-machine.js';const b=t(null);function u(e){let o=r(b);if(o===null){let n=new Error(`<${e} /> is missing a parent <Combobox /> component.`);throw Error.captureStackTrace&&Error.captureStackTrace(n,i),n}return o}function i({virtual:e=null,__demoMode:o=!1}={}){return a(()=>c.new({virtual:e,__demoMode:o}),[])}export{b as ComboboxContext,i as useComboboxMachine,u as useComboboxMachineContext};
