import { useNavigate, useLocation } from 'react-router-dom';
import { useQuery, useMutation } from '@tanstack/react-query';
import { consultasService } from '../../services/consultasService';
import { pacientesService } from '../../services/pacientesService';
import { useAuth } from '../../context/AuthContext';
import { Button } from '../../components/ui/Button';
import { Input } from '../../components/ui/Input';
import { useForm } from 'react-hook-form';
import { z } from 'zod';
import { zodResolver } from '@hookform/resolvers/zod';

const consultaSchema = z.object({
  paciente_id: z.string().min(1, 'Seleccione un paciente'),
  especialidad: z.string().min(1, 'La especialidad es obligatoria'),
  fecha: z.string().min(1, 'La fecha es obligatoria'),
  hora_inicio: z.string().min(1, 'La hora de inicio es obligatoria'),
  hora_fin: z.string().min(1, 'La hora de fin es obligatoria'),
  motivo: z.string().min(1, 'El motivo de la consulta es obligatorio'),
  tipo_consulta: z.enum(['PRIMERA_VEZ', 'CONTROL', 'PROCEDIMIENTO']),
  notas_medicas: z.string().optional(),
});

type ConsultaForm = z.infer<typeof consultaSchema>;

export const ConsultaNueva = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const { user } = useAuth();
  const hospitalId = user?.hospital_id || 1;
  
  // Obtener paciente_id de la URL si existe
  const queryParams = new URLSearchParams(location.search);
  const pacienteIdFromUrl = queryParams.get('paciente_id') || '';
  
  // Obtener todos los pacientes
  const { data: pacientes, isLoading: isLoadingPacientes } = useQuery({
    queryKey: ['pacientes', hospitalId],
    queryFn: () => pacientesService.getPacientes(hospitalId),
  });
  
  // Obtener todas las especialidades
  const { data: especialidades, isLoading: isLoadingEspecialidades } = useQuery({
    queryKey: ['especialidades', hospitalId],
    queryFn: () => consultasService.getEspecialidades(hospitalId),
  });
  
  // Formulario
  const {
    register,
    handleSubmit,
    formState: { errors },
  } = useForm<ConsultaForm>({
    resolver: zodResolver(consultaSchema),
    defaultValues: {
      paciente_id: pacienteIdFromUrl,
      tipo_consulta: 'PRIMERA_VEZ',
    },
  });
  
  // Mutación para crear una consulta
  const createConsultaMutation = useMutation({
    mutationFn: (data: ConsultaForm) =>
      consultasService.createConsulta(
        {
          ...data,
          medico_id: user?.id || '',
          estado: 'PROGRAMADA',
        },
        hospitalId
      ),
    onSuccess: (data: { id: string }) => {
      navigate(`/consultas/${data.id}`);
    },
  });
  
  // Manejar el envío del formulario
  const onSubmit = (data: ConsultaForm) => {
    createConsultaMutation.mutate(data);
  };
  
  const isLoading = isLoadingPacientes || isLoadingEspecialidades;
  
  if (isLoading) return <div className="text-center p-6 text-white">Cargando...</div>;
  
  // Lista de especialidades predefinidas si no hay datos del servidor
  const especialidadesLista = especialidades?.length ? especialidades : [
    'MEDICINA GENERAL',
    'PEDIATRÍA',
    'GINECOLOGÍA',
    'OBSTETRICIA',
    'CARDIOLOGÍA',
    'DERMATOLOGÍA',
    'ENDOCRINOLOGÍA',
    'GASTROENTEROLOGÍA',
    'NEUROLOGÍA',
    'OFTALMOLOGÍA',
    'ORTOPEDIA',
    'OTORRINOLARINGOLOGÍA',
    'PSIQUIATRÍA',
    'UROLOGÍA',
  ] as string[];
  
  return (
    <div className="bg-card p-6 rounded-lg shadow-lg">
        <div className="flex justify-between items-center mb-6">
          <h1 className="text-2xl font-bold text-primary">Nueva Consulta</h1>
          <Button variant="secondary" onClick={() => navigate('/consultas')}>
            Cancelar
          </Button>
        </div>
        
        <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <label className="block text-sm font-medium text-secondary mb-1">
                Paciente *
              </label>
              <select
                {...register('paciente_id')}
                className="w-full p-3 rounded-lg bg-secondary border border-color text-primary focus:ring-2 focus:ring-primary focus:border-primary transition-colors"
              >
                <option value="">Seleccione un paciente</option>
                {pacientes?.map((paciente) => (
                  <option key={paciente.id} value={paciente.id}>
                    {paciente.nombre_completo || `${paciente.primer_nombre} ${paciente.primer_apellido}`}
                  </option>
                ))}
              </select>
              {errors.paciente_id && (
                <p className="mt-1 text-sm text-error">{errors.paciente_id.message}</p>
              )}
            </div>
            
            <div>
              <label className="block text-sm font-medium text-secondary mb-1">
                Especialidad *
              </label>
              <select
                {...register('especialidad')}
                className="w-full p-3 rounded-lg bg-secondary border border-color text-primary focus:ring-2 focus:ring-primary focus:border-primary transition-colors"
              >
                <option value="">Seleccione una especialidad</option>
                {especialidadesLista.map((especialidad: string) => (
                  <option key={especialidad} value={especialidad}>
                    {especialidad}
                  </option>
                ))}
              </select>
              {errors.especialidad && (
                <p className="mt-1 text-sm text-error">{errors.especialidad.message}</p>
              )}
            </div>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div>
              <label className="block text-sm font-medium text-secondary mb-1">
                Fecha *
              </label>
              <Input
                {...register('fecha')}
                type="date"
                error={errors.fecha?.message}
                className="bg-secondary border-color text-primary"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-secondary mb-1">
                Hora de Inicio *
              </label>
              <Input
                {...register('hora_inicio')}
                type="time"
                error={errors.hora_inicio?.message}
                className="bg-secondary border-color text-primary"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-secondary mb-1">
                Hora de Fin *
              </label>
              <Input
                {...register('hora_fin')}
                type="time"
                error={errors.hora_fin?.message}
                className="bg-secondary border-color text-primary"
              />
            </div>
          </div>
          
          <div>
            <label className="block text-sm font-medium text-secondary mb-3">
              Tipo de Consulta *
            </label>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div className="flex items-center p-3 rounded-lg border border-color hover:bg-secondary/50 transition-colors">
                <input
                  {...register('tipo_consulta')}
                  type="radio"
                  value="PRIMERA_VEZ"
                  id="tipo-primera-vez"
                  className="mr-3 text-primary focus:ring-primary"
                />
                <label htmlFor="tipo-primera-vez" className="text-primary font-medium cursor-pointer">
                  Primera Vez
                </label>
              </div>

              <div className="flex items-center p-3 rounded-lg border border-color hover:bg-secondary/50 transition-colors">
                <input
                  {...register('tipo_consulta')}
                  type="radio"
                  value="CONTROL"
                  id="tipo-control"
                  className="mr-3 text-primary focus:ring-primary"
                />
                <label htmlFor="tipo-control" className="text-primary font-medium cursor-pointer">
                  Control
                </label>
              </div>

              <div className="flex items-center p-3 rounded-lg border border-color hover:bg-secondary/50 transition-colors">
                <input
                  {...register('tipo_consulta')}
                  type="radio"
                  value="PROCEDIMIENTO"
                  id="tipo-procedimiento"
                  className="mr-3 text-primary focus:ring-primary"
                />
                <label htmlFor="tipo-procedimiento" className="text-primary font-medium cursor-pointer">
                  Procedimiento
                </label>
              </div>
            </div>
            {errors.tipo_consulta && (
              <p className="mt-1 text-sm text-error">{errors.tipo_consulta.message}</p>
            )}
          </div>
          
          <div>
            <label className="block text-sm font-medium text-secondary mb-1">
              Motivo de la Consulta *
            </label>
            <textarea
              {...register('motivo')}
              rows={3}
              className="w-full p-3 rounded-lg bg-secondary border border-color text-primary focus:ring-2 focus:ring-primary focus:border-primary transition-colors resize-none"
              placeholder="Describa el motivo de la consulta..."
            ></textarea>
            {errors.motivo && (
              <p className="mt-1 text-sm text-error">{errors.motivo.message}</p>
            )}
          </div>

          <div>
            <label className="block text-sm font-medium text-secondary mb-1">
              Notas Médicas (Opcional)
            </label>
            <textarea
              {...register('notas_medicas')}
              rows={4}
              className="w-full p-3 rounded-lg bg-secondary border border-color text-primary focus:ring-2 focus:ring-primary focus:border-primary transition-colors resize-none"
              placeholder="Notas adicionales para el médico..."
            ></textarea>
          </div>
          
          <div className="flex justify-end space-x-2 pt-4">
            <Button
              type="button"
              variant="secondary"
              onClick={() => navigate('/consultas')}
            >
              Cancelar
            </Button>
            <Button
              type="submit"
              disabled={createConsultaMutation.isPending}
            >
              {createConsultaMutation.isPending ? 'Guardando...' : 'Guardar Consulta'}
            </Button>
          </div>
        </form>
      </div>
  );
};

