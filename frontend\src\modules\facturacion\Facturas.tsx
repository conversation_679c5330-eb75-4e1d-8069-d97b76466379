import { useState, useRef } from 'react';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { useNavigate } from 'react-router-dom';
import { format } from 'date-fns';
import { es } from 'date-fns/locale';
import { facturasService } from '../../services/facturasService';
import { useAuth } from '../../services/authService';
import { Button } from '../../components/ui/Button';
import { Input } from '../../components/ui/Input';
import { Select } from '../../components/ui/Select';
import { EstadoBadge } from '../../components/ui/EstadosBadges';
import { ModalConfirmacion, useModalConfirmacion } from '../../components/ui/ModalConfirmacion';
import GestorPagos from './GestorPagos';
import { ReportesFacturacion } from './ReportesFacturacion';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import {
  faPlus,
  faSearch,
  faEdit,
  faTrash,
  faEye,
  faFileInvoice,
  faFileInvoiceDollar,
  faCalendarDay,
  faFilter,
  faUser,
  faMoneyBillWave,
  faQrcode,
  faEnvelope,
  faDownload,
  faCreditCard,
  faChartLine,
  faFileAlt
} from '@fortawesome/free-solid-svg-icons';
import { Factura, EstadoFactura } from '../../types';

export const Facturas = () => {
  const navigate = useNavigate();
  const queryClient = useQueryClient();
  const { user } = useAuth();
  const hospitalId = user?.hospital_id || 1;
  const [activeTab, setActiveTab] = useState<'facturas' | 'pagos' | 'reportes'>('facturas');
  const [searchTerm, setSearchTerm] = useState('');
  const [filterEstado, setFilterEstado] = useState<string>('');
  const [filterFecha, setFilterFecha] = useState<string>('');
  const [selectedFactura, setSelectedFactura] = useState<Factura | null>(null);
  const [showTooltip, setShowTooltip] = useState(false);
  const [tooltipPosition, setTooltipPosition] = useState({ x: 0, y: 0, showAbove: false });
  const tooltipRef = useRef<HTMLDivElement>(null);

  // Modal de confirmación
  const { mostrar: mostrarConfirmacion, ModalConfirmacion: ModalConfirmacionComponent } = useModalConfirmacion();

  // Obtener todas las facturas
  const { data: facturas, isLoading, isError, refetch } = useQuery({
    queryKey: ['facturas', hospitalId],
    queryFn: () => facturasService.getAll(hospitalId),
  });

  // Mutación para anular factura
  const anularFacturaMutation = useMutation({
    mutationFn: ({ id, motivo }: { id: string; motivo?: string }) =>
      facturasService.anular(id, motivo, hospitalId),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['facturas', hospitalId] });
    },
  });

  // Mutación para generar CUFE
  const generarCUFEMutation = useMutation({
    mutationFn: (id: string) => facturasService.generarCUFE(id, hospitalId),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['facturas', hospitalId] });
    },
  });

  // Mutación para generar PDF
  const generarPDFMutation = useMutation({
    mutationFn: (id: string) => facturasService.generarPDF(id, hospitalId),
    onSuccess: (url) => {
      window.open(url, '_blank');
    },
  });

  // Mutación para enviar por correo
  const enviarPorCorreoMutation = useMutation({
    mutationFn: ({ id, email }: { id: string; email: string }) =>
      facturasService.enviarPorCorreo(id, email, hospitalId),
    onSuccess: () => {
      alert('Factura enviada por correo exitosamente');
    },
  });

  // Mutación para eliminar factura
  const deleteMutation = useMutation({
    mutationFn: (id: string) => facturasService.delete(id, hospitalId),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['facturas', hospitalId] });
    },
  });

  // Manejar anulación de factura
  const handleAnularFactura = (id: string) => {
    mostrarConfirmacion({
      tipo: 'anular',
      titulo: 'Anular Factura',
      mensaje: '¿Está seguro de que desea anular esta factura?',
      requiereMotivo: true,
      placeholderMotivo: 'Ingrese el motivo de la anulación',
      onConfirm: (motivo) => {
        if (motivo) {
          anularFacturaMutation.mutate({ id, motivo });
        }
      }
    });
  };

  // Manejar generación de CUFE
  const handleGenerarCUFE = (id: string) => {
    mostrarConfirmacion({
      tipo: 'confirmar',
      titulo: 'Generar CUFE',
      mensaje: '¿Desea generar el CUFE para esta factura? Esta acción no se puede deshacer.',
      onConfirm: () => {
        generarCUFEMutation.mutate(id);
      }
    });
  };

  // Manejar generación de PDF
  const handleGenerarPDF = (id: string) => {
    generarPDFMutation.mutate(id);
  };

  // Manejar envío por correo
  const handleEnviarPorCorreo = (id: string) => {
    const email = prompt('Ingrese el correo electrónico del destinatario:');
    if (email && /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(email)) {
      enviarPorCorreoMutation.mutate({ id, email });
    } else if (email !== null) {
      alert('Por favor ingrese un correo electrónico válido.');
    }
  };

  // Manejar eliminación de factura
  const handleDeleteFactura = (id: string) => {
    mostrarConfirmacion({
      tipo: 'eliminar',
      titulo: 'Eliminar Factura',
      mensaje: '¿Está seguro de eliminar esta factura? Esta acción no se puede deshacer.',
      onConfirm: () => {
        deleteMutation.mutate(id);
      }
    });
  };

  // Manejar el mouse enter en una fila
  const handleRowMouseEnter = (factura: Factura, event: React.MouseEvent) => {
    setSelectedFactura(factura);
    setShowTooltip(true);

    // Calcular la posición del tooltip
    const rect = (event.currentTarget as HTMLElement).getBoundingClientRect();
    const tooltipHeight = 150; // Altura estimada del tooltip
    const windowHeight = window.innerHeight;
    const viewportBottom = window.scrollY + windowHeight;
    const rowMiddle = rect.top + (rect.height / 2);
    const viewportMiddle = window.scrollY + (windowHeight / 2);

    // Determinar si debe mostrarse arriba o abajo basado en la posición de la fila en la ventana
    const showAbove = rowMiddle > viewportMiddle;

    setTooltipPosition({
      x: rect.left,
      y: showAbove ? rect.top - 5 : rect.bottom + 5,
      showAbove: showAbove
    });
  };

  // Manejar el mouse leave en una fila
  const handleRowMouseLeave = () => {
    setShowTooltip(false);
  };

  // Formatear fecha
  const formatDate = (dateString: string) => {
    try {
      return format(new Date(dateString), "dd 'de' MMMM 'de' yyyy", { locale: es });
    } catch (error) {
      console.error('Error al formatear la fecha:', error);
      return dateString;
    }
  };

  // Formatear moneda
  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('es-CO', {
      style: 'currency',
      currency: 'COP',
      minimumFractionDigits: 0
    }).format(amount);
  };

  // Mapear estados de factura a estados genéricos
  const mapEstadoFactura = (estado: EstadoFactura) => {
    switch (estado) {
      case 'PENDIENTE':
        return 'PENDIENTE';
      case 'PAGADA':
        return 'PAGADO';
      case 'ANULADA':
        return 'ANULADO';
      case 'VENCIDA':
        return 'VENCIDO';
      default:
        return 'PENDIENTE';
    }
  };

  // Filtrar facturas según los filtros seleccionados
  const filteredFacturas = Array.isArray(facturas)
    ? facturas.filter((factura) => {
        // Filtro por término de búsqueda
        const searchMatch = searchTerm === '' ||
          (factura.paciente?.nombre_completo?.toLowerCase().includes(searchTerm.toLowerCase()) ||
           factura.numero_factura?.toLowerCase().includes(searchTerm.toLowerCase()) ||
           factura.tipo_servicio?.toLowerCase().includes(searchTerm.toLowerCase()));

        // Filtro por estado
        const estadoMatch = filterEstado === '' || factura.estado === filterEstado;

        // Filtro por fecha
        const fechaMatch = filterFecha === '' || factura.fecha_emision?.startsWith(filterFecha);

        return searchMatch && estadoMatch && fechaMatch;
      })
    : [];

  // Ordenar facturas por fecha (más recientes primero)
  const sortedFacturas = [...filteredFacturas].sort((a, b) => {
    const dateA = new Date(a.fecha_emision).getTime();
    const dateB = new Date(b.fecha_emision).getTime();
    return dateB - dateA;
  });

  // Renderizar contenido según pestaña activa
  const renderTabContent = () => {
    switch (activeTab) {
      case 'pagos':
        return <GestorPagos />;
      case 'reportes':
        return <ReportesFacturacion />;
      default:
        return renderFacturasContent();
    }
  };

  // Renderizar contenido de facturas principal
  const renderFacturasContent = () => {
    if (isLoading) return <div className="text-center p-6 text-gray-700">Cargando facturas...</div>;
    if (isError) return <div className="text-center p-6 text-red-500">Error al cargar las facturas</div>;

    return (
      <>
        {/* Filtros */}
        <div className="bg-white border border-gray-200 p-4 mb-6 rounded-lg shadow-sm">
          <h2 className="text-xl font-semibold text-gray-900 mb-4 flex items-center">
            <FontAwesomeIcon icon={faFilter} className="mr-2" />
            Filtros
          </h2>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                <FontAwesomeIcon icon={faSearch} className="mr-2" />
                Buscar
              </label>
              <Input
                type="text"
                placeholder="Buscar por paciente, número, servicio..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="border-gray-300"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                <FontAwesomeIcon icon={faFileInvoice} className="mr-2" />
                Estado
              </label>
              <Select
                value={filterEstado}
                onChange={(e) => setFilterEstado(e.target.value)}
                className="border-gray-300"
              >
                <option value="">Todos los estados</option>
                <option value="PENDIENTE">Pendiente</option>
                <option value="PAGADA">Pagada</option>
                <option value="ANULADA">Anulada</option>
                <option value="VENCIDA">Vencida</option>
              </Select>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                <FontAwesomeIcon icon={faCalendarDay} className="mr-2" />
                Fecha de Emisión
              </label>
              <Input
                type="date"
                value={filterFecha}
                onChange={(e) => setFilterFecha(e.target.value)}
                className="border-gray-300"
              />
            </div>
          </div>
        </div>

        {/* Tabla de facturas */}
        <div className="bg-white border border-gray-200 rounded-lg overflow-hidden shadow-sm">
          {Array.isArray(sortedFacturas) && sortedFacturas.length > 0 ? (
            <table className="min-w-full table-auto">
              <thead className="bg-gray-50 border-b border-gray-200">
                <tr>
                  <th className="px-4 py-3 text-left text-sm font-medium text-gray-700 uppercase tracking-wider">
                    <div className="flex items-center">
                      <FontAwesomeIcon icon={faFileInvoice} className="mr-2" />
                      Número
                    </div>
                  </th>
                  <th className="px-4 py-3 text-left text-sm font-medium text-gray-700 uppercase tracking-wider">
                    <div className="flex items-center">
                      <FontAwesomeIcon icon={faUser} className="mr-2" />
                      Paciente
                    </div>
                  </th>
                  <th className="px-4 py-3 text-left text-sm font-medium text-gray-700 uppercase tracking-wider">
                    <div className="flex items-center">
                      <FontAwesomeIcon icon={faCalendarDay} className="mr-2" />
                      Fecha
                    </div>
                  </th>
                  <th className="px-4 py-3 text-left text-sm font-medium text-gray-700 uppercase tracking-wider">
                    <div className="flex items-center">
                      <FontAwesomeIcon icon={faMoneyBillWave} className="mr-2" />
                      Monto
                    </div>
                  </th>
                  <th className="px-4 py-3 text-left text-sm font-medium text-gray-700 uppercase tracking-wider">
                    <div className="flex items-center">
                      <FontAwesomeIcon icon={faFileInvoiceDollar} className="mr-2" />
                      Estado
                    </div>
                  </th>
                  <th className="px-4 py-3 text-right text-sm font-medium text-gray-700 uppercase tracking-wider">
                    Acciones
                  </th>
                </tr>
              </thead>
              <tbody>
                {sortedFacturas.map((factura) => (
                  <tr
                    key={factura.id}
                    className="hover:bg-gray-50 transition-colors"
                    onMouseEnter={(e) => handleRowMouseEnter(factura, e)}
                    onMouseLeave={handleRowMouseLeave}
                  >
                    <td className="px-4 py-3 whitespace-nowrap text-sm text-gray-900">
                      {factura.numero_factura}
                    </td>
                    <td className="px-4 py-3 whitespace-nowrap text-sm text-gray-900">
                      {factura.paciente?.nombre_completo || 'Paciente no disponible'}
                    </td>
                    <td className="px-4 py-3 whitespace-nowrap text-sm text-gray-900">
                      {formatDate(factura.fecha_emision)}
                    </td>
                    <td className="px-4 py-3 whitespace-nowrap text-sm text-gray-900">
                      {formatCurrency(factura.monto_total)}
                    </td>
                    <td className="px-4 py-3 whitespace-nowrap text-sm">
                      <EstadoBadge estado={mapEstadoFactura(factura.estado as EstadoFactura)} size="sm" />
                    </td>
                    <td className="px-4 py-3 whitespace-nowrap text-sm text-right">
                      <div className="flex justify-end space-x-1">
                        {factura.estado === 'PENDIENTE' && (
                          <Button
                            variant="warning"
                            size="sm"
                            onClick={() => handleAnularFactura(factura.id)}
                            className="text-orange-700 hover:text-orange-800 hover:bg-orange-50 border border-orange-200 bg-orange-50/50 focus:ring-2 focus:ring-orange-500 focus:ring-offset-2 focus:outline-none focus:scale-110 transition-all duration-200 focus:shadow-lg focus:shadow-orange-500/25"
                            title="Anular factura"
                          >
                            <FontAwesomeIcon icon={faFileInvoice} className="text-orange-700" />
                          </Button>
                        )}
                        {!factura.cufe && (
                          <Button
                            variant="secondary"
                            size="sm"
                            onClick={() => handleGenerarCUFE(factura.id)}
                            className="text-purple-700 hover:text-purple-800 hover:bg-purple-50 border border-purple-200 bg-purple-50/50 focus:ring-2 focus:ring-purple-500 focus:ring-offset-2 focus:outline-none focus:scale-110 transition-all duration-200 focus:shadow-lg focus:shadow-purple-500/25"
                            title="Generar CUFE"
                          >
                            <FontAwesomeIcon icon={faQrcode} className="text-purple-700" />
                          </Button>
                        )}
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => handleGenerarPDF(factura.id)}
                          className="text-indigo-700 hover:text-indigo-800 hover:bg-indigo-50 border border-indigo-200 bg-indigo-50/50 focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 focus:outline-none focus:scale-110 transition-all duration-200 focus:shadow-lg focus:shadow-indigo-500/25"
                          title="Generar PDF"
                        >
                          <FontAwesomeIcon icon={faDownload} className="text-indigo-700" />
                        </Button>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => handleEnviarPorCorreo(factura.id)}
                          className="text-teal-700 hover:text-teal-800 hover:bg-teal-50 border border-teal-200 bg-teal-50/50 focus:ring-2 focus:ring-teal-500 focus:ring-offset-2 focus:outline-none focus:scale-110 transition-all duration-200 focus:shadow-lg focus:shadow-teal-500/25"
                          title="Enviar por correo"
                        >
                          <FontAwesomeIcon icon={faEnvelope} className="text-teal-700" />
                        </Button>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => navigate(`/facturacion/ver/${factura.id}`)}
                          className="text-blue-700 hover:text-blue-800 hover:bg-blue-50 border border-blue-200 bg-blue-50/50 focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 focus:outline-none focus:scale-110 transition-all duration-200 focus:shadow-lg focus:shadow-blue-500/25"
                          title="Ver detalles"
                        >
                          <FontAwesomeIcon icon={faEye} className="text-blue-700" />
                        </Button>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => navigate(`/facturacion/editar/${factura.id}`)}
                          className="text-amber-700 hover:text-amber-800 hover:bg-amber-50 border border-amber-200 bg-amber-50/50 focus:ring-2 focus:ring-amber-500 focus:ring-offset-2 focus:outline-none focus:scale-110 transition-all duration-200 focus:shadow-lg focus:shadow-amber-500/25"
                          title="Editar factura"
                        >
                          <FontAwesomeIcon icon={faEdit} className="text-amber-700" />
                        </Button>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => handleDeleteFactura(factura.id)}
                          className="text-red-700 hover:text-red-800 hover:bg-red-50 border border-red-200 bg-red-50/50 focus:ring-2 focus:ring-red-500 focus:ring-offset-2 focus:outline-none focus:scale-110 transition-all duration-200 focus:shadow-lg focus:shadow-red-500/25"
                          title="Eliminar factura"
                        >
                          <FontAwesomeIcon icon={faTrash} className="text-red-700" />
                        </Button>
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          ) : (
            <p className="text-gray-700 text-center py-4">
              {facturas && facturas.length > 0
                ? 'No se encontraron facturas que coincidan con los filtros.'
                : 'No hay facturas registradas en el sistema.'}
            </p>
          )}
        </div>

        {/* Tooltip para mostrar información adicional */}
        {showTooltip && selectedFactura && (
          <div
            ref={tooltipRef}
            className={`fixed z-50 bg-gray-800 text-white p-3 rounded shadow-lg border border-gray-700 max-w-md tooltip-container ${tooltipPosition.showAbove ? 'tooltip-above' : 'tooltip-below'}`}
            style={{
              left: `${tooltipPosition.x}px`,
              top: tooltipPosition.showAbove ? 'auto' : `${tooltipPosition.y}px`,
              bottom: tooltipPosition.showAbove ? `calc(100vh - ${tooltipPosition.y}px)` : 'auto',
            }}
          >
            <div className="space-y-2">
              <p className="text-sm font-medium">
                <span className="font-bold">Número:</span> {selectedFactura.numero_factura}
              </p>
              <p className="text-sm font-medium">
                <span className="font-bold">Paciente:</span> {selectedFactura.paciente?.nombre_completo}
                {selectedFactura.paciente?.numero_documento && (
                  <span className="text-gray-400 ml-1">({selectedFactura.paciente.tipo_documento} {selectedFactura.paciente.numero_documento})</span>
                )}
              </p>
              <p className="text-sm font-medium">
                <span className="font-bold">Fecha emisión:</span> {formatDate(selectedFactura.fecha_emision)}
              </p>
              <p className="text-sm font-medium">
                <span className="font-bold">Fecha vencimiento:</span> {selectedFactura.fecha_vencimiento ? formatDate(selectedFactura.fecha_vencimiento) : 'No especificada'}
              </p>
              <p className="text-sm font-medium">
                <span className="font-bold">Monto total:</span> {formatCurrency(selectedFactura.monto_total)}
              </p>
              <p className="text-sm font-medium">
                <span className="font-bold">Tipo de servicio:</span> {selectedFactura.tipo_servicio || 'No especificado'}
              </p>
              {selectedFactura.cufe && (
                <p className="text-sm font-medium">
                  <span className="font-bold">CUFE:</span> {selectedFactura.cufe}
                </p>
              )}
            </div>
          </div>
        )}
      </>
    );
  };

  return (
    <div className="p-6 bg-white min-h-screen">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold text-gray-900">
          <FontAwesomeIcon icon={faFileInvoiceDollar} className="mr-2" />
          Gestión de Facturación
        </h1>
        {activeTab === 'facturas' && (
          <Button
            onClick={() => navigate('/facturacion/nueva')}
            className="bg-blue-600 hover:bg-blue-700 text-white focus:ring-4 focus:ring-blue-300 focus:ring-offset-2 focus:outline-none focus:scale-105 transition-all duration-300 focus:shadow-xl focus:shadow-blue-500/50 focus:bg-blue-800"
          >
            <FontAwesomeIcon icon={faPlus} className="mr-2" />
            Nueva Factura
          </Button>
        )}
      </div>

      {/* Navegación por pestañas */}
      <div className="mb-6 border-b border-gray-200">
        <nav className="flex space-x-8">
          <button
            onClick={() => setActiveTab('facturas')}
            className={`py-2 px-1 border-b-2 font-medium text-sm transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 focus:scale-105 focus:shadow-lg ${
              activeTab === 'facturas'
                ? 'border-blue-500 text-blue-600'
                : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
            }`}
          >
            <FontAwesomeIcon icon={faFileInvoice} className="mr-2" />
            Facturas
          </button>
          <button
            onClick={() => setActiveTab('pagos')}
            className={`py-2 px-1 border-b-2 font-medium text-sm transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 focus:scale-105 focus:shadow-lg ${
              activeTab === 'pagos'
                ? 'border-blue-500 text-blue-600'
                : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
            }`}
          >
            <FontAwesomeIcon icon={faCreditCard} className="mr-2" />
            Pagos
          </button>
          <button
            onClick={() => setActiveTab('reportes')}
            className={`py-2 px-1 border-b-2 font-medium text-sm transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 focus:scale-105 focus:shadow-lg ${
              activeTab === 'reportes'
                ? 'border-blue-500 text-blue-600'
                : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
            }`}
          >
            <FontAwesomeIcon icon={faChartLine} className="mr-2" />
            Reportes
          </button>
        </nav>
      </div>

      {/* Contenido de la pestaña activa */}
      {renderTabContent()}

      {/* Modal de confirmación */}
      <ModalConfirmacionComponent />
    </div>
  );
};

