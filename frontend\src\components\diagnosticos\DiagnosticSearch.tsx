import React, { useState, useEffect, useRef } from 'react';
import { Search, X, AlertCircle, CheckCircle, Clock, Filter } from 'lucide-react';
import { useDiagnosis } from '../../hooks/useDiagnosis';
import { Diagnosis, DiagnosticSearchProps } from '../../types/diagnosis.types';

export const DiagnosticSearch: React.FC<DiagnosticSearchProps> = ({
  onSelect,
  version = 'BOTH',
  placeholder = 'Buscar diagnóstico...',
  multiple = false,
  disabled = false,
  className = '',
  showFilters = false,
  showSuggestions = true,
  maxResults = 20,
  autoFocus = false,
  clearOnSelect = false,
  value
}) => {
  // Estados locales
  const [query, setQuery] = useState('');
  const [isOpen, setIsOpen] = useState(false);
  const [selectedItems, setSelectedItems] = useState<Diagnosis[]>(
    multiple && Array.isArray(value) ? value : value ? [value as Diagnosis] : []
  );
  const [highlightedIndex, setHighlightedIndex] = useState(-1);

  // Referencias
  const inputRef = useRef<HTMLInputElement>(null);
  const listRef = useRef<HTMLUListElement>(null);
  const containerRef = useRef<HTMLDivElement>(null);

  // Hook de diagnósticos
  const { searchDiagnosis, results, isLoading, error, filters, setFilters } = useDiagnosis();

  // Efectos
  useEffect(() => {
    if (autoFocus && inputRef.current) {
      inputRef.current.focus();
    }
  }, [autoFocus]);

  useEffect(() => {
    if (query.length >= 2) {
      searchDiagnosis({
        query,
        version,
        limit: maxResults
      });
      setIsOpen(true);
    } else {
      setIsOpen(false);
    }
  }, [query, version, maxResults, searchDiagnosis]);

  // Manejar selección de diagnóstico
  const handleSelect = (diagnosis: Diagnosis) => {
    if (multiple) {
      const isAlreadySelected = selectedItems.some(item => item.id === diagnosis.id);
      if (!isAlreadySelected) {
        const newSelection = [...selectedItems, diagnosis];
        setSelectedItems(newSelection);
        onSelect(diagnosis);
      }
    } else {
      setSelectedItems([diagnosis]);
      onSelect(diagnosis);
      if (clearOnSelect) {
        setQuery('');
      } else {
        setQuery(diagnosis.title);
      }
    }
    setIsOpen(false);
    setHighlightedIndex(-1);
  };

  // Remover elemento seleccionado (solo para múltiple)
  const handleRemove = (diagnosisId: string) => {
    const newSelection = selectedItems.filter(item => item.id !== diagnosisId);
    setSelectedItems(newSelection);
  };

  // Manejar teclas
  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (!isOpen) return;

    switch (e.key) {
      case 'ArrowDown':
        e.preventDefault();
        setHighlightedIndex(prev => 
          prev < results.length - 1 ? prev + 1 : prev
        );
        break;
      case 'ArrowUp':
        e.preventDefault();
        setHighlightedIndex(prev => prev > 0 ? prev - 1 : prev);
        break;
      case 'Enter':
        e.preventDefault();
        if (highlightedIndex >= 0 && results[highlightedIndex]) {
          handleSelect(results[highlightedIndex]);
        }
        break;
      case 'Escape':
        setIsOpen(false);
        setHighlightedIndex(-1);
        break;
    }
  };

  // Scroll al elemento resaltado
  useEffect(() => {
    if (highlightedIndex >= 0 && listRef.current) {
      const highlightedElement = listRef.current.children[highlightedIndex] as HTMLElement;
      if (highlightedElement) {
        highlightedElement.scrollIntoView({
          block: 'nearest',
          behavior: 'smooth'
        });
      }
    }
  }, [highlightedIndex]);

  // Cerrar al hacer clic fuera
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (containerRef.current && !containerRef.current.contains(event.target as Node)) {
        setIsOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);

  // Renderizar badge de versión
  const renderVersionBadge = (source: 'CIE-10' | 'CIE-11') => (
    <span
      className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${
        source === 'CIE-11'
          ? 'bg-secondary/20 text-secondary'
          : 'bg-accent/20 text-accent'
      }`}
    >
      {source}
    </span>
  );

  // Renderizar elemento seleccionado
  const renderSelectedItem = (diagnosis: Diagnosis) => (
    <div
      key={diagnosis.id}
      className="inline-flex items-center gap-2 px-3 py-1 bg-primary/10 text-primary rounded-full text-sm"
    >
      <span className="font-medium">{diagnosis.code}</span>
      <span className="truncate max-w-32">{diagnosis.title}</span>
      {multiple && (
        <button
          onClick={() => handleRemove(diagnosis.id)}
          className="p-0.5 hover:bg-primary/20 rounded-full transition-colors"
          aria-label={`Remover ${diagnosis.title}`}
        >
          <X className="h-3 w-3" />
        </button>
      )}
    </div>
  );

  return (
    <div ref={containerRef} className={`relative ${className}`}>
      {/* Elementos seleccionados (múltiple) */}
      {multiple && selectedItems.length > 0 && (
        <div className="flex flex-wrap gap-2 mb-2">
          {selectedItems.map(renderSelectedItem)}
        </div>
      )}

      {/* Input de búsqueda */}
      <div className="relative">
        <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
          <Search className="h-4 w-4 text-muted" />
        </div>
        
        <input
          ref={inputRef}
          type="text"
          value={query}
          onChange={(e) => setQuery(e.target.value)}
          onKeyDown={handleKeyDown}
          onFocus={() => query.length >= 2 && setIsOpen(true)}
          placeholder={placeholder}
          disabled={disabled}
          className={`
            w-full pl-10 pr-10 py-2 border rounded-lg
            bg-bg-card border-border text-text-primary
            placeholder:text-text-muted
            focus:border-primary focus:outline-none focus:ring-1 focus:ring-primary
            disabled:opacity-50 disabled:cursor-not-allowed
            transition-colors
          `}
          aria-expanded={isOpen}
          aria-haspopup="listbox"
          aria-autocomplete="list"
          role="combobox"
        />

        {/* Indicadores de estado */}
        <div className="absolute inset-y-0 right-0 pr-3 flex items-center">
          {isLoading && (
            <div className="h-4 w-4 animate-spin rounded-full border-2 border-primary border-t-transparent" />
          )}
          {error && (
            <AlertCircle className="h-4 w-4 text-error" title={error} />
          )}
          {showFilters && (
            <button
              onClick={() => {/* Implementar modal de filtros */}}
              className="ml-2 p-1 hover:bg-border rounded transition-colors"
              title="Filtros"
            >
              <Filter className="h-4 w-4 text-muted" />
            </button>
          )}
        </div>
      </div>

      {/* Lista de resultados */}
      {isOpen && (
        <div className="absolute z-50 w-full mt-1 bg-bg-card border border-border rounded-lg shadow-lg max-h-60 overflow-auto">
          {isLoading && (
            <div className="p-4 text-center text-muted">
              <Clock className="h-5 w-5 mx-auto mb-2 animate-pulse" />
              Buscando diagnósticos...
            </div>
          )}

          {error && (
            <div className="p-4 text-center text-error">
              <AlertCircle className="h-5 w-5 mx-auto mb-2" />
              {error}
            </div>
          )}

          {!isLoading && !error && results.length === 0 && query.length >= 2 && (
            <div className="p-4 text-center text-muted">
              No se encontraron diagnósticos para "{query}"
            </div>
          )}

          {!isLoading && !error && results.length > 0 && (
            <ul ref={listRef} role="listbox" className="py-1">
              {results.map((diagnosis, index) => (
                <li
                  key={diagnosis.id}
                  role="option"
                  aria-selected={highlightedIndex === index}
                  className={`
                    px-4 py-3 cursor-pointer transition-colors
                    ${highlightedIndex === index
                      ? 'bg-primary/10 text-primary'
                      : 'hover:bg-border/50'
                    }
                  `}
                  onClick={() => handleSelect(diagnosis)}
                  onMouseEnter={() => setHighlightedIndex(index)}
                >
                  <div className="flex items-start justify-between gap-3">
                    <div className="flex-1 min-w-0">
                      <div className="flex items-center gap-2 mb-1">
                        <span className="font-semibold text-sm">
                          {diagnosis.code}
                        </span>
                        {renderVersionBadge(diagnosis.source)}
                      </div>
                      <p className="text-sm font-medium text-text-primary truncate">
                        {diagnosis.title}
                      </p>
                      {diagnosis.description && (
                        <p className="text-xs text-text-muted mt-1 line-clamp-2">
                          {diagnosis.description}
                        </p>
                      )}
                      {diagnosis.category && (
                        <p className="text-xs text-text-muted mt-1">
                          Categoría: {diagnosis.category}
                        </p>
                      )}
                    </div>
                    <CheckCircle className="h-4 w-4 text-success opacity-0 group-hover:opacity-100 transition-opacity" />
                  </div>
                </li>
              ))}
            </ul>
          )}

          {/* Sugerencias */}
          {showSuggestions && !isLoading && results.length > 0 && (
            <div className="border-t border-border p-3">
              <p className="text-xs text-text-muted mb-2">Sugerencias:</p>
              <div className="flex flex-wrap gap-1">
                {['Diabetes', 'Hipertensión', 'Neumonía'].map((suggestion) => (
                  <button
                    key={suggestion}
                    onClick={() => setQuery(suggestion)}
                    className="px-2 py-1 text-xs bg-border/50 hover:bg-border rounded transition-colors"
                  >
                    {suggestion}
                  </button>
                ))}
              </div>
            </div>
          )}
        </div>
      )}
    </div>
  );
};

export default DiagnosticSearch;
