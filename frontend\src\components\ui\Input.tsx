import { InputHTMLAttributes, forwardRef } from 'react';

interface InputProps extends InputHTMLAttributes<HTMLInputElement> {
  error?: string;
}

export const Input = forwardRef<HTMLInputElement, InputProps>(
  ({ className = '', error, ...props }, ref) => {
    return (
      <div className="w-full">
        <input
          ref={ref}
          className={`
            w-full px-3 py-3 border rounded-lg
            focus:outline-none focus:ring-2 focus:ring-primary focus:border-primary
            transition-colors duration-200
            bg-secondary border-color text-primary
            placeholder:text-muted
            ${error ? 'border-error focus:ring-error' : 'border-color'}
            ${className}
          `}
          {...props}
        />
        {error && <p className="mt-1 text-sm text-error">{error}</p>}
      </div>
    );
  }
);

Input.displayName = 'Input';
