import React, { useState, useRef, useEffect } from 'react';
import { useSettingsStore } from '../../store/useSettingsStore';
import { useTheme } from '../../hooks/useTheme';

// Icono discreto para personalización
const PaletteIcon = ({ className }: { className?: string }) => (
  <svg className={className} fill="none" stroke="currentColor" viewBox="0 0 24 24">
    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M7 21a4 4 0 01-4-4V5a2 2 0 012-2h4a2 2 0 012 2v12a4 4 0 01-4 4zM7 3H5a2 2 0 00-2 2v12a4 4 0 004 4h2a2 2 0 002-2V5a2 2 0 00-2-2z" />
    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 9h6m-6 4h6m2-7h4a2 2 0 012 2v8a2 2 0 01-2 2h-4" />
  </svg>
);

const ResetIcon = ({ className }: { className?: string }) => (
  <svg className={className} fill="none" stroke="currentColor" viewBox="0 0 24 24">
    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
  </svg>
);

export const UICustomizer: React.FC = () => {
  const [isOpen, setIsOpen] = useState(false);
  const panelRef = useRef<HTMLDivElement>(null);
  const buttonRef = useRef<HTMLButtonElement>(null);

  const {
    glassmorphismOpacity,
    textContrast,
    backgroundContrast,
    backgroundTint,
    contentBackground,
    updateGlassmorphismOpacity,
    updateTextContrast,
    updateBackgroundContrast,
    updateBackgroundTint,
    updateContentBackground,
    highContrast,
    reducedMotion,
    toggleHighContrast,
    toggleReducedMotion
  } = useSettingsStore();

  const { theme, setTheme } = useTheme();

  // Cerrar panel al presionar Escape o hacer clic fuera
  useEffect(() => {
    const handleEscape = (event: KeyboardEvent) => {
      if (event.key === 'Escape' && isOpen) {
        setIsOpen(false);
      }
    };

    const handleClickOutside = (event: MouseEvent) => {
      if (
        isOpen &&
        panelRef.current &&
        !panelRef.current.contains(event.target as Node) &&
        buttonRef.current &&
        !buttonRef.current.contains(event.target as Node)
      ) {
        setIsOpen(false);
      }
    };

    document.addEventListener('keydown', handleEscape);
    document.addEventListener('mousedown', handleClickOutside);

    return () => {
      document.removeEventListener('keydown', handleEscape);
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [isOpen]);

  // Función para resetear configuraciones
  const resetToDefaults = () => {
    console.log('Resetting UI settings to defaults');
    updateGlassmorphismOpacity(60);
    updateTextContrast(90);
    updateBackgroundContrast(80);
    updateBackgroundTint(0);
    updateContentBackground('elegant');
  };

  // Debug: mostrar valores actuales
  console.log('UICustomizer current values:', {
    glassmorphismOpacity,
    textContrast,
    backgroundContrast,
    backgroundTint,
    contentBackground,
    theme
  });

  return (
    <div className="relative">
      {/* Botón discreto */}
      <button
        ref={buttonRef}
        onClick={() => setIsOpen(!isOpen)}
        className={`
          w-6 h-6 rounded-md transition-all duration-200 flex items-center justify-center
          ${isOpen 
            ? 'bg-primary/20 text-primary border border-primary/30' 
            : 'text-muted hover:text-secondary hover:bg-secondary/10 border border-transparent'
          }
        `}
        title="Personalizar interfaz"
        aria-label="Personalizar interfaz"
      >
        <PaletteIcon className="h-3.5 w-3.5" />
      </button>

      {/* Panel de personalización con z-index alto */}
      {isOpen && (
        <div
          ref={panelRef}
          className="absolute right-0 top-full mt-2 w-80 glassmorphism border border-glass-border rounded-lg shadow-2xl z-[9999] p-4 max-h-96 overflow-y-auto custom-scrollbar"
          onClick={(e) => e.stopPropagation()}
        >
            {/* Header del panel */}
            <div className="flex items-center justify-between mb-4 pb-2 border-b border-glass-border">
              <h3 className="text-sm font-semibold text-primary">Personalización UI</h3>
              <button
                onClick={resetToDefaults}
                className="p-1 rounded text-muted hover:text-secondary hover:bg-secondary/10 transition-colors"
                title="Resetear a valores por defecto"
              >
                <ResetIcon className="h-4 w-4" />
              </button>
            </div>

            {/* Configuración de tema */}
            <div className="space-y-3 mb-4">
              <h4 className="text-xs font-medium text-secondary uppercase tracking-wide">Tema</h4>
              <div className="flex space-x-2">
                <button
                  onClick={() => setTheme('light')}
                  className={`
                    flex-1 px-2 py-1.5 rounded text-xs transition-all
                    ${theme === 'light' 
                      ? 'bg-primary text-white' 
                      : 'bg-card border border-color hover:border-primary'
                    }
                  `}
                >
                  🌞 Claro
                </button>
                <button
                  onClick={() => setTheme('dark')}
                  className={`
                    flex-1 px-2 py-1.5 rounded text-xs transition-all
                    ${theme === 'dark' 
                      ? 'bg-primary text-white' 
                      : 'bg-card border border-color hover:border-primary'
                    }
                  `}
                >
                  🌙 Oscuro
                </button>
              </div>
            </div>

            {/* Configuración de contenido */}
            <div className="space-y-3 mb-4">
              <h4 className="text-xs font-medium text-secondary uppercase tracking-wide">Fondo de Contenido</h4>
              <div className="grid grid-cols-3 gap-1">
                {[
                  { value: 'elegant', label: 'Elegante', emoji: '✨' },
                  { value: 'glass', label: 'Glass', emoji: '🔮' },
                  { value: 'solid', label: 'Sólido', emoji: '⬜' }
                ].map((option) => (
                  <button
                    key={option.value}
                    onClick={() => updateContentBackground(option.value as any)}
                    className={`
                      px-2 py-1.5 rounded text-xs transition-all
                      ${contentBackground === option.value 
                        ? 'bg-primary text-white' 
                        : 'bg-card border border-color hover:border-primary'
                      }
                    `}
                  >
                    {option.emoji} {option.label}
                  </button>
                ))}
              </div>
            </div>

            {/* Glassmorphism */}
            <div className="space-y-3 mb-4">
              <h4 className="text-xs font-medium text-secondary uppercase tracking-wide">Glassmorphism</h4>
              
              <div>
                <label className="text-xs text-muted">Opacidad: {glassmorphismOpacity}%</label>
                <input
                  type="range"
                  min="0"
                  max="100"
                  value={glassmorphismOpacity}
                  onChange={(e) => {
                    const value = Number(e.target.value);
                    console.log('Updating glassmorphism opacity:', value);
                    updateGlassmorphismOpacity(value);
                  }}
                  onClick={(e) => e.stopPropagation()}
                  onMouseDown={(e) => e.stopPropagation()}
                  className="w-full h-2 bg-gray-300 rounded-lg appearance-none cursor-pointer mt-1 slider"
                  style={{
                    background: `linear-gradient(to right, var(--primary) 0%, var(--primary) ${glassmorphismOpacity}%, var(--border) ${glassmorphismOpacity}%, var(--border) 100%)`
                  }}
                />
              </div>
            </div>

            {/* Contraste y Fondo */}
            <div className="space-y-3 mb-4">
              <h4 className="text-xs font-medium text-secondary uppercase tracking-wide">Ajustes Visuales</h4>
              
              <div>
                <label className="text-xs text-muted">Contraste de Texto: {textContrast}%</label>
                <input
                  type="range"
                  min="50"
                  max="100"
                  value={textContrast}
                  onChange={(e) => {
                    const value = Number(e.target.value);
                    console.log('Updating text contrast:', value);
                    updateTextContrast(value);
                  }}
                  onClick={(e) => e.stopPropagation()}
                  onMouseDown={(e) => e.stopPropagation()}
                  className="w-full h-2 bg-gray-300 rounded-lg appearance-none cursor-pointer mt-1 slider"
                  style={{
                    background: `linear-gradient(to right, var(--primary) 0%, var(--primary) ${(textContrast-50)/50*100}%, var(--border) ${(textContrast-50)/50*100}%, var(--border) 100%)`
                  }}
                />
              </div>

              <div>
                <label className="text-xs text-muted">Contraste de Fondo: {backgroundContrast}%</label>
                <input
                  type="range"
                  min="50"
                  max="150"
                  value={backgroundContrast}
                  onChange={(e) => {
                    const value = Number(e.target.value);
                    console.log('Updating background contrast:', value);
                    updateBackgroundContrast(value);
                  }}
                  onClick={(e) => e.stopPropagation()}
                  onMouseDown={(e) => e.stopPropagation()}
                  className="w-full h-2 bg-gray-300 rounded-lg appearance-none cursor-pointer mt-1 slider"
                  style={{
                    background: `linear-gradient(to right, var(--primary) 0%, var(--primary) ${(backgroundContrast-50)/100*100}%, var(--border) ${(backgroundContrast-50)/100*100}%, var(--border) 100%)`
                  }}
                />
              </div>

              <div>
                <label className="text-xs text-muted">Tinte de Fondo: {backgroundTint > 0 ? '+' : ''}{backgroundTint}</label>
                <input
                  type="range"
                  min="-100"
                  max="100"
                  value={backgroundTint}
                  onChange={(e) => {
                    const value = Number(e.target.value);
                    console.log('Updating background tint:', value);
                    updateBackgroundTint(value);
                  }}
                  onClick={(e) => e.stopPropagation()}
                  onMouseDown={(e) => e.stopPropagation()}
                  className="w-full h-2 bg-gray-300 rounded-lg appearance-none cursor-pointer mt-1 slider"
                  style={{
                    background: `linear-gradient(to right, var(--primary) 0%, var(--primary) ${(backgroundTint+100)/200*100}%, var(--border) ${(backgroundTint+100)/200*100}%, var(--border) 100%)`
                  }}
                />
              </div>
            </div>

            {/* Accesibilidad */}
            <div className="space-y-3">
              <h4 className="text-xs font-medium text-secondary uppercase tracking-wide">Accesibilidad</h4>
              
              <label className="flex items-center space-x-2 cursor-pointer">
                <input
                  type="checkbox"
                  checked={highContrast}
                  onChange={toggleHighContrast}
                  className="w-3 h-3 rounded border-color focus:ring-primary"
                />
                <span className="text-xs">Alto contraste</span>
              </label>
              
              <label className="flex items-center space-x-2 cursor-pointer">
                <input
                  type="checkbox"
                  checked={reducedMotion}
                  onChange={toggleReducedMotion}
                  className="w-3 h-3 rounded border-color focus:ring-primary"
                />
                <span className="text-xs">Reducir animaciones</span>
              </label>
            </div>
          </div>
      )}
    </div>
  );
};

export default UICustomizer;
