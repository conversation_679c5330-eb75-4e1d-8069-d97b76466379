import { useState, useCallback, useRef, useEffect } from 'react';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { diagnosisService } from '../services/diagnosis.service';
import {
  Diagnosis,
  DiagnosisSearchParams,
  DiagnosisSearchResponse,
  TransitionMapping,
  DiagnosisFilter,
  DiagnosisCategory,
  DiagnosisUsageStats,
  DiagnosisValidation,
  UseDiagnosisReturn
} from '../types/diagnosis.types';

// Configuración por defecto
const DEFAULT_CONFIG = {
  minQueryLength: 2,
  debounceMs: 300,
  maxResults: 20,
  cacheTime: 5 * 60 * 1000, // 5 minutos
};

export const useDiagnosis = (): UseDiagnosisReturn => {
  // Estados locales
  const [results, setResults] = useState<Diagnosis[]>([]);
  const [currentDiagnosis, setCurrentDiagnosis] = useState<Diagnosis | null>(null);
  const [filters, setFilters] = useState<DiagnosisFilter>({});
  const [categories, setCategories] = useState<DiagnosisCategory[]>([]);
  const [error, setError] = useState<string | null>(null);

  // Referencias para debounce
  const debounceRef = useRef<NodeJS.Timeout>();
  const queryClient = useQueryClient();

  // Query para búsqueda de diagnósticos
  const {
    data: searchResponse,
    isLoading: isSearchLoading,
    error: searchError,
    refetch: refetchSearch
  } = useQuery<DiagnosisSearchResponse>({
    queryKey: ['diagnosis-search'],
    queryFn: () => Promise.resolve({ results: [], total: 0, hasMore: false }),
    enabled: false, // Solo ejecutar manualmente
    staleTime: DEFAULT_CONFIG.cacheTime,
  });

  // Query para diagnóstico individual
  const {
    data: diagnosisData,
    isLoading: isDiagnosisLoading,
    error: diagnosisError
  } = useQuery<Diagnosis | null>({
    queryKey: ['diagnosis-detail'],
    queryFn: () => Promise.resolve(null),
    enabled: false,
    staleTime: DEFAULT_CONFIG.cacheTime,
  });

  // Mutation para búsqueda
  const searchMutation = useMutation({
    mutationFn: (params: DiagnosisSearchParams) => diagnosisService.search(params),
    onSuccess: (data) => {
      setResults(data.results);
      setError(null);
    },
    onError: (error: any) => {
      setError(error.message || 'Error al buscar diagnósticos');
      setResults([]);
    }
  });

  // Mutation para obtener diagnóstico individual
  const getDiagnosisMutation = useMutation({
    mutationFn: ({ id, version }: { id: string; version?: 'CIE-10' | 'CIE-11' }) =>
      diagnosisService.getById(id, version),
    onSuccess: (data) => {
      setCurrentDiagnosis(data);
      setError(null);
    },
    onError: (error: any) => {
      setError(error.message || 'Error al obtener diagnóstico');
      setCurrentDiagnosis(null);
    }
  });

  // Función de búsqueda con debounce
  const searchDiagnosis = useCallback(async (params: DiagnosisSearchParams) => {
    // Validar longitud mínima de consulta
    if (params.query.length < DEFAULT_CONFIG.minQueryLength) {
      setResults([]);
      return;
    }

    // Limpiar timeout anterior
    if (debounceRef.current) {
      clearTimeout(debounceRef.current);
    }

    // Aplicar debounce
    debounceRef.current = setTimeout(() => {
      // Aplicar filtros si están configurados
      const searchParams: DiagnosisSearchParams = {
        ...params,
        limit: params.limit || DEFAULT_CONFIG.maxResults,
        ...filters
      };

      searchMutation.mutate(searchParams);
    }, DEFAULT_CONFIG.debounceMs);
  }, [filters, searchMutation]);

  // Función para obtener diagnóstico individual
  const getDiagnosis = useCallback(async (id: string, version?: 'CIE-10' | 'CIE-11') => {
    getDiagnosisMutation.mutate({ id, version });
  }, [getDiagnosisMutation]);

  // Función para mapear CIE-10 a CIE-11
  const mapCie10ToCie11 = useCallback(async (cie10Code: string): Promise<TransitionMapping | null> => {
    try {
      const mapping = await diagnosisService.mapCie10ToCie11(cie10Code);
      return mapping;
    } catch (error: any) {
      setError(error.message || 'Error al mapear diagnóstico');
      return null;
    }
  }, []);

  // Función para obtener estadísticas de uso
  const getUsageStats = useCallback(async (diagnosisId: string): Promise<DiagnosisUsageStats | null> => {
    try {
      // Simulación de estadísticas (implementar con API real)
      const stats: DiagnosisUsageStats = {
        diagnosisId,
        usageCount: Math.floor(Math.random() * 1000),
        lastUsed: new Date().toISOString(),
        popularityRank: Math.floor(Math.random() * 100),
        trendDirection: ['up', 'down', 'stable'][Math.floor(Math.random() * 3)] as any
      };
      return stats;
    } catch (error: any) {
      setError(error.message || 'Error al obtener estadísticas');
      return null;
    }
  }, []);

  // Función para validar diagnóstico
  const validateDiagnosis = useCallback((diagnosis: Partial<Diagnosis>): DiagnosisValidation => {
    const errors: string[] = [];
    const warnings: string[] = [];
    const suggestions: string[] = [];

    // Validaciones básicas
    if (!diagnosis.code) {
      errors.push('El código es requerido');
    }

    if (!diagnosis.title) {
      errors.push('El título es requerido');
    }

    if (!diagnosis.source) {
      errors.push('La fuente (CIE-10 o CIE-11) es requerida');
    }

    // Validaciones de formato
    if (diagnosis.code && diagnosis.source === 'CIE-10') {
      if (!/^[A-Z]\d{2}(\.\d{1,2})?$/.test(diagnosis.code)) {
        warnings.push('El formato del código CIE-10 puede no ser válido');
      }
    }

    if (diagnosis.code && diagnosis.source === 'CIE-11') {
      if (!/^\d[A-Z]\d{2}(\.\d{1,2})?$/.test(diagnosis.code)) {
        warnings.push('El formato del código CIE-11 puede no ser válido');
      }
    }

    // Sugerencias
    if (!diagnosis.description) {
      suggestions.push('Considere agregar una descripción');
    }

    if (!diagnosis.category) {
      suggestions.push('Considere especificar una categoría');
    }

    return {
      isValid: errors.length === 0,
      errors,
      warnings,
      suggestions
    };
  }, []);

  // Función para limpiar caché
  const clearCache = useCallback(() => {
    diagnosisService.clearCache();
    queryClient.invalidateQueries({ queryKey: ['diagnosis'] });
    setResults([]);
    setCurrentDiagnosis(null);
    setError(null);
  }, [queryClient]);

  // Cargar categorías al inicializar
  useEffect(() => {
    const loadCategories = async () => {
      try {
        // Simulación de categorías (implementar con API real)
        const mockCategories: DiagnosisCategory[] = [
          {
            id: 'infectious',
            name: 'Enfermedades infecciosas y parasitarias',
            description: 'Enfermedades causadas por microorganismos',
            count: 150
          },
          {
            id: 'neoplasms',
            name: 'Neoplasias',
            description: 'Tumores benignos y malignos',
            count: 200
          },
          {
            id: 'blood',
            name: 'Enfermedades de la sangre',
            description: 'Trastornos hematológicos',
            count: 80
          },
          {
            id: 'endocrine',
            name: 'Enfermedades endocrinas',
            description: 'Trastornos del sistema endocrino',
            count: 120
          },
          {
            id: 'mental',
            name: 'Trastornos mentales',
            description: 'Enfermedades psiquiátricas y psicológicas',
            count: 180
          }
        ];
        setCategories(mockCategories);
      } catch (error) {
        console.error('Error al cargar categorías:', error);
      }
    };

    loadCategories();
  }, []);

  // Limpiar timeout al desmontar
  useEffect(() => {
    return () => {
      if (debounceRef.current) {
        clearTimeout(debounceRef.current);
      }
    };
  }, []);

  // Combinar estados de loading
  const isLoading = searchMutation.isPending || getDiagnosisMutation.isPending;

  // Combinar errores
  const combinedError = error || 
    (searchError as any)?.message || 
    (diagnosisError as any)?.message || 
    null;

  return {
    // Search functionality
    searchDiagnosis,
    results,
    isLoading,
    error: combinedError,
    
    // Single diagnosis
    getDiagnosis,
    currentDiagnosis,
    
    // Mapping functionality
    mapCie10ToCie11,
    
    // Cache management
    clearCache,
    
    // Filters and categories
    categories,
    filters,
    setFilters,
    
    // History and stats
    getUsageStats,
    
    // Validation
    validateDiagnosis
  };
};

export default useDiagnosis;
