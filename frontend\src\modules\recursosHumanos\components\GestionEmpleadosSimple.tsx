import React from 'react';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faUsers, faPlus } from '@fortawesome/free-solid-svg-icons';
import { Button } from '../../../components/ui/Button';

const GestionEmpleadosSimple: React.FC = () => {
  return (
    <div className="space-y-6">
      <div className="bg-card p-6 rounded-lg shadow-lg">
        <div className="flex justify-between items-center">
          <div>
            <h2 className="text-2xl font-bold text-primary flex items-center">
              <FontAwesomeIcon icon={faUsers} className="mr-2" />
              Gestión de Empleados
            </h2>
            <p className="text-muted mt-1">
              Administración completa del personal del hospital
            </p>
          </div>
          <Button>
            <FontAwesomeIcon icon={faPlus} className="mr-2" />
            Nuevo Empleado
          </Button>
        </div>
      </div>
      
      <div className="bg-card p-6 rounded-lg shadow-lg">
        <p className="text-center text-muted">Componente en desarrollo...</p>
      </div>
    </div>
  );
};

export default GestionEmpleadosSimple;
