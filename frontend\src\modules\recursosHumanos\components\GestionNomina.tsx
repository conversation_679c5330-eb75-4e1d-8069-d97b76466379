import React from 'react';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faMoneyBill, faPlus, faCalculator } from '@fortawesome/free-solid-svg-icons';
import { Button } from '../../../components/ui/Button';

const GestionNomina: React.FC = () => {
  return (
    <div className="space-y-6">
      <div className="bg-card p-6 rounded-lg shadow-lg">
        <div className="flex justify-between items-center">
          <div>
            <h2 className="text-2xl font-bold text-primary flex items-center">
              <FontAwesomeIcon icon={faMoneyBill} className="mr-2" />
              Gestión de Nómina
            </h2>
            <p className="text-muted mt-1">
              Liquidación y procesamiento de nómina
            </p>
          </div>
          <div className="flex gap-2">
            <Button variant="outline">
              <FontAwesomeIcon icon={faCalculator} className="mr-2" />
              Calcular Nómina
            </Button>
            <Button>
              <FontAwesomeIcon icon={faPlus} className="mr-2" />
              Nueva Liquidación
            </Button>
          </div>
        </div>
      </div>
      
      <div className="bg-card p-6 rounded-lg shadow-lg">
        <p className="text-center text-muted">Componente en desarrollo...</p>
      </div>
    </div>
  );
};

export default GestionNomina;
