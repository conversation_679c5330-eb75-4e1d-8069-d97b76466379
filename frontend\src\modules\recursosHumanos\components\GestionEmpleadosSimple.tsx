import React, { useState } from 'react';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import {
  faUsers,
  faPlus,
  faSearch,
  faEdit,
  faTrash,
  faEye,
  faUserCheck,
  faUserTimes,
  faPlane,
  faUserClock,
  faFilter,
  faFileExcel,
  faFilePdf,
  faUser,
  faPhone,
  faEnvelope,
  faMapMarkerAlt,
  faBriefcase,
  faMoneyBillWave
} from '@fortawesome/free-solid-svg-icons';
import { Button } from '../../../components/ui/Button';
import { Input } from '../../../components/ui/Input';
import { toast } from 'react-hot-toast';

// Datos mock de empleados
const mockEmpleados = [
  {
    id: 1,
    nombres: '<PERSON>',
    apellidos: '<PERSON>',
    tipo_documento: 'CC',
    numero_documento: '12345678',
    email: '<EMAIL>',
    telefono: '3001234567',
    direccion: 'Calle 123 #45-67',
    departamento: 'Medicina Interna',
    cargo: 'Médico Especialista',
    fecha_ingreso: '2020-01-15',
    tipo_contrato: 'Indefinido',
    salario_base: 8500000,
    estado: 'Activo'
  },
  {
    id: 2,
    nombres: 'María Alejandra',
    apellidos: 'López García',
    tipo_documento: 'CC',
    numero_documento: '87654321',
    email: '<EMAIL>',
    telefono: '3007654321',
    direccion: 'Carrera 89 #12-34',
    departamento: 'Enfermería',
    cargo: 'Jefe de Enfermería',
    fecha_ingreso: '2019-03-20',
    tipo_contrato: 'Indefinido',
    salario_base: 4500000,
    estado: 'Activo'
  },
  {
    id: 3,
    nombres: 'Ana María',
    apellidos: 'González Pérez',
    tipo_documento: 'CC',
    numero_documento: '11223344',
    email: '<EMAIL>',
    telefono: '3009876543',
    direccion: 'Avenida 56 #78-90',
    departamento: 'Urgencias',
    cargo: 'Médico General',
    fecha_ingreso: '2021-06-10',
    tipo_contrato: 'Fijo',
    salario_base: 6500000,
    estado: 'Vacaciones'
  },
  {
    id: 4,
    nombres: 'Luis Fernando',
    apellidos: 'Martínez Silva',
    tipo_documento: 'CC',
    numero_documento: '55667788',
    email: '<EMAIL>',
    telefono: '3005551234',
    direccion: 'Calle 90 #23-45',
    departamento: 'Cirugía',
    cargo: 'Cirujano',
    fecha_ingreso: '2018-09-05',
    tipo_contrato: 'Indefinido',
    salario_base: 12000000,
    estado: 'Activo'
  },
  {
    id: 5,
    nombres: 'Patricia',
    apellidos: 'Hernández Ruiz',
    tipo_documento: 'CC',
    numero_documento: '99887766',
    email: '<EMAIL>',
    telefono: '3002223333',
    direccion: 'Carrera 12 #67-89',
    departamento: 'Administración',
    cargo: 'Coordinadora RRHH',
    fecha_ingreso: '2017-11-12',
    tipo_contrato: 'Indefinido',
    salario_base: 5500000,
    estado: 'Licencia'
  }
];

const GestionEmpleadosSimple: React.FC = () => {
  const [empleados, setEmpleados] = useState(mockEmpleados);
  const [searchTerm, setSearchTerm] = useState('');
  const [filtroEstado, setFiltroEstado] = useState<string>('todos');
  const [filtroDepartamento, setFiltroDepartamento] = useState<string>('todos');

  const filteredEmpleados = empleados.filter(empleado => {
    const matchesSearch = empleado.nombres.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         empleado.apellidos.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         empleado.numero_documento.includes(searchTerm) ||
                         empleado.email.toLowerCase().includes(searchTerm.toLowerCase());

    const matchesEstado = filtroEstado === 'todos' || empleado.estado === filtroEstado;
    const matchesDepartamento = filtroDepartamento === 'todos' || empleado.departamento === filtroDepartamento;

    return matchesSearch && matchesEstado && matchesDepartamento;
  });

  const getEstadoColor = (estado: string) => {
    switch (estado) {
      case 'Activo':
        return 'bg-success/20 text-success';
      case 'Inactivo':
        return 'bg-error/20 text-error';
      case 'Vacaciones':
        return 'bg-warning/20 text-warning';
      case 'Licencia':
        return 'bg-info/20 text-info';
      default:
        return 'bg-secondary/20 text-secondary';
    }
  };

  const getEstadoIcon = (estado: string) => {
    switch (estado) {
      case 'Activo':
        return faUserCheck;
      case 'Inactivo':
        return faUserTimes;
      case 'Vacaciones':
        return faPlane;
      case 'Licencia':
        return faUserClock;
      default:
        return faUsers;
    }
  };

  const formatCurrency = (value: number) => {
    return new Intl.NumberFormat('es-CO', {
      style: 'currency',
      currency: 'COP',
      minimumFractionDigits: 0
    }).format(value);
  };

  const handleEdit = (empleado: any) => {
    toast.success(`Editando empleado: ${empleado.nombres} ${empleado.apellidos}`);
  };

  const handleDelete = (id: number) => {
    if (window.confirm('¿Está seguro de eliminar este empleado?')) {
      setEmpleados(prev => prev.filter(emp => emp.id !== id));
      toast.success('Empleado eliminado exitosamente');
    }
  };

  const exportarEmpleados = (formato: 'excel' | 'pdf') => {
    toast.success(`Exportando empleados en formato ${formato.toUpperCase()}`);
  };

  const totalEmpleados = empleados.length;
  const empleadosActivos = empleados.filter(e => e.estado === 'Activo').length;
  const empleadosVacaciones = empleados.filter(e => e.estado === 'Vacaciones').length;
  const empleadosLicencia = empleados.filter(e => e.estado === 'Licencia').length;

  const departamentos = [...new Set(empleados.map(e => e.departamento))];

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="bg-card p-6 rounded-lg shadow-lg">
        <div className="flex justify-between items-center">
          <div>
            <h2 className="text-2xl font-bold text-primary flex items-center">
              <FontAwesomeIcon icon={faUsers} className="mr-2" />
              Gestión de Empleados
            </h2>
            <p className="text-muted mt-1">
              Administración completa del personal del hospital
            </p>
          </div>
          <div className="flex gap-2">
            <Button variant="outline" onClick={() => exportarEmpleados('excel')}>
              <FontAwesomeIcon icon={faFileExcel} className="mr-2" />
              Excel
            </Button>
            <Button variant="outline" onClick={() => exportarEmpleados('pdf')}>
              <FontAwesomeIcon icon={faFilePdf} className="mr-2" />
              PDF
            </Button>
            <Button onClick={() => toast.info('Modal de nuevo empleado próximamente')}>
              <FontAwesomeIcon icon={faPlus} className="mr-2" />
              Nuevo Empleado
            </Button>
          </div>
        </div>
      </div>

      {/* Estadísticas Rápidas */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <div className="bg-card p-6 rounded-lg shadow">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-muted">Total Empleados</p>
              <p className="text-2xl font-bold text-primary">{totalEmpleados}</p>
            </div>
            <FontAwesomeIcon icon={faUsers} className="text-primary text-2xl" />
          </div>
        </div>

        <div className="bg-card p-6 rounded-lg shadow">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-muted">Activos</p>
              <p className="text-2xl font-bold text-success">{empleadosActivos}</p>
            </div>
            <FontAwesomeIcon icon={faUserCheck} className="text-success text-2xl" />
          </div>
        </div>

        <div className="bg-card p-6 rounded-lg shadow">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-muted">En Vacaciones</p>
              <p className="text-2xl font-bold text-warning">{empleadosVacaciones}</p>
            </div>
            <FontAwesomeIcon icon={faPlane} className="text-warning text-2xl" />
          </div>
        </div>

        <div className="bg-card p-6 rounded-lg shadow">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-muted">En Licencia</p>
              <p className="text-2xl font-bold text-info">{empleadosLicencia}</p>
            </div>
            <FontAwesomeIcon icon={faUserClock} className="text-info text-2xl" />
          </div>
        </div>
      </div>

      {/* Filtros */}
      <div className="bg-card p-6 rounded-lg shadow-lg">
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div>
            <label className="block text-sm font-medium text-secondary mb-1">
              Buscar
            </label>
            <div className="relative">
              <FontAwesomeIcon icon={faSearch} className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted" />
              <Input
                type="text"
                placeholder="Nombre, documento, email..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10"
              />
            </div>
          </div>

          <div>
            <label className="block text-sm font-medium text-secondary mb-1">
              Estado
            </label>
            <select
              value={filtroEstado}
              onChange={(e) => setFiltroEstado(e.target.value)}
              className="w-full p-3 rounded-lg bg-secondary border border-color text-primary focus:ring-2 focus:ring-primary"
            >
              <option value="todos">Todos los estados</option>
              <option value="Activo">Activo</option>
              <option value="Inactivo">Inactivo</option>
              <option value="Vacaciones">Vacaciones</option>
              <option value="Licencia">Licencia</option>
            </select>
          </div>

          <div>
            <label className="block text-sm font-medium text-secondary mb-1">
              Departamento
            </label>
            <select
              value={filtroDepartamento}
              onChange={(e) => setFiltroDepartamento(e.target.value)}
              className="w-full p-3 rounded-lg bg-secondary border border-color text-primary focus:ring-2 focus:ring-primary"
            >
              <option value="todos">Todos los departamentos</option>
              {departamentos.map((dept) => (
                <option key={dept} value={dept}>
                  {dept}
                </option>
              ))}
            </select>
          </div>
        </div>
      </div>

      {/* Tabla de Empleados */}
      <div className="bg-card rounded-lg shadow-lg overflow-hidden">
        <div className="overflow-x-auto">
          <table className="w-full">
            <thead className="bg-secondary/50">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-secondary uppercase tracking-wider">
                  Empleado
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-secondary uppercase tracking-wider">
                  Documento
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-secondary uppercase tracking-wider">
                  Cargo
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-secondary uppercase tracking-wider">
                  Departamento
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-secondary uppercase tracking-wider">
                  Contrato
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-secondary uppercase tracking-wider">
                  Salario
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-secondary uppercase tracking-wider">
                  Estado
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-secondary uppercase tracking-wider">
                  Acciones
                </th>
              </tr>
            </thead>
            <tbody className="bg-card divide-y divide-color">
              {filteredEmpleados.map((empleado) => (
                <tr key={empleado.id} className="hover:bg-secondary/20 transition-colors">
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="flex items-center">
                      <div className="flex-shrink-0 h-10 w-10">
                        <div className="h-10 w-10 rounded-full bg-primary/20 flex items-center justify-center">
                          <FontAwesomeIcon icon={faUser} className="text-primary" />
                        </div>
                      </div>
                      <div className="ml-4">
                        <div className="text-sm font-medium text-primary">
                          {empleado.nombres} {empleado.apellidos}
                        </div>
                        <div className="text-sm text-muted flex items-center">
                          <FontAwesomeIcon icon={faEnvelope} className="mr-1" />
                          {empleado.email}
                        </div>
                      </div>
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="text-sm text-primary">{empleado.tipo_documento}</div>
                    <div className="text-sm text-muted">{empleado.numero_documento}</div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="text-sm text-primary flex items-center">
                      <FontAwesomeIcon icon={faBriefcase} className="mr-2 text-muted" />
                      {empleado.cargo}
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="text-sm text-primary">
                      {empleado.departamento}
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="text-sm text-primary">{empleado.tipo_contrato}</div>
                    <div className="text-sm text-muted">
                      Desde: {new Date(empleado.fecha_ingreso).toLocaleDateString()}
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="text-sm font-medium text-primary flex items-center">
                      <FontAwesomeIcon icon={faMoneyBillWave} className="mr-2 text-muted" />
                      {formatCurrency(empleado.salario_base)}
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getEstadoColor(empleado.estado)}`}>
                      <FontAwesomeIcon icon={getEstadoIcon(empleado.estado)} className="mr-1" />
                      {empleado.estado}
                    </span>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                    <div className="flex space-x-2">
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => handleEdit(empleado)}
                        className="text-blue-600 border-blue-600 hover:bg-blue-50"
                      >
                        <FontAwesomeIcon icon={faEdit} />
                      </Button>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => toast.info(`Viendo detalles de ${empleado.nombres}`)}
                        className="text-green-600 border-green-600 hover:bg-green-50"
                      >
                        <FontAwesomeIcon icon={faEye} />
                      </Button>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => handleDelete(empleado.id)}
                        className="text-red-600 border-red-600 hover:bg-red-50"
                      >
                        <FontAwesomeIcon icon={faTrash} />
                      </Button>
                    </div>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>

        {filteredEmpleados.length === 0 && (
          <div className="text-center py-12">
            <FontAwesomeIcon icon={faUsers} className="text-muted text-4xl mb-4" />
            <p className="text-muted text-lg">No se encontraron empleados</p>
            <p className="text-muted">Intenta ajustar los filtros de búsqueda</p>
          </div>
        )}
      </div>
    </div>
  );
};

export default GestionEmpleadosSimple;
