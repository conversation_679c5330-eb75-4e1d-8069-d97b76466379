import React, { useState } from 'react';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import {
  faMoneyBill,
  faPlus,
  faCalculator,
  faSearch,
  faEye,
  faDownload,
  faCalendarAlt,
  faUsers,
  faMoneyBillWave,
  faPercent,
  faFileInvoiceDollar,
  faCheckCircle,
  faClock,
  faExclamationTriangle,
  faTimes,
  faUser,
  faFileAlt,
  faEdit
} from '@fortawesome/free-solid-svg-icons';
import { Button } from '../../../components/ui/Button';
import { Input } from '../../../components/ui/Input';
import { toast } from 'react-hot-toast';

// Datos mock de nómina
const mockNominas = [
  {
    id: 1,
    periodo: '2024-01',
    empleado_nombre: '<PERSON>',
    salario_base: 8500000,
    horas_extras: 112500,
    bonificaciones: 500000,
    deducciones: 1275000,
    salario_neto: 7837500,
    estado: 'Pagada',
    fecha_pago: '2024-01-30'
  },
  {
    id: 2,
    periodo: '2024-01',
    empleado_nombre: '<PERSON> Alejandra López',
    salario_base: 4500000,
    horas_extras: 116667,
    bonificaciones: 200000,
    deducciones: 675000,
    salario_neto: 4141667,
    estado: 'Pagada',
    fecha_pago: '2024-01-30'
  },
  {
    id: 3,
    periodo: '2024-02',
    empleado_nombre: 'Ana María González',
    salario_base: 6500000,
    horas_extras: 125000,
    bonificaciones: 300000,
    deducciones: 975000,
    salario_neto: 5950000,
    estado: 'Pendiente',
    fecha_pago: null
  },
  {
    id: 4,
    periodo: '2024-02',
    empleado_nombre: 'Luis Fernando Martínez',
    salario_base: 12000000,
    horas_extras: 600000,
    bonificaciones: 800000,
    deducciones: 1800000,
    salario_neto: 11600000,
    estado: 'Calculada',
    fecha_pago: null
  }
];

const GestionNomina: React.FC = () => {
  const [nominas, setNominas] = useState(mockNominas);
  const [searchTerm, setSearchTerm] = useState('');
  const [filtroPeriodo, setFiltroPeriodo] = useState<string>('todos');
  const [filtroEstado, setFiltroEstado] = useState<string>('todos');
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [editingNomina, setEditingNomina] = useState<any>(null);

  const filteredNominas = nominas.filter(nomina => {
    const matchesSearch = nomina.empleado_nombre.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesPeriodo = filtroPeriodo === 'todos' || nomina.periodo === filtroPeriodo;
    const matchesEstado = filtroEstado === 'todos' || nomina.estado === filtroEstado;

    return matchesSearch && matchesPeriodo && matchesEstado;
  });

  const getEstadoColor = (estado: string) => {
    switch (estado) {
      case 'Pagada':
        return 'bg-success/20 text-success';
      case 'Calculada':
        return 'bg-info/20 text-info';
      case 'Pendiente':
        return 'bg-warning/20 text-warning';
      case 'Error':
        return 'bg-error/20 text-error';
      default:
        return 'bg-secondary/20 text-secondary';
    }
  };

  const getEstadoIcon = (estado: string) => {
    switch (estado) {
      case 'Pagada':
        return faCheckCircle;
      case 'Calculada':
        return faCalculator;
      case 'Pendiente':
        return faClock;
      case 'Error':
        return faExclamationTriangle;
      default:
        return faMoneyBill;
    }
  };

  const formatCurrency = (value: number) => {
    return new Intl.NumberFormat('es-CO', {
      style: 'currency',
      currency: 'COP',
      minimumFractionDigits: 0
    }).format(value);
  };

  const handlePagar = (id: number) => {
    setNominas(prev =>
      prev.map(nomina =>
        nomina.id === id
          ? { ...nomina, estado: 'Pagada', fecha_pago: new Date().toISOString().split('T')[0] }
          : nomina
      )
    );
    toast.success('Nómina pagada exitosamente');
  };

  const calcularNomina = () => {
    toast.success('Calculando nómina del período actual...');
  };

  const exportarNomina = () => {
    toast.success('Exportando reporte de nómina...');
  };

  const totalNomina = nominas.reduce((sum, n) => sum + n.salario_neto, 0);
  const nominasPagadas = nominas.filter(n => n.estado === 'Pagada').length;
  const nominasPendientes = nominas.filter(n => n.estado === 'Pendiente').length;
  const totalPagado = nominas.filter(n => n.estado === 'Pagada').reduce((sum, n) => sum + n.salario_neto, 0);

  const periodos = [...new Set(nominas.map(n => n.periodo))];

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="bg-card p-6 rounded-lg shadow-lg">
        <div className="flex justify-between items-center">
          <div>
            <h2 className="text-2xl font-bold text-primary flex items-center">
              <FontAwesomeIcon icon={faMoneyBill} className="mr-2" />
              Gestión de Nómina
            </h2>
            <p className="text-muted mt-1">
              Liquidación y procesamiento de nómina
            </p>
          </div>
          <div className="flex gap-2">
            <Button variant="outline" onClick={exportarNomina}>
              <FontAwesomeIcon icon={faDownload} className="mr-2" />
              Exportar
            </Button>
            <Button variant="outline" onClick={calcularNomina}>
              <FontAwesomeIcon icon={faCalculator} className="mr-2" />
              Calcular Nómina
            </Button>
            <Button onClick={() => setIsModalOpen(true)}>
              <FontAwesomeIcon icon={faPlus} className="mr-2" />
              Nueva Liquidación
            </Button>
          </div>
        </div>
      </div>

      {/* Estadísticas */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <div className="bg-card p-6 rounded-lg shadow">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-muted">Total Nómina</p>
              <p className="text-xl font-bold text-primary">{formatCurrency(totalNomina)}</p>
            </div>
            <FontAwesomeIcon icon={faMoneyBillWave} className="text-primary text-2xl" />
          </div>
        </div>

        <div className="bg-card p-6 rounded-lg shadow">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-muted">Pagadas</p>
              <p className="text-2xl font-bold text-success">{nominasPagadas}</p>
            </div>
            <FontAwesomeIcon icon={faCheckCircle} className="text-success text-2xl" />
          </div>
        </div>

        <div className="bg-card p-6 rounded-lg shadow">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-muted">Pendientes</p>
              <p className="text-2xl font-bold text-warning">{nominasPendientes}</p>
            </div>
            <FontAwesomeIcon icon={faClock} className="text-warning text-2xl" />
          </div>
        </div>

        <div className="bg-card p-6 rounded-lg shadow">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-muted">Total Pagado</p>
              <p className="text-xl font-bold text-info">{formatCurrency(totalPagado)}</p>
            </div>
            <FontAwesomeIcon icon={faFileInvoiceDollar} className="text-info text-2xl" />
          </div>
        </div>
      </div>

      {/* Filtros */}
      <div className="bg-card p-6 rounded-lg shadow-lg">
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div>
            <label className="block text-sm font-medium text-secondary mb-1">
              Buscar Empleado
            </label>
            <div className="relative">
              <FontAwesomeIcon icon={faSearch} className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted" />
              <Input
                type="text"
                placeholder="Nombre del empleado..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10"
              />
            </div>
          </div>

          <div>
            <label className="block text-sm font-medium text-secondary mb-1">
              Período
            </label>
            <select
              value={filtroPeriodo}
              onChange={(e) => setFiltroPeriodo(e.target.value)}
              className="w-full p-3 rounded-lg bg-secondary border border-color text-primary focus:ring-2 focus:ring-primary"
            >
              <option value="todos">Todos los períodos</option>
              {periodos.map((periodo) => (
                <option key={periodo} value={periodo}>
                  {periodo}
                </option>
              ))}
            </select>
          </div>

          <div>
            <label className="block text-sm font-medium text-secondary mb-1">
              Estado
            </label>
            <select
              value={filtroEstado}
              onChange={(e) => setFiltroEstado(e.target.value)}
              className="w-full p-3 rounded-lg bg-secondary border border-color text-primary focus:ring-2 focus:ring-primary"
            >
              <option value="todos">Todos los estados</option>
              <option value="Pendiente">Pendiente</option>
              <option value="Calculada">Calculada</option>
              <option value="Pagada">Pagada</option>
              <option value="Error">Error</option>
            </select>
          </div>
        </div>
      </div>

      {/* Tabla de Nómina */}
      <div className="bg-card rounded-lg shadow-lg overflow-hidden">
        <div className="overflow-x-auto">
          <table className="w-full">
            <thead className="bg-secondary/50">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-secondary uppercase tracking-wider">
                  Empleado
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-secondary uppercase tracking-wider">
                  Período
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-secondary uppercase tracking-wider">
                  Salario Base
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-secondary uppercase tracking-wider">
                  Extras
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-secondary uppercase tracking-wider">
                  Deducciones
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-secondary uppercase tracking-wider">
                  Salario Neto
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-secondary uppercase tracking-wider">
                  Estado
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-secondary uppercase tracking-wider">
                  Acciones
                </th>
              </tr>
            </thead>
            <tbody className="bg-card divide-y divide-color">
              {filteredNominas.map((nomina) => (
                <tr key={nomina.id} className="hover:bg-secondary/20 transition-colors">
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="text-sm font-medium text-primary">
                      {nomina.empleado_nombre}
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="text-sm text-primary flex items-center">
                      <FontAwesomeIcon icon={faCalendarAlt} className="mr-2 text-muted" />
                      {nomina.periodo}
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="text-sm font-medium text-primary">
                      {formatCurrency(nomina.salario_base)}
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="text-sm text-primary">
                      {formatCurrency(nomina.horas_extras + nomina.bonificaciones)}
                    </div>
                    <div className="text-xs text-muted">
                      HE: {formatCurrency(nomina.horas_extras)}
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="text-sm text-error">
                      -{formatCurrency(nomina.deducciones)}
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="text-sm font-bold text-success">
                      {formatCurrency(nomina.salario_neto)}
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getEstadoColor(nomina.estado)}`}>
                      <FontAwesomeIcon icon={getEstadoIcon(nomina.estado)} className="mr-1" />
                      {nomina.estado}
                    </span>
                    {nomina.fecha_pago && (
                      <div className="text-xs text-muted mt-1">
                        Pagado: {new Date(nomina.fecha_pago).toLocaleDateString()}
                      </div>
                    )}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                    <div className="flex space-x-2">
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => toast.success(`Viendo detalles de nómina de ${nomina.empleado_nombre}`)}
                        className="text-blue-600 border-blue-600 hover:bg-blue-50"
                      >
                        <FontAwesomeIcon icon={faEye} />
                      </Button>
                      {nomina.estado === 'Calculada' && (
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => handlePagar(nomina.id)}
                          className="text-green-600 border-green-600 hover:bg-green-50"
                        >
                          <FontAwesomeIcon icon={faMoneyBillWave} />
                        </Button>
                      )}
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => toast.success(`Descargando comprobante de ${nomina.empleado_nombre}`)}
                        className="text-purple-600 border-purple-600 hover:bg-purple-50"
                      >
                        <FontAwesomeIcon icon={faDownload} />
                      </Button>
                    </div>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>

        {filteredNominas.length === 0 && (
          <div className="text-center py-12">
            <FontAwesomeIcon icon={faMoneyBill} className="text-muted text-4xl mb-4" />
            <p className="text-muted text-lg">No se encontraron registros de nómina</p>
            <p className="text-muted">Intenta ajustar los filtros de búsqueda</p>
          </div>
        )}
      </div>

      {/* Modal Funcional */}
      {isModalOpen && (
        <div className="fixed inset-0 bg-black/60 backdrop-blur-sm flex items-center justify-center z-50 p-4 animate-in fade-in duration-300">
          <div className="glass-modal modal-container max-h-[90vh] overflow-y-auto custom-scrollbar animate-in slide-in-from-bottom-4 duration-300">
            <div className="p-6">
              <div className="flex justify-between items-center mb-6">
                <h3 className="text-xl font-semibold text-primary flex items-center">
                  <FontAwesomeIcon icon={faMoneyBill} className="mr-2" />
                  {editingNomina ? 'Editar Liquidación' : 'Nueva Liquidación'}
                </h3>
                <button
                  onClick={() => {
                    setIsModalOpen(false);
                    setEditingNomina(null);
                  }}
                  className="text-muted hover:text-primary"
                >
                  <FontAwesomeIcon icon={faTimes} className="text-xl" />
                </button>
              </div>

              <form onSubmit={(e) => {
                e.preventDefault();
                const formData = new FormData(e.target as HTMLFormElement);
                const nuevaLiquidacion = {
                  id: editingNomina ? editingNomina.id : Date.now(),
                  empleado_nombre: formData.get('empleado_nombre') as string,
                  periodo: formData.get('periodo') as string,
                  salario_base: parseInt(formData.get('salario_base') as string),
                  horas_extras: parseInt(formData.get('horas_extras') as string) || 0,
                  bonificaciones: parseInt(formData.get('bonificaciones') as string) || 0,
                  deducciones: parseInt(formData.get('deducciones') as string) || 0,
                  salario_neto: 0, // Se calculará automáticamente
                  estado: 'Calculada',
                  fecha_pago: null
                };

                // Calcular salario neto
                nuevaLiquidacion.salario_neto = nuevaLiquidacion.salario_base +
                  nuevaLiquidacion.horas_extras +
                  nuevaLiquidacion.bonificaciones -
                  nuevaLiquidacion.deducciones;

                if (editingNomina) {
                  setNominas(prev => prev.map(n => n.id === editingNomina.id ? nuevaLiquidacion : n));
                  toast.success('Liquidación actualizada exitosamente');
                } else {
                  setNominas(prev => [...prev, nuevaLiquidacion]);
                  toast.success('Liquidación creada exitosamente');
                }

                setIsModalOpen(false);
                setEditingNomina(null);
              }}>
                <div className="form-grid">
                  {/* Empleado */}
                  <div className="form-grid-full">
                    <label className="block text-sm font-medium text-secondary mb-2">
                      <FontAwesomeIcon icon={faUser} className="mr-2" />
                      Empleado *
                    </label>
                    <Input
                      name="empleado_nombre"
                      type="text"
                      required
                      defaultValue={editingNomina?.empleado_nombre || ''}
                      placeholder="Nombre completo del empleado"
                      className="w-full"
                    />
                  </div>

                  {/* Período */}
                  <div>
                    <label className="block text-sm font-medium text-secondary mb-2">
                      <FontAwesomeIcon icon={faCalendarAlt} className="mr-2" />
                      Período *
                    </label>
                    <Input
                      name="periodo"
                      type="month"
                      required
                      defaultValue={editingNomina?.periodo || ''}
                      className="w-full"
                    />
                  </div>

                  {/* Salario Base */}
                  <div>
                    <label className="block text-sm font-medium text-secondary mb-2">
                      <FontAwesomeIcon icon={faMoneyBillWave} className="mr-2" />
                      Salario Base *
                    </label>
                    <Input
                      name="salario_base"
                      type="number"
                      required
                      min="0"
                      step="1000"
                      defaultValue={editingNomina?.salario_base || ''}
                      placeholder="Ej: 5000000"
                      className="w-full"
                    />
                  </div>

                  {/* Horas Extras */}
                  <div>
                    <label className="block text-sm font-medium text-secondary mb-2">
                      <FontAwesomeIcon icon={faClock} className="mr-2" />
                      Horas Extras
                    </label>
                    <Input
                      name="horas_extras"
                      type="number"
                      min="0"
                      step="1000"
                      defaultValue={editingNomina?.horas_extras || '0'}
                      placeholder="Ej: 150000"
                      className="w-full"
                    />
                  </div>

                  {/* Bonificaciones */}
                  <div>
                    <label className="block text-sm font-medium text-secondary mb-2">
                      <FontAwesomeIcon icon={faCheckCircle} className="mr-2" />
                      Bonificaciones
                    </label>
                    <Input
                      name="bonificaciones"
                      type="number"
                      min="0"
                      step="1000"
                      defaultValue={editingNomina?.bonificaciones || '0'}
                      placeholder="Ej: 200000"
                      className="w-full"
                    />
                  </div>

                  {/* Deducciones */}
                  <div>
                    <label className="block text-sm font-medium text-secondary mb-2">
                      <FontAwesomeIcon icon={faExclamationTriangle} className="mr-2" />
                      Deducciones
                    </label>
                    <Input
                      name="deducciones"
                      type="number"
                      min="0"
                      step="1000"
                      defaultValue={editingNomina?.deducciones || '0'}
                      placeholder="Ej: 750000"
                      className="w-full"
                    />
                  </div>

                  {/* Observaciones */}
                  <div className="md:col-span-2">
                    <label className="block text-sm font-medium text-secondary mb-2">
                      <FontAwesomeIcon icon={faFileAlt} className="mr-2" />
                      Observaciones
                    </label>
                    <textarea
                      name="observaciones"
                      rows={3}
                      placeholder="Observaciones adicionales sobre la liquidación..."
                      className="w-full p-3 rounded-lg bg-secondary border border-color text-primary focus:ring-2 focus:ring-primary resize-none"
                    />
                  </div>
                </div>

                <div className="flex justify-end gap-3 mt-6">
                  <Button
                    type="button"
                    variant="outline"
                    onClick={() => {
                      setIsModalOpen(false);
                      setEditingNomina(null);
                    }}
                  >
                    Cancelar
                  </Button>
                  <Button type="submit">
                    <FontAwesomeIcon icon={editingNomina ? faEdit : faPlus} className="mr-2" />
                    {editingNomina ? 'Actualizar' : 'Crear'} Liquidación
                  </Button>
                </div>
              </form>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default GestionNomina;
