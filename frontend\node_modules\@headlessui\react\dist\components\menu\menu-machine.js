var h=Object.defineProperty;var y=(e,n,t)=>n in e?h(e,n,{enumerable:!0,configurable:!0,writable:!0,value:t}):e[n]=t;var v=(e,n,t)=>(y(e,typeof n!="symbol"?n+"":n,t),t);import{Machine as R,batch as p}from'../../machine.js';import{Focus as c,calculateActiveIndex as f}from'../../utils/calculate-active-index.js';import{sortByDomNode as A}from'../../utils/focus-management.js';import{match as E}from'../../utils/match.js';var M=(t=>(t[t.Open=0]="Open",t[t.Closed=1]="Closed",t))(M||{}),T=(t=>(t[t.Pointer=0]="Pointer",t[t.Other=1]="Other",t))(T||{}),b=(i=>(i[i.OpenMenu=0]="OpenMenu",i[i.CloseMenu=1]="CloseMenu",i[i.GoToItem=2]="GoToItem",i[i.Search=3]="Search",i[i.ClearSearch=4]="ClearSearch",i[i.RegisterItems=5]="RegisterItems",i[i.UnregisterItems=6]="UnregisterItems",i[i.SetButtonElement=7]="SetButtonElement",i[i.SetItemsElement=8]="SetItemsElement",i[i.SortItems=9]="SortItems",i))(b||{});function S(e,n=t=>t){let t=e.activeItemIndex!==null?e.items[e.activeItemIndex]:null,r=A(n(e.items.slice()),u=>u.dataRef.current.domRef.current),l=t?r.indexOf(t):null;return l===-1&&(l=null),{items:r,activeItemIndex:l}}let F={[1](e){return e.menuState===1?e:{...e,activeItemIndex:null,pendingFocus:{focus:c.Nothing},menuState:1}},[0](e,n){return e.menuState===0?e:{...e,__demoMode:!1,pendingFocus:n.focus,menuState:0}},[2]:(e,n)=>{var u,m,d,a,I;if(e.menuState===1)return e;let t={...e,searchQuery:"",activationTrigger:(u=n.trigger)!=null?u:1,__demoMode:!1};if(n.focus===c.Nothing)return{...t,activeItemIndex:null};if(n.focus===c.Specific)return{...t,activeItemIndex:e.items.findIndex(i=>i.id===n.id)};if(n.focus===c.Previous){let i=e.activeItemIndex;if(i!==null){let g=e.items[i].dataRef.current.domRef,o=f(n,{resolveItems:()=>e.items,resolveActiveIndex:()=>e.activeItemIndex,resolveId:s=>s.id,resolveDisabled:s=>s.dataRef.current.disabled});if(o!==null){let s=e.items[o].dataRef.current.domRef;if(((m=g.current)==null?void 0:m.previousElementSibling)===s.current||((d=s.current)==null?void 0:d.previousElementSibling)===null)return{...t,activeItemIndex:o}}}}else if(n.focus===c.Next){let i=e.activeItemIndex;if(i!==null){let g=e.items[i].dataRef.current.domRef,o=f(n,{resolveItems:()=>e.items,resolveActiveIndex:()=>e.activeItemIndex,resolveId:s=>s.id,resolveDisabled:s=>s.dataRef.current.disabled});if(o!==null){let s=e.items[o].dataRef.current.domRef;if(((a=g.current)==null?void 0:a.nextElementSibling)===s.current||((I=s.current)==null?void 0:I.nextElementSibling)===null)return{...t,activeItemIndex:o}}}}let r=S(e),l=f(n,{resolveItems:()=>r.items,resolveActiveIndex:()=>r.activeItemIndex,resolveId:i=>i.id,resolveDisabled:i=>i.dataRef.current.disabled});return{...t,...r,activeItemIndex:l}},[3]:(e,n)=>{let r=e.searchQuery!==""?0:1,l=e.searchQuery+n.value.toLowerCase(),m=(e.activeItemIndex!==null?e.items.slice(e.activeItemIndex+r).concat(e.items.slice(0,e.activeItemIndex+r)):e.items).find(a=>{var I;return((I=a.dataRef.current.textValue)==null?void 0:I.startsWith(l))&&!a.dataRef.current.disabled}),d=m?e.items.indexOf(m):-1;return d===-1||d===e.activeItemIndex?{...e,searchQuery:l}:{...e,searchQuery:l,activeItemIndex:d,activationTrigger:1}},[4](e){return e.searchQuery===""?e:{...e,searchQuery:"",searchActiveItemIndex:null}},[5]:(e,n)=>{let t=e.items.concat(n.items.map(l=>l)),r=e.activeItemIndex;return e.pendingFocus.focus!==c.Nothing&&(r=f(e.pendingFocus,{resolveItems:()=>t,resolveActiveIndex:()=>e.activeItemIndex,resolveId:l=>l.id,resolveDisabled:l=>l.dataRef.current.disabled})),{...e,items:t,activeItemIndex:r,pendingFocus:{focus:c.Nothing},pendingShouldSort:!0}},[6]:(e,n)=>{let t=e.items,r=[],l=new Set(n.items);for(let[u,m]of t.entries())if(l.has(m.id)&&(r.push(u),l.delete(m.id),l.size===0))break;if(r.length>0){t=t.slice();for(let u of r.reverse())t.splice(u,1)}return{...e,items:t,activationTrigger:1}},[7]:(e,n)=>e.buttonElement===n.element?e:{...e,buttonElement:n.element},[8]:(e,n)=>e.itemsElement===n.element?e:{...e,itemsElement:n.element},[9]:e=>e.pendingShouldSort?{...e,...S(e),pendingShouldSort:!1}:e};class x extends R{constructor(t){super(t);v(this,"actions",{registerItem:p(()=>{let t=[],r=new Set;return[(l,u)=>{r.has(u)||(r.add(u),t.push({id:l,dataRef:u}))},()=>(r.clear(),this.send({type:5,items:t.splice(0)}))]}),unregisterItem:p(()=>{let t=[];return[r=>t.push(r),()=>this.send({type:6,items:t.splice(0)})]})});v(this,"selectors",{activeDescendantId(t){var u;let r=t.activeItemIndex,l=t.items;return r===null||(u=l[r])==null?void 0:u.id},isActive(t,r){var m;let l=t.activeItemIndex,u=t.items;return l!==null?((m=u[l])==null?void 0:m.id)===r:!1},shouldScrollIntoView(t,r){return t.__demoMode||t.menuState!==0||t.activationTrigger===0?!1:this.isActive(t,r)}});this.on(5,()=>{requestAnimationFrame(()=>{this.send({type:9})})})}static new({__demoMode:t=!1}={}){return new x({__demoMode:t,menuState:t?0:1,buttonElement:null,itemsElement:null,items:[],searchQuery:"",activeItemIndex:null,activationTrigger:1,pendingShouldSort:!1,pendingFocus:{focus:c.Nothing}})}reduce(t,r){return E(r.type,F,t,r)}}export{b as ActionTypes,T as ActivationTrigger,x as MenuMachine,M as MenuState};
