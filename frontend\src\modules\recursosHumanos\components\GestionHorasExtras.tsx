import React, { useState } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import {
  faClock,
  faPlus,
  faSearch,
  faEdit,
  faTrash,
  faEye,
  faCheckCircle,
  faTimesCircle,
  faExclamationTriangle,
  faMoneyBillWave,
  faCalendarAlt,
  faFilter,
  faUserClock,
  faPercent,
  faChartBar
} from '@fortawesome/free-solid-svg-icons';
import { Button } from '../../../components/ui/Button';
import { Input } from '../../../components/ui/Input';
import { toast } from 'react-hot-toast';
import { useAuth } from '../../../services/authService';
import { HoraExtra, HoraExtraFormData } from '../../../types/recursosHumanos';

// Schema de validación
const horaExtraSchema = z.object({
  empleado_id: z.number().min(1, 'Debe seleccionar un empleado'),
  fecha: z.string().min(1, 'Fecha requerida'),
  hora_inicio: z.string().min(1, 'Hora de inicio requerida'),
  hora_fin: z.string().min(1, 'Hora de fin requerida'),
  tipo_hora: z.enum(['Diurna', 'Nocturna', 'Dominical', 'Festiva']),
  motivo: z.string().min(1, 'Motivo requerido'),
  observaciones: z.string().optional(),
});

// Datos mock
const mockHorasExtras: HoraExtra[] = [
  {
    id: 1,
    hospital_id: 1,
    empleado_id: 1,
    empleado_nombre: 'Carlos Andrés Rodríguez',
    fecha: '2024-01-15',
    hora_inicio: '18:00',
    hora_fin: '22:00',
    total_horas: 4,
    tipo_hora: 'Nocturna',
    factor_multiplicador: 1.35,
    valor_hora: 20833,
    valor_total: 112500,
    motivo: 'Emergencia médica',
    aprobado_por: 2,
    aprobado_por_nombre: 'Dr. María López',
    estado: 'Aprobada',
    observaciones: 'Atención de emergencia en urgencias',
    created_at: '2024-01-15T18:00:00Z',
    updated_at: '2024-01-16T08:00:00Z'
  },
  {
    id: 2,
    hospital_id: 1,
    empleado_id: 2,
    empleado_nombre: 'María Alejandra López',
    fecha: '2024-01-20',
    hora_inicio: '14:00',
    hora_fin: '18:00',
    total_horas: 4,
    tipo_hora: 'Dominical',
    factor_multiplicador: 1.75,
    valor_hora: 16667,
    valor_total: 116667,
    motivo: 'Cobertura de turno dominical',
    estado: 'Pendiente',
    observaciones: 'Cobertura por ausencia de colega',
    created_at: '2024-01-20T14:00:00Z',
    updated_at: '2024-01-20T14:00:00Z'
  },
  {
    id: 3,
    hospital_id: 1,
    empleado_id: 3,
    empleado_nombre: 'Ana María González',
    fecha: '2024-01-18',
    hora_inicio: '08:00',
    hora_fin: '12:00',
    total_horas: 4,
    tipo_hora: 'Diurna',
    factor_multiplicador: 1.25,
    valor_hora: 25000,
    valor_total: 125000,
    motivo: 'Proyecto especial',
    aprobado_por: 1,
    aprobado_por_nombre: 'Dr. Carlos Rodríguez',
    estado: 'Pagada',
    observaciones: 'Trabajo en proyecto de mejora de procesos',
    created_at: '2024-01-18T08:00:00Z',
    updated_at: '2024-01-25T10:00:00Z'
  }
];

const mockEmpleados = [
  { id: 1, nombre: 'Carlos Andrés Rodríguez' },
  { id: 2, nombre: 'María Alejandra López' },
  { id: 3, nombre: 'Ana María González' },
  { id: 4, nombre: 'Luis Fernando Martínez' },
  { id: 5, nombre: 'Patricia Hernández' }
];

const GestionHorasExtras: React.FC = () => {
  const [horasExtras, setHorasExtras] = useState<HoraExtra[]>(mockHorasExtras);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [editingHoraExtra, setEditingHoraExtra] = useState<HoraExtra | null>(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [filtroEstado, setFiltroEstado] = useState<string>('todos');
  const [filtroTipo, setFiltroTipo] = useState<string>('todos');
  const { user } = useAuth();

  const {
    register,
    handleSubmit,
    formState: { errors },
    reset,
    setValue,
    watch
  } = useForm<HoraExtraFormData>({
    resolver: zodResolver(horaExtraSchema),
    defaultValues: {
      hospital_id: user?.hospital_id || 1,
    }
  });

  const horaInicio = watch('hora_inicio');
  const horaFin = watch('hora_fin');
  const tipoHora = watch('tipo_hora');

  // Calcular horas automáticamente
  const calcularHoras = (inicio: string, fin: string): number => {
    if (!inicio || !fin) return 0;
    
    const [horaIni, minIni] = inicio.split(':').map(Number);
    const [horaFin, minFin] = fin.split(':').map(Number);
    
    const inicioMinutos = horaIni * 60 + minIni;
    const finMinutos = horaFin * 60 + minFin;
    
    let diferencia = finMinutos - inicioMinutos;
    if (diferencia < 0) diferencia += 24 * 60; // Cruzó medianoche
    
    return diferencia / 60;
  };

  const getFactorMultiplicador = (tipo: string): number => {
    switch (tipo) {
      case 'Diurna': return 1.25;
      case 'Nocturna': return 1.35;
      case 'Dominical': return 1.75;
      case 'Festiva': return 2.0;
      default: return 1.25;
    }
  };

  const onSubmit = async (data: HoraExtraFormData) => {
    try {
      const totalHoras = calcularHoras(data.hora_inicio, data.hora_fin);
      const factor = getFactorMultiplicador(data.tipo_hora);
      
      // Calcular valores (simulado - obtener salario real del empleado)
      const salarioBase = 5000000; // Obtener del empleado
      const valorHoraNormal = salarioBase / 240; // 240 horas mensuales
      const valorHora = valorHoraNormal * factor;
      const valorTotal = valorHora * totalHoras;

      if (editingHoraExtra) {
        // Actualizar hora extra existente
        const updatedHoraExtra: HoraExtra = {
          ...editingHoraExtra,
          ...data,
          total_horas: totalHoras,
          factor_multiplicador: factor,
          valor_hora: valorHora,
          valor_total: valorTotal,
          updated_at: new Date().toISOString()
        };
        
        setHorasExtras(prev => 
          prev.map(he => he.id === editingHoraExtra.id ? updatedHoraExtra : he)
        );
        toast.success('Hora extra actualizada exitosamente');
      } else {
        // Crear nueva hora extra
        const newHoraExtra: HoraExtra = {
          id: Math.max(...horasExtras.map(he => he.id)) + 1,
          ...data,
          empleado_nombre: mockEmpleados.find(e => e.id === data.empleado_id)?.nombre || '',
          total_horas: totalHoras,
          factor_multiplicador: factor,
          valor_hora: valorHora,
          valor_total: valorTotal,
          estado: 'Pendiente',
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        };
        
        setHorasExtras(prev => [...prev, newHoraExtra]);
        toast.success('Hora extra registrada exitosamente');
      }
      
      setIsModalOpen(false);
      setEditingHoraExtra(null);
      reset();
    } catch (error) {
      toast.error('Error al procesar la hora extra');
    }
  };

  const handleEdit = (horaExtra: HoraExtra) => {
    setEditingHoraExtra(horaExtra);
    setValue('empleado_id', horaExtra.empleado_id);
    setValue('fecha', horaExtra.fecha);
    setValue('hora_inicio', horaExtra.hora_inicio);
    setValue('hora_fin', horaExtra.hora_fin);
    setValue('tipo_hora', horaExtra.tipo_hora);
    setValue('motivo', horaExtra.motivo);
    setValue('observaciones', horaExtra.observaciones || '');
    setIsModalOpen(true);
  };

  const handleDelete = (id: number) => {
    if (window.confirm('¿Está seguro de eliminar esta hora extra?')) {
      setHorasExtras(prev => prev.filter(he => he.id !== id));
      toast.success('Hora extra eliminada exitosamente');
    }
  };

  const handleApprove = (id: number) => {
    setHorasExtras(prev => 
      prev.map(he => 
        he.id === id 
          ? { ...he, estado: 'Aprobada', aprobado_por: user?.id, aprobado_por_nombre: user?.nombre }
          : he
      )
    );
    toast.success('Hora extra aprobada exitosamente');
  };

  const handleReject = (id: number) => {
    setHorasExtras(prev => 
      prev.map(he => 
        he.id === id 
          ? { ...he, estado: 'Rechazada' }
          : he
      )
    );
    toast.success('Hora extra rechazada');
  };

  const filteredHorasExtras = horasExtras.filter(horaExtra => {
    const matchesSearch = horaExtra.empleado_nombre?.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         horaExtra.motivo.toLowerCase().includes(searchTerm.toLowerCase());
    
    const matchesEstado = filtroEstado === 'todos' || horaExtra.estado === filtroEstado;
    const matchesTipo = filtroTipo === 'todos' || horaExtra.tipo_hora === filtroTipo;
    
    return matchesSearch && matchesEstado && matchesTipo;
  });

  const getEstadoColor = (estado: string) => {
    switch (estado) {
      case 'Aprobada':
        return 'bg-success/20 text-success';
      case 'Pendiente':
        return 'bg-warning/20 text-warning';
      case 'Rechazada':
        return 'bg-error/20 text-error';
      case 'Pagada':
        return 'bg-info/20 text-info';
      default:
        return 'bg-secondary/20 text-secondary';
    }
  };

  const getEstadoIcon = (estado: string) => {
    switch (estado) {
      case 'Aprobada':
        return faCheckCircle;
      case 'Pendiente':
        return faClock;
      case 'Rechazada':
        return faTimesCircle;
      case 'Pagada':
        return faMoneyBillWave;
      default:
        return faExclamationTriangle;
    }
  };

  const getTipoColor = (tipo: string) => {
    switch (tipo) {
      case 'Diurna':
        return 'text-blue-600';
      case 'Nocturna':
        return 'text-purple-600';
      case 'Dominical':
        return 'text-orange-600';
      case 'Festiva':
        return 'text-red-600';
      default:
        return 'text-gray-600';
    }
  };

  const formatCurrency = (value: number) => {
    return new Intl.NumberFormat('es-CO', {
      style: 'currency',
      currency: 'COP',
      minimumFractionDigits: 0
    }).format(value);
  };

  const totalHorasExtras = horasExtras.reduce((sum, he) => sum + he.total_horas, 0);
  const horasAprobadas = horasExtras.filter(he => he.estado === 'Aprobada').reduce((sum, he) => sum + he.total_horas, 0);
  const horasPendientes = horasExtras.filter(he => he.estado === 'Pendiente').reduce((sum, he) => sum + he.total_horas, 0);
  const valorTotal = horasExtras.reduce((sum, he) => sum + he.valor_total, 0);

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="bg-card p-6 rounded-lg shadow-lg">
        <div className="flex justify-between items-center">
          <div>
            <h2 className="text-2xl font-bold text-primary flex items-center">
              <FontAwesomeIcon icon={faClock} className="mr-2" />
              Gestión de Horas Extras
            </h2>
            <p className="text-muted mt-1">
              Control y administración de horas extras del personal
            </p>
          </div>
          <Button onClick={() => setIsModalOpen(true)}>
            <FontAwesomeIcon icon={faPlus} className="mr-2" />
            Registrar Horas Extras
          </Button>
        </div>
      </div>

      {/* Estadísticas */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <div className="bg-card p-6 rounded-lg shadow">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-muted">Total Horas</p>
              <p className="text-2xl font-bold text-primary">{totalHorasExtras}</p>
            </div>
            <FontAwesomeIcon icon={faUserClock} className="text-primary text-2xl" />
          </div>
        </div>

        <div className="bg-card p-6 rounded-lg shadow">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-muted">Aprobadas</p>
              <p className="text-2xl font-bold text-success">{horasAprobadas}</p>
            </div>
            <FontAwesomeIcon icon={faCheckCircle} className="text-success text-2xl" />
          </div>
        </div>

        <div className="bg-card p-6 rounded-lg shadow">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-muted">Pendientes</p>
              <p className="text-2xl font-bold text-warning">{horasPendientes}</p>
            </div>
            <FontAwesomeIcon icon={faClock} className="text-warning text-2xl" />
          </div>
        </div>

        <div className="bg-card p-6 rounded-lg shadow">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-muted">Valor Total</p>
              <p className="text-xl font-bold text-info">{formatCurrency(valorTotal)}</p>
            </div>
            <FontAwesomeIcon icon={faMoneyBillWave} className="text-info text-2xl" />
          </div>
        </div>
      </div>
