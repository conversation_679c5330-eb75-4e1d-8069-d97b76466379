import f,{createContext as M,useContext as d,useState as H}from"react";import{Hidden as E,HiddenFeatures as T}from'../internal/hidden.js';import{getOwnerDocument as L}from'../utils/owner.js';import{useEvent as s}from'./use-event.js';import{useOwnerDocument as h}from'./use-owner.js';function R({defaultContainers:l=[],portals:n,mainTreeNode:o}={}){let r=h(o),u=s(()=>{var i,c;let t=[];for(let e of l)e!==null&&(e instanceof HTMLElement?t.push(e):"current"in e&&e.current instanceof HTMLElement&&t.push(e.current));if(n!=null&&n.current)for(let e of n.current)t.push(e);for(let e of(i=r==null?void 0:r.querySelectorAll("html > *, body > *"))!=null?i:[])e!==document.body&&e!==document.head&&e instanceof HTMLElement&&e.id!=="headlessui-portal-root"&&(o&&(e.contains(o)||e.contains((c=o==null?void 0:o.getRootNode())==null?void 0:c.host))||t.some(m=>e.contains(m))||t.push(e));return t});return{resolveContainers:u,contains:s(t=>u().some(i=>i.contains(t)))}}let a=M(null);function O({children:l,node:n}){let[o,r]=H(null),u=b(n!=null?n:o);return f.createElement(a.Provider,{value:u},l,u===null&&f.createElement(E,{features:T.Hidden,ref:t=>{var i,c;if(t){for(let e of(c=(i=L(t))==null?void 0:i.querySelectorAll("html > *, body > *"))!=null?c:[])if(e!==document.body&&e!==document.head&&e instanceof HTMLElement&&e!=null&&e.contains(t)){r(e);break}}}}))}function b(l=null){var n;return(n=d(a))!=null?n:l}export{O as MainTreeProvider,b as useMainTreeNode,R as useRootContainers};
