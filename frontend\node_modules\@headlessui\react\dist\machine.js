var f=(t,e,r)=>{if(!e.has(t))throw TypeError("Cannot "+r)};var a=(t,e,r)=>(f(t,e,"read from private field"),r?r.call(t):e.get(t)),l=(t,e,r)=>{if(e.has(t))throw TypeError("Cannot add the same private member more than once");e instanceof WeakSet?e.add(t):e.set(t,r)},c=(t,e,r,n)=>(f(t,e,"write to private field"),n?n.call(t,r):e.set(t,r),r);var i,s,o;import{DefaultMap as d}from'./utils/default-map.js';import{disposables as y}from'./utils/disposables.js';class m{constructor(e){l(this,i,{});l(this,s,new d(()=>new Set));l(this,o,new Set);c(this,i,e)}get state(){return a(this,i)}subscribe(e,r){let n={selector:e,callback:r,current:e(a(this,i))};return a(this,o).add(n),()=>{a(this,o).delete(n)}}on(e,r){return a(this,s).get(e).add(r),()=>{a(this,s).get(e).delete(r)}}send(e){c(this,i,this.reduce(a(this,i),e));for(let r of a(this,o)){let n=r.selector(a(this,i));h(r.current,n)||(r.current=n,r.callback(n))}for(let r of a(this,s).get(e.type))r(a(this,i),e)}}i=new WeakMap,s=new WeakMap,o=new WeakMap;function h(t,e){return Object.is(t,e)?!0:typeof t!="object"||t===null||typeof e!="object"||e===null?!1:Array.isArray(t)&&Array.isArray(e)?t.length!==e.length?!1:u(t[Symbol.iterator](),e[Symbol.iterator]()):t instanceof Map&&e instanceof Map||t instanceof Set&&e instanceof Set?t.size!==e.size?!1:u(t.entries(),e.entries()):S(t)&&S(e)?u(Object.entries(t)[Symbol.iterator](),Object.entries(e)[Symbol.iterator]()):!1}function u(t,e){do{let r=t.next(),n=e.next();if(r.done&&n.done)return!0;if(r.done||n.done||!Object.is(r.value,n.value))return!1}while(!0)}function S(t){if(Object.prototype.toString.call(t)!=="[object Object]")return!1;let e=Object.getPrototypeOf(t);return e===null||Object.getPrototypeOf(e)===null}function g(t){let[e,r]=t(),n=y();return(...b)=>{e(...b),n.dispose(),n.microTask(r)}}export{m as Machine,g as batch,h as shallowEqual};
