"use client";import{useFocusRing as ve}from"@react-aria/focus";import{useHover as he}from"@react-aria/interactions";import F,{Fragment as pe,createContext as ue,useCallback as re,useContext as de,useEffect as De,useMemo as H,useRef as le,useState as Se}from"react";import{flushSync as G}from"react-dom";import{useActivePress as Ae}from'../../hooks/use-active-press.js';import{useByComparator as _e}from'../../hooks/use-by-comparator.js';import{useControllable as Re}from'../../hooks/use-controllable.js';import{useDefaultValue as Fe}from'../../hooks/use-default-value.js';import{useDidElementMove as Ce}from'../../hooks/use-did-element-move.js';import{useDisposables as Me}from'../../hooks/use-disposables.js';import{useElementSize as we}from'../../hooks/use-element-size.js';import{useEvent as C}from'../../hooks/use-event.js';import{useId as ae}from'../../hooks/use-id.js';import{useInertOthers as Be}from'../../hooks/use-inert-others.js';import{useIsoMorphicEffect as ie}from'../../hooks/use-iso-morphic-effect.js';import{useLatestValue as Ie}from'../../hooks/use-latest-value.js';import{useOnDisappear as Ue}from'../../hooks/use-on-disappear.js';import{useOutsideClick as ke}from'../../hooks/use-outside-click.js';import{useOwnerDocument as ce}from'../../hooks/use-owner.js';import{useResolveButtonType as Ne}from'../../hooks/use-resolve-button-type.js';import{useScrollLock as He}from'../../hooks/use-scroll-lock.js';import{useSyncRefs as j}from'../../hooks/use-sync-refs.js';import{useTextValue as Ge}from'../../hooks/use-text-value.js';import{useTrackedPointer as Ve}from'../../hooks/use-tracked-pointer.js';import{transitionDataAttributes as Ke,useTransition as ze}from'../../hooks/use-transition.js';import{useDisabled as We}from'../../internal/disabled.js';import{FloatingProvider as Xe,useFloatingPanel as je,useFloatingPanelProps as Je,useFloatingReference as $e,useFloatingReferenceProps as qe,useResolvedAnchor as Qe}from'../../internal/floating.js';import{FormFields as Ye}from'../../internal/form-fields.js';import{useFrozenData as Ze}from'../../internal/frozen.js';import{useProvidedId as et}from'../../internal/id.js';import{OpenClosedProvider as tt,State as Q,useOpenClosed as ot}from'../../internal/open-closed.js';import{useSlice as M}from'../../react-glue.js';import{isDisabledReactIssue7711 as nt}from'../../utils/bugs.js';import{Focus as w}from'../../utils/calculate-active-index.js';import{disposables as rt}from'../../utils/disposables.js';import{Focus as fe,FocusableMode as lt,focusFrom as at,isFocusableElement as it}from'../../utils/focus-management.js';import{attemptSubmit as st}from'../../utils/form.js';import{match as Y}from'../../utils/match.js';import{getOwnerDocument as pt}from'../../utils/owner.js';import{RenderFeatures as Te,forwardRefWithAs as J,mergeProps as be,useRender as $}from'../../utils/render.js';import{useDescribedBy as ut}from'../description/description.js';import{Keys as c}from'../keyboard.js';import{Label as dt,useLabelledBy as ct,useLabels as ft}from'../label/label.js';import{Portal as Tt}from'../portal/portal.js';import{ActionTypes as bt,ActivationTrigger as me,ListboxStates as f,ValueMode as N}from'./listbox-machine.js';import{ListboxContext as mt,useListboxMachine as yt,useListboxMachineContext as se}from'./listbox-machine-glue.js';let Z=ue(null);Z.displayName="ListboxDataContext";function q(x){let P=de(Z);if(P===null){let g=new Error(`<${x} /> is missing a parent <Listbox /> component.`);throw Error.captureStackTrace&&Error.captureStackTrace(g,q),g}return P}let xt=pe;function Ot(x,P){let g=We(),{value:p,defaultValue:l,form:i,name:E,onChange:s,by:T,invalid:t=!1,disabled:u=g||!1,horizontal:S=!1,multiple:a=!1,__demoMode:o=!1,...d}=x;const O=S?"horizontal":"vertical";let v=j(P),A=Fe(l),[b=a?[]:void 0,m]=Re(p,s,A),y=yt({__demoMode:o}),h=le({static:!1,hold:!1}),B=le(new Map),I=_e(T),U=re(R=>Y(k.mode,{[N.Multi]:()=>b.some(z=>I(z,R)),[N.Single]:()=>I(b,R)}),[b]),k=H(()=>({value:b,disabled:u,invalid:t,mode:a?N.Multi:N.Single,orientation:O,onChange:m,compare:I,isSelected:U,optionsPropsRef:h,listRef:B}),[b,u,t,a,O,m,I,U,h,B]);ie(()=>{y.state.dataRef.current=k},[k]);let n=M(y,R=>R.listboxState),_=n===f.Open,[L,W]=M(y,R=>[R.buttonElement,R.optionsElement]);ke(_,[L,W],(R,z)=>{y.send({type:bt.CloseListbox}),it(z,lt.Loose)||(R.preventDefault(),L==null||L.focus())});let V=H(()=>({open:n===f.Open,disabled:u,invalid:t,value:b}),[n,u,t,b]),[r,K]=ft({inherit:!0}),ee={ref:v},te=re(()=>{if(A!==void 0)return m==null?void 0:m(A)},[m,A]),oe=$();return F.createElement(K,{value:r,props:{htmlFor:L==null?void 0:L.id},slot:{open:n===f.Open,disabled:u}},F.createElement(Xe,null,F.createElement(mt.Provider,{value:y},F.createElement(Z.Provider,{value:k},F.createElement(tt,{value:Y(n,{[f.Open]:Q.Open,[f.Closed]:Q.Closed})},E!=null&&b!=null&&F.createElement(Ye,{disabled:u,data:{[E]:b},form:i,onReset:te}),oe({ourProps:ee,theirProps:d,slot:V,defaultTag:xt,name:"Listbox"}))))))}let Lt="button";function Pt(x,P){let g=ae(),p=et(),l=q("Listbox.Button"),i=se("Listbox.Button"),{id:E=p||`headlessui-listbox-button-${g}`,disabled:s=l.disabled||!1,autoFocus:T=!1,...t}=x,u=j(P,$e(),i.actions.setButtonElement),S=qe(),a=C(r=>{switch(r.key){case c.Enter:st(r.currentTarget);break;case c.Space:case c.ArrowDown:r.preventDefault(),G(()=>i.actions.openListbox()),l.value||i.actions.goToOption({focus:w.First});break;case c.ArrowUp:r.preventDefault(),G(()=>i.actions.openListbox()),l.value||i.actions.goToOption({focus:w.Last});break}}),o=C(r=>{switch(r.key){case c.Space:r.preventDefault();break}}),d=C(r=>{var K;if(r.button===0){if(nt(r.currentTarget))return r.preventDefault();i.state.listboxState===f.Open?(G(()=>i.actions.closeListbox()),(K=i.state.buttonElement)==null||K.focus({preventScroll:!0})):(r.preventDefault(),i.actions.openListbox())}}),O=C(r=>r.preventDefault()),v=ct([E]),A=ut(),{isFocusVisible:b,focusProps:m}=ve({autoFocus:T}),{isHovered:y,hoverProps:h}=he({isDisabled:s}),{pressed:B,pressProps:I}=Ae({disabled:s}),U=M(i,r=>r.listboxState),k=H(()=>({open:U===f.Open,active:B||U===f.Open,disabled:s,invalid:l.invalid,value:l.value,hover:y,focus:b,autofocus:T}),[U,l.value,s,y,b,B,l.invalid,T]),n=M(i,r=>r.listboxState===f.Open),[_,L]=M(i,r=>[r.buttonElement,r.optionsElement]),W=be(S(),{ref:u,id:E,type:Ne(x,_),"aria-haspopup":"listbox","aria-controls":L==null?void 0:L.id,"aria-expanded":n,"aria-labelledby":v,"aria-describedby":A,disabled:s||void 0,autoFocus:T,onKeyDown:a,onKeyUp:o,onKeyPress:O,onMouseDown:d},m,h,I);return $()({ourProps:W,theirProps:t,slot:k,defaultTag:Lt,name:"Listbox.Button"})}let ye=ue(!1),gt="div",Et=Te.RenderStrategy|Te.Static;function vt(x,P){let g=ae(),{id:p=`headlessui-listbox-options-${g}`,anchor:l,portal:i=!1,modal:E=!0,transition:s=!1,...T}=x,t=Qe(l),[u,S]=Se(null);t&&(i=!0);let a=q("Listbox.Options"),o=se("Listbox.Options"),[d,O,v,A]=M(o,e=>[e.listboxState,e.buttonElement,e.optionsElement,e.__demoMode]),b=ce(O),m=ce(v),y=ot(),[h,B]=ze(s,u,y!==null?(y&Q.Open)===Q.Open:d===f.Open);Ue(h,O,o.actions.closeListbox);let I=A?!1:E&&d===f.Open;He(I,m);let U=A?!1:E&&d===f.Open;Be(U,{allowed:re(()=>[O,v],[O,v])});let k=d!==f.Open,_=Ce(k,O)?!1:h,L=h&&d===f.Closed,W=Ze(L,a.value),V=C(e=>a.compare(W,e)),r=M(o,e=>{var X;if(t==null||!((X=t==null?void 0:t.to)!=null&&X.includes("selection")))return null;let D=e.options.findIndex(ne=>V(ne.dataRef.current.value));return D===-1&&(D=0),D}),K=(()=>{if(t==null)return;if(r===null)return{...t,inner:void 0};let e=Array.from(a.listRef.current.values());return{...t,inner:{listRef:{current:e},index:r}}})(),[ee,te]=je(K),oe=Je(),R=j(P,t?ee:null,o.actions.setOptionsElement,S),z=Me();De(()=>{var D;let e=v;e&&d===f.Open&&e!==((D=pt(e))==null?void 0:D.activeElement)&&(e==null||e.focus({preventScroll:!0}))},[d,v]);let xe=C(e=>{var D,X;switch(z.dispose(),e.key){case c.Space:if(o.state.searchQuery!=="")return e.preventDefault(),e.stopPropagation(),o.actions.search(e.key);case c.Enter:if(e.preventDefault(),e.stopPropagation(),o.state.activeOptionIndex!==null){let{dataRef:ne}=o.state.options[o.state.activeOptionIndex];o.actions.onChange(ne.current.value)}a.mode===N.Single&&(G(()=>o.actions.closeListbox()),(D=o.state.buttonElement)==null||D.focus({preventScroll:!0}));break;case Y(a.orientation,{vertical:c.ArrowDown,horizontal:c.ArrowRight}):return e.preventDefault(),e.stopPropagation(),o.actions.goToOption({focus:w.Next});case Y(a.orientation,{vertical:c.ArrowUp,horizontal:c.ArrowLeft}):return e.preventDefault(),e.stopPropagation(),o.actions.goToOption({focus:w.Previous});case c.Home:case c.PageUp:return e.preventDefault(),e.stopPropagation(),o.actions.goToOption({focus:w.First});case c.End:case c.PageDown:return e.preventDefault(),e.stopPropagation(),o.actions.goToOption({focus:w.Last});case c.Escape:e.preventDefault(),e.stopPropagation(),G(()=>o.actions.closeListbox()),(X=o.state.buttonElement)==null||X.focus({preventScroll:!0});return;case c.Tab:e.preventDefault(),e.stopPropagation(),G(()=>o.actions.closeListbox()),at(o.state.buttonElement,e.shiftKey?fe.Previous:fe.Next);break;default:e.key.length===1&&(o.actions.search(e.key),z.setTimeout(()=>o.actions.clearSearch(),350));break}}),Oe=M(o,e=>{var D;return(D=e.buttonElement)==null?void 0:D.id}),Le=H(()=>({open:d===f.Open}),[d]),Pe=be(t?oe():{},{id:p,ref:R,"aria-activedescendant":M(o,o.selectors.activeDescendantId),"aria-multiselectable":a.mode===N.Multi?!0:void 0,"aria-labelledby":Oe,"aria-orientation":a.orientation,onKeyDown:xe,role:"listbox",tabIndex:d===f.Open?0:void 0,style:{...T.style,...te,"--button-width":we(O,!0).width},...Ke(B)}),ge=$(),Ee=H(()=>a.mode===N.Multi?a:{...a,isSelected:V},[a,V]);return F.createElement(Tt,{enabled:i?x.static||h:!1,ownerDocument:b},F.createElement(Z.Provider,{value:Ee},ge({ourProps:Pe,theirProps:T,slot:Le,defaultTag:gt,features:Et,visible:_,name:"Listbox.Options"})))}let ht="div";function Dt(x,P){let g=ae(),{id:p=`headlessui-listbox-option-${g}`,disabled:l=!1,value:i,...E}=x,s=de(ye)===!0,T=q("Listbox.Option"),t=se("Listbox.Option"),u=M(t,n=>t.selectors.isActive(n,p)),S=T.isSelected(i),a=le(null),o=Ge(a),d=Ie({disabled:l,value:i,domRef:a,get textValue(){return o()}}),O=j(P,a,n=>{n?T.listRef.current.set(p,n):T.listRef.current.delete(p)}),v=M(t,n=>t.selectors.shouldScrollIntoView(n,p));ie(()=>{if(v)return rt().requestAnimationFrame(()=>{var n,_;(_=(n=a.current)==null?void 0:n.scrollIntoView)==null||_.call(n,{block:"nearest"})})},[v,a]),ie(()=>{if(!s)return t.actions.registerOption(p,d),()=>t.actions.unregisterOption(p)},[d,p,s]);let A=C(n=>{var _;if(l)return n.preventDefault();t.actions.onChange(i),T.mode===N.Single&&(G(()=>t.actions.closeListbox()),(_=t.state.buttonElement)==null||_.focus({preventScroll:!0}))}),b=C(()=>{if(l)return t.actions.goToOption({focus:w.Nothing});t.actions.goToOption({focus:w.Specific,id:p})}),m=Ve(),y=C(n=>{m.update(n),!l&&(u||t.actions.goToOption({focus:w.Specific,id:p},me.Pointer))}),h=C(n=>{m.wasMoved(n)&&(l||u||t.actions.goToOption({focus:w.Specific,id:p},me.Pointer))}),B=C(n=>{m.wasMoved(n)&&(l||u&&t.actions.goToOption({focus:w.Nothing}))}),I=H(()=>({active:u,focus:u,selected:S,disabled:l,selectedOption:S&&s}),[u,S,l,s]),U=s?{}:{id:p,ref:O,role:"option",tabIndex:l===!0?void 0:-1,"aria-disabled":l===!0?!0:void 0,"aria-selected":S,disabled:void 0,onClick:A,onFocus:b,onPointerEnter:y,onMouseEnter:y,onPointerMove:h,onMouseMove:h,onPointerLeave:B,onMouseLeave:B},k=$();return!S&&s?null:k({ourProps:U,theirProps:E,slot:I,defaultTag:ht,name:"Listbox.Option"})}let St=pe;function At(x,P){let{options:g,placeholder:p,...l}=x,E={ref:j(P)},s=q("ListboxSelectedOption"),T=H(()=>({}),[]),t=s.value===void 0||s.value===null||s.mode===N.Multi&&Array.isArray(s.value)&&s.value.length===0,u=$();return F.createElement(ye.Provider,{value:!0},u({ourProps:E,theirProps:{...l,children:F.createElement(F.Fragment,null,p&&t?p:g)},slot:T,defaultTag:St,name:"ListboxSelectedOption"}))}let _t=J(Ot),Rt=J(Pt),Ft=dt,Ct=J(vt),Mt=J(Dt),wt=J(At),Ao=Object.assign(_t,{Button:Rt,Label:Ft,Options:Ct,Option:Mt,SelectedOption:wt});export{Ao as Listbox,Rt as ListboxButton,Ft as ListboxLabel,Mt as ListboxOption,Ct as ListboxOptions,wt as ListboxSelectedOption};
