import { useState } from 'react';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import {
  faUsers,
  faBriefcase,
  faSitemap,
  faMoneyBill,
  faFileContract,
  faPlane,
  faCalendarCheck,
  faSearch,
  faPlus,
  faClock,
  faGraduationCap,
  faStarHalfAlt,
  faEdit,
  faTrash
} from '@fortawesome/free-solid-svg-icons';
import * as recursosHumanosService from '../../services/recursosHumanosService';
import { useQuery, useQueryClient } from '@tanstack/react-query';
import { useAuth } from '../../services/authService';
import EmpleadoModal from './components/EmpleadoModal';
import DepartamentoModal from './components/DepartamentoModal';
import CargoModal from './components/CargoModal';
import NominaModal from './components/NominaModal';
import ContratoModal from './components/ContratoModal';
import VacacionesModal from './components/VacacionesModal';
import PermisoModal from './components/PermisoModal';
import TurnoModal from './components/TurnoModal';
import CapacitacionModal from './components/CapacitacionModal';
import EvaluacionDesempenoModal from './components/EvaluacionDesempenoModal';
import TabTurnos from './components/TabTurnos';
import TabCapacitaciones from './components/TabCapacitaciones';
import TabEvaluaciones from './components/TabEvaluaciones';
import { toast } from 'react-hot-toast';

export default function RRHH() {
  const [activeTab, setActiveTab] = useState('empleados');
  const { user } = useAuth();
  const hospitalId = user?.hospital_id || 1;
  const [searchTerm, setSearchTerm] = useState('');
  const [filterEstado, setFilterEstado] = useState('');
  const queryClient = useQueryClient();

  const [showEmpleadoModal, setShowEmpleadoModal] = useState(false);
  const [showDepartamentoModal, setShowDepartamentoModal] = useState(false);
  const [showCargoModal, setShowCargoModal] = useState(false);
  const [showNominaModal, setShowNominaModal] = useState(false);
  const [showContratoModal, setShowContratoModal] = useState(false);
  const [showVacacionesModal, setShowVacacionesModal] = useState(false);
  const [showPermisoModal, setShowPermisoModal] = useState(false);
  const [showTurnoModal, setShowTurnoModal] = useState(false);
  const [showCapacitacionModal, setShowCapacitacionModal] = useState(false);
  const [showEvaluacionModal, setShowEvaluacionModal] = useState(false);

  const [selectedEmpleado, setSelectedEmpleado] = useState<any>(null);
  const [selectedDepartamento, setSelectedDepartamento] = useState<any>(null);
  const [selectedCargo, setSelectedCargo] = useState<any>(null);
  const [selectedNomina, setSelectedNomina] = useState<any>(null);
  const [selectedContrato, setSelectedContrato] = useState<any>(null);
  const [selectedVacaciones, setSelectedVacaciones] = useState<any>(null);
  const [selectedPermiso, setSelectedPermiso] = useState<any>(null);
  const [selectedTurno, setSelectedTurno] = useState<any>(null);
  const [selectedCapacitacion, setSelectedCapacitacion] = useState<any>(null);
  const [selectedEvaluacion, setSelectedEvaluacion] = useState<any>(null);

  const { data: empleados, isLoading: isLoadingEmpleados, error: errorEmpleados } = useQuery({
    queryKey: ['empleados', hospitalId],
    queryFn: () => recursosHumanosService.getEmpleados(hospitalId),
    enabled: !!hospitalId,
  });

  const { data: departamentos, isLoading: isLoadingDepartamentos, error: errorDepartamentos } = useQuery({
    queryKey: ['departamentos', hospitalId],
    queryFn: () => recursosHumanosService.getDepartamentos(hospitalId),
    enabled: !!hospitalId,
  });

  const { data: cargos, isLoading: isLoadingCargos, error: errorCargos } = useQuery({
    queryKey: ['cargos', hospitalId],
    queryFn: () => recursosHumanosService.getCargos(hospitalId),
    enabled: !!hospitalId,
  });

  const { data: nominas, isLoading: isLoadingNominas, error: errorNominas } = useQuery({
    queryKey: ['nominas', hospitalId],
    queryFn: () => recursosHumanosService.getNominas(hospitalId),
    enabled: !!hospitalId,
  });

  const { data: contratos, isLoading: isLoadingContratos, error: errorContratos } = useQuery({
    queryKey: ['contratos', hospitalId],
    queryFn: () => recursosHumanosService.getContratos(hospitalId),
    enabled: !!hospitalId,
  });

  const { data: vacaciones, isLoading: isLoadingVacaciones, error: errorVacaciones } = useQuery({
    queryKey: ['vacaciones', hospitalId],
    queryFn: () => recursosHumanosService.getVacaciones(hospitalId),
    enabled: !!hospitalId,
  });

  const { data: permisos, isLoading: isLoadingPermisos, error: errorPermisos } = useQuery({
    queryKey: ['permisos', hospitalId],
    queryFn: () => recursosHumanosService.getPermisos(hospitalId),
    enabled: !!hospitalId,
  });

  const { data: turnos, isLoading: isLoadingTurnos, error: errorTurnos } = useQuery({
    queryKey: ['turnos', hospitalId],
    queryFn: () => recursosHumanosService.getTurnos(hospitalId),
    enabled: !!hospitalId,
  });

  const { data: capacitaciones, isLoading: isLoadingCapacitaciones, error: errorCapacitaciones } = useQuery({
    queryKey: ['capacitaciones', hospitalId],
    queryFn: () => recursosHumanosService.getCapacitaciones(hospitalId),
    enabled: !!hospitalId,
  });

  const { data: evaluaciones, isLoading: isLoadingEvaluaciones, error: errorEvaluaciones } = useQuery({
    queryKey: ['evaluaciones', hospitalId],
    queryFn: () => recursosHumanosService.getEvaluacionesDesempeno(hospitalId),
    enabled: !!hospitalId,
  });

  const { data: resumenRRHH, isLoading: isLoadingResumenRRHH, error: errorResumenRRHH } = useQuery({
    queryKey: ['resumenRRHH', hospitalId],
    queryFn: () => recursosHumanosService.getResumenRecursosHumanos(hospitalId),
    enabled: !!hospitalId,
  });

  const handleOpenTurnoModal = (turno?: any) => {
    setSelectedTurno(turno);
    setShowTurnoModal(true);
  };

  const handleDeleteTurno = (id: number) => {
    if (window.confirm('¿Está seguro de eliminar este turno?')) {
      recursosHumanosService.deleteTurno(id)
        .then(() => {
          toast.success('Turno eliminado exitosamente.');
          queryClient.invalidateQueries({ queryKey: ['turnos', hospitalId] });
        })
        .catch(error => {
          toast.error('Error al eliminar turno.');
          console.error('Error al eliminar turno:', error)
        });
    }
  };

  const handleOpenCapacitacionModal = (capacitacion?: any) => {
    setSelectedCapacitacion(capacitacion);
    setShowCapacitacionModal(true);
  };

  const handleDeleteCapacitacion = (id: number) => {
    if (window.confirm('¿Está seguro de eliminar esta capacitación?')) {
      recursosHumanosService.deleteCapacitacion(id)
        .then(() => {
          toast.success('Capacitación eliminada exitosamente.');
          queryClient.invalidateQueries({ queryKey: ['capacitaciones', hospitalId] });
        })
        .catch(error => {
          toast.error('Error al eliminar capacitación.');
          console.error('Error al eliminar capacitación:', error)
        });
    }
  };

  const handleOpenEvaluacionModal = (evaluacion?: any) => {
    setSelectedEvaluacion(evaluacion);
    setShowEvaluacionModal(true);
  };

  const handleDeleteEvaluacion = (id: number) => {
    if (window.confirm('¿Está seguro de eliminar esta evaluación?')) {
      recursosHumanosService.deleteEvaluacionDesempeno(id)
        .then(() => {
          toast.success('Evaluación eliminada exitosamente.');
          queryClient.invalidateQueries({ queryKey: ['evaluaciones', hospitalId] });
        })
        .catch(error => {
          toast.error('Error al eliminar evaluación.');
          console.error('Error al eliminar evaluación:', error)
        });
    }
  };

  const handleDeleteEmpleado = async (id: number) => {
    if (window.confirm('¿Estás seguro de que deseas eliminar este empleado? Esta acción no se puede deshacer.')) {
      try {
        await recursosHumanosService.deleteEmpleado(id);
        toast.success('Empleado eliminado exitosamente.');
        queryClient.invalidateQueries({ queryKey: ['empleados', hospitalId] });
      } catch (error) {
        toast.error('Error al eliminar el empleado.');
        console.error("Error deleting empleado:", error);
      }
    }
  };

  if (isLoadingResumenRRHH || isLoadingEmpleados || isLoadingDepartamentos || isLoadingCargos || isLoadingNominas || isLoadingContratos || isLoadingVacaciones || isLoadingPermisos || isLoadingTurnos || isLoadingCapacitaciones || isLoadingEvaluaciones) {
    return <div className="flex justify-center items-center h-screen"><p className="text-white text-xl">Cargando datos de Recursos Humanos...</p></div>;
  }

  if (errorResumenRRHH || errorEmpleados || errorDepartamentos || errorCargos || errorNominas || errorContratos || errorVacaciones || errorPermisos || errorTurnos || errorCapacitaciones || errorEvaluaciones) {
    console.error("Errores de carga:", {
        errorResumenRRHH, errorEmpleados, errorDepartamentos, errorCargos, errorNominas, errorContratos, errorVacaciones, errorPermisos, errorTurnos, errorCapacitaciones, errorEvaluaciones
    });
    return <div className="flex justify-center items-center h-screen"><p className="text-red-500 text-xl">Error al cargar algunos datos. Por favor, intente más tarde.</p></div>;
  }

  const filteredEmpleados = (empleados || []).filter(empleado => {
    const nombreCompleto = `${empleado.nombres} ${empleado.apellidos}`.toLowerCase();
    const cargo = empleado.cargo_nombre?.toLowerCase() || '';
    const departamento = empleado.departamento_nombre?.toLowerCase() || '';
    const searchTermLower = searchTerm.toLowerCase();

    return (
      (nombreCompleto.includes(searchTermLower) ||
       cargo.includes(searchTermLower) ||
       departamento.includes(searchTermLower)) &&
      (filterEstado ? empleado.estado === filterEstado : true)
    );
  });

  return (
    <div className="container-responsive">
      <div className="w-full">
        <h1 className="text-3xl font-bold mb-8 text-adaptive">Recursos Humanos</h1>

        {/* Resumen */}
        {!isLoadingResumenRRHH && resumenRRHH && (
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
            <div className="glass-card p-6 rounded-xl hover:shadow-lg transition-all duration-300">
              <h3 className="text-lg font-semibold text-adaptive mb-2">Total Empleados</h3>
              <p className="text-3xl font-bold text-blue-600 dark:text-blue-400">{resumenRRHH.total_empleados}</p>
            </div>
            <div className="glass-card p-6 rounded-xl hover:shadow-lg transition-all duration-300">
              <h3 className="text-lg font-semibold text-adaptive mb-2">Activos</h3>
              <p className="text-3xl font-bold text-green-600 dark:text-green-400">{resumenRRHH.empleados_activos}</p>
            </div>
            <div className="glass-card p-6 rounded-xl hover:shadow-lg transition-all duration-300">
              <h3 className="text-lg font-semibold text-adaptive mb-2">Inactivos</h3>
              <p className="text-3xl font-bold text-red-600 dark:text-red-400">{resumenRRHH.empleados_inactivos}</p>
            </div>
            <div className="glass-card p-6 rounded-xl hover:shadow-lg transition-all duration-300">
              <h3 className="text-lg font-semibold text-adaptive mb-2">En Vacaciones/Licencia</h3>
              <p className="text-3xl font-bold text-yellow-600 dark:text-yellow-400">{(resumenRRHH.empleados_vacaciones || 0) + (resumenRRHH.empleados_licencia || 0)}</p>
            </div>
          </div>
        )}
        
        {/* Pestañas de navegación */}
        <div className="flex flex-wrap mb-8 border-b border-white/10 dark:border-black/10 overflow-x-auto">
          {[
            { key: 'empleados', label: 'Empleados', icon: faUsers },
            { key: 'departamentos', label: 'Departamentos', icon: faSitemap },
            { key: 'cargos', label: 'Cargos', icon: faBriefcase },
            { key: 'nominas', label: 'Nóminas', icon: faMoneyBill },
            { key: 'contratos', label: 'Contratos', icon: faFileContract },
            { key: 'vacaciones', label: 'Vacaciones', icon: faPlane },
            { key: 'permisos', label: 'Permisos', icon: faCalendarCheck },
            { key: 'turnos', label: 'Turnos', icon: faClock },
            { key: 'capacitaciones', label: 'Capacitaciones', icon: faGraduationCap },
            { key: 'evaluaciones', label: 'Evaluaciones', icon: faStarHalfAlt },
          ].map(tab => (
            <button
              key={tab.key}
              onClick={() => setActiveTab(tab.key)}
              className={`flex items-center px-4 py-3 text-sm font-medium rounded-t-xl mr-2 transition-all duration-300 whitespace-nowrap
                ${
                  activeTab === tab.key
                    ? 'glass-card text-adaptive shadow-lg'
                    : 'text-adaptive-muted hover:text-adaptive hover:bg-white/5 dark:hover:bg-black/5'
                }`}
            >
              <FontAwesomeIcon icon={tab.icon} className="mr-2" />
              {tab.label}
            </button>
          ))}
        </div>

        {/* Contenido de las pestañas */}
        <div className="glass-card p-6 rounded-xl shadow-lg">
          {activeTab === 'empleados' && (
            <div>
              <div className="flex flex-col lg:flex-row justify-between items-start lg:items-center mb-6 gap-4">
                <div className="relative w-full lg:w-1/3">
                  <input
                    type="text"
                    placeholder="Buscar empleado..."
                    className="w-full pl-10 pr-4 py-3 glass-card text-adaptive placeholder:text-adaptive-subtle focus:outline-none focus:ring-2 focus:ring-blue-400/50 transition-all duration-300"
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                  />
                  <FontAwesomeIcon icon={faSearch} className="absolute left-3 top-1/2 transform -translate-y-1/2 text-adaptive-muted" />
                </div>
                <div className="flex flex-col sm:flex-row items-stretch sm:items-center gap-3 w-full lg:w-auto">
                  <select
                    className="px-4 py-3 glass-card text-adaptive focus:outline-none focus:ring-2 focus:ring-blue-400/50 transition-all duration-300 min-w-[180px]"
                    value={filterEstado}
                    onChange={(e) => setFilterEstado(e.target.value)}
                  >
                    <option value="">Todos los estados</option>
                    <option value="Activo">Activo</option>
                    <option value="Inactivo">Inactivo</option>
                    <option value="Vacaciones">Vacaciones</option>
                    <option value="Licencia">Licencia</option>
                  </select>
                  <button
                    className="bg-gradient-to-r from-blue-500 to-purple-600 hover:from-blue-600 hover:to-purple-700 text-white px-6 py-3 rounded-xl flex items-center transition-all duration-300 transform hover:scale-105 shadow-lg min-w-[160px] justify-center"
                    onClick={() => {
                      setSelectedEmpleado(null);
                      setShowEmpleadoModal(true);
                    }}
                  >
                    <FontAwesomeIcon icon={faPlus} className="mr-2" />
                    Nuevo Empleado
                  </button>
                </div>
              </div>
              {isLoadingEmpleados ? (
                <p className="text-adaptive text-center py-8">Cargando empleados...</p>
              ) : filteredEmpleados.length > 0 ? (
                <div className="table-container">
                  <table className="table-responsive divide-y divide-white/10 dark:divide-black/10">
                    <thead className="bg-white/5 dark:bg-black/5">
                      <tr>
                        <th className="px-6 py-4 text-left text-xs font-semibold text-adaptive-muted uppercase tracking-wider">ID</th>
                        <th className="px-6 py-4 text-left text-xs font-semibold text-adaptive-muted uppercase tracking-wider">Nombre Completo</th>
                        <th className="px-6 py-4 text-left text-xs font-semibold text-adaptive-muted uppercase tracking-wider">Cargo</th>
                        <th className="px-6 py-4 text-left text-xs font-semibold text-adaptive-muted uppercase tracking-wider">Departamento</th>
                        <th className="px-6 py-4 text-left text-xs font-semibold text-adaptive-muted uppercase tracking-wider">Estado</th>
                        <th className="px-6 py-4 text-right text-xs font-semibold text-adaptive-muted uppercase tracking-wider">Acciones</th>
                      </tr>
                    </thead>
                    <tbody className="divide-y divide-white/10 dark:divide-black/10">
                      {filteredEmpleados.map(empleado => {
                        return (
                          <tr key={empleado.id} className="hover:bg-white/5 dark:hover:bg-black/5 transition-colors duration-200">
                            <td className="px-6 py-4 whitespace-nowrap text-sm text-adaptive">{empleado.id}</td>
                            <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-adaptive">{empleado.nombres} {empleado.apellidos}</td>
                            <td className="px-6 py-4 whitespace-nowrap text-sm text-adaptive-muted">{empleado.cargo_nombre}</td>
                            <td className="px-6 py-4 whitespace-nowrap text-sm text-adaptive-muted">{empleado.departamento_nombre}</td>
                            <td className="px-6 py-4 whitespace-nowrap text-sm">
                              <span
                                className={`px-3 py-1 inline-flex text-xs leading-5 font-semibold rounded-full ${
                                  empleado.estado === 'Activo'
                                    ? 'bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-400'
                                    : empleado.estado === 'Inactivo'
                                    ? 'bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-400'
                                    : empleado.estado === 'Vacaciones'
                                    ? 'bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-400'
                                    : empleado.estado === 'Licencia'
                                    ? 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-400'
                                    : 'bg-gray-100 text-gray-800 dark:bg-gray-900/30 dark:text-gray-400'
                                }`}
                              >
                                {empleado.estado}
                              </span>
                            </td>
                            <td className="px-6 py-4 whitespace-nowrap text-sm text-right">
                              <div className="flex justify-end space-x-2">
                                <button
                                  title="Editar Empleado"
                                  className="glass-card px-3 py-2 rounded-lg text-blue-600 dark:text-blue-400 hover:shadow-lg transition-all duration-200 transform hover:scale-105"
                                  onClick={() => {
                                    setSelectedEmpleado(empleado);
                                    setShowEmpleadoModal(true);
                                  }}
                                >
                                  <FontAwesomeIcon icon={faEdit} className="mr-1" />
                                  Editar
                                </button>
                                <button
                                  title="Eliminar Empleado"
                                  className="glass-card px-3 py-2 rounded-lg text-red-600 dark:text-red-400 hover:shadow-lg transition-all duration-200 transform hover:scale-105"
                                  onClick={() => handleDeleteEmpleado(empleado.id)}
                                >
                                  <FontAwesomeIcon icon={faTrash} className="mr-1" />
                                  Eliminar
                                </button>
                              </div>
                            </td>
                          </tr>
                        );
                      })}
                    </tbody>
                  </table>
                </div>
              ) : (
                <div className="text-center py-12">
                  <div className="w-16 h-16 mx-auto mb-4 rounded-full bg-gray-500/10 flex items-center justify-center">
                    <FontAwesomeIcon icon={faUsers} className="text-2xl text-adaptive-muted" />
                  </div>
                  <p className="text-adaptive-muted text-lg">
                    No se encontraron empleados que coincidan con los filtros.
                  </p>
                </div>
              )}
            </div>
          )}

          {activeTab === 'turnos' && (
            <TabTurnos
              turnos={turnos || []}
              isLoading={isLoadingTurnos}
              onOpenModal={handleOpenTurnoModal}
              onDelete={handleDeleteTurno} 
            />
          )}
          {activeTab === 'capacitaciones' && (
            <TabCapacitaciones
              capacitaciones={capacitaciones || []}
              isLoading={isLoadingCapacitaciones}
              onOpenModal={handleOpenCapacitacionModal}
              onDelete={handleDeleteCapacitacion}
            />
          )}
          {activeTab === 'evaluaciones' && (
            <TabEvaluaciones
              evaluaciones={evaluaciones || []}
              isLoading={isLoadingEvaluaciones}
              onOpenModal={handleOpenEvaluacionModal}
              onDelete={handleDeleteEvaluacion}
            />
          )}
          {activeTab === 'departamentos' && <p className="text-white">Gestión de Departamentos - Próximamente</p>}
          {activeTab === 'cargos' && <p className="text-white">Gestión de Cargos - Próximamente</p>}
          {activeTab === 'nominas' && <p className="text-white">Gestión de Nóminas - Próximamente</p>}
          {activeTab === 'contratos' && <p className="text-white">Gestión de Contratos - Próximamente</p>}
          {activeTab === 'vacaciones' && <p className="text-white">Gestión de Vacaciones - Próximamente</p>}
          {activeTab === 'permisos' && <p className="text-white">Gestión de Permisos - Próximamente</p>}
        </div>
      </div>

      {/* Modales */}
      <EmpleadoModal
        isOpen={showEmpleadoModal}
        onClose={() => {
          setShowEmpleadoModal(false);
          setSelectedEmpleado(null);
        }}
        empleado={selectedEmpleado}
        departamentos={departamentos || []}
        cargos={cargos || []}
      />

      <DepartamentoModal
        isOpen={showDepartamentoModal}
        onClose={() => {
          setShowDepartamentoModal(false);
          setSelectedDepartamento(null);
        }}
        departamento={selectedDepartamento}
        empleados={empleados || []}
      />

      <CargoModal
        isOpen={showCargoModal}
        onClose={() => {
          setShowCargoModal(false);
          setSelectedCargo(null);
        }}
        cargo={selectedCargo}
        departamentos={departamentos || []}
      />

      <NominaModal
        isOpen={showNominaModal}
        onClose={() => {
          setShowNominaModal(false);
          setSelectedNomina(null);
        }}
        nomina={selectedNomina}
      />

      <ContratoModal
        isOpen={showContratoModal}
        onClose={() => {
          setShowContratoModal(false);
          setSelectedContrato(null);
        }}
        contrato={selectedContrato}
        empleados={empleados || []}
      />

      <VacacionesModal
        isOpen={showVacacionesModal}
        onClose={() => {
          setShowVacacionesModal(false);
          setSelectedVacaciones(null);
        }}
        vacaciones={selectedVacaciones}
        empleados={empleados || []}
      />

      <PermisoModal
        isOpen={showPermisoModal}
        onClose={() => {
          setShowPermisoModal(false);
          setSelectedPermiso(null);
        }}
        permiso={selectedPermiso}
        empleados={empleados || []}
      />

      <TurnoModal
        isOpen={showTurnoModal}
        onClose={() => {
          setShowTurnoModal(false);
          setSelectedTurno(null);
        }}
        turno={selectedTurno}
        empleados={empleados || []}
      />

      <CapacitacionModal
        isOpen={showCapacitacionModal}
        onClose={() => {
          setShowCapacitacionModal(false);
          setSelectedCapacitacion(null);
        }}
        capacitacion={selectedCapacitacion}
        empleados={empleados || []}
      />

      <EvaluacionDesempenoModal
        isOpen={showEvaluacionModal}
        onClose={() => {
          setShowEvaluacionModal(false);
          setSelectedEvaluacion(null);
        }}
        evaluacion={selectedEvaluacion}
        empleados={empleados || []}
      />
    </div>
  );
}
