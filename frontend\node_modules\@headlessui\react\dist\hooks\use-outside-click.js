import{useCallback as T,useRef as d}from"react";import{FocusableMode as y,isFocusableElement as M}from'../utils/focus-management.js';import{isMobile as g}from'../utils/platform.js';import{useDocumentEvent as c}from'./use-document-event.js';import{useIsTopLayer as L}from'./use-is-top-layer.js';import{useLatestValue as b}from'./use-latest-value.js';import{useWindowEvent as P}from'./use-window-event.js';const E=30;function R(p,f,C){let u=L(p,"outside-click"),m=b(C),s=T(function(e,n){if(e.defaultPrevented)return;let r=n(e);if(r===null||!r.getRootNode().contains(r)||!r.isConnected)return;let h=function l(o){return typeof o=="function"?l(o()):Array.isArray(o)||o instanceof Set?o:[o]}(f);for(let l of h)if(l!==null&&(l.contains(r)||e.composed&&e.composedPath().includes(l)))return;return!M(r,y.Loose)&&r.tabIndex!==-1&&e.preventDefault(),m.current(e,r)},[m,f]),i=d(null);c(u,"pointerdown",t=>{var e,n;i.current=((n=(e=t.composedPath)==null?void 0:e.call(t))==null?void 0:n[0])||t.target},!0),c(u,"mousedown",t=>{var e,n;i.current=((n=(e=t.composedPath)==null?void 0:e.call(t))==null?void 0:n[0])||t.target},!0),c(u,"click",t=>{g()||i.current&&(s(t,()=>i.current),i.current=null)},!0);let a=d({x:0,y:0});c(u,"touchstart",t=>{a.current.x=t.touches[0].clientX,a.current.y=t.touches[0].clientY},!0),c(u,"touchend",t=>{let e={x:t.changedTouches[0].clientX,y:t.changedTouches[0].clientY};if(!(Math.abs(e.x-a.current.x)>=E||Math.abs(e.y-a.current.y)>=E))return s(t,()=>t.target instanceof HTMLElement?t.target:null)},!0),P(u,"blur",t=>s(t,()=>window.document.activeElement instanceof HTMLIFrameElement?window.document.activeElement:null),!0)}export{R as useOutsideClick};
