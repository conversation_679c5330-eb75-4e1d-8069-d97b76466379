import { useState } from 'react';
import { DiagnosticoCIE11, DiagnosticoPersonalizado } from '../../../services/cie11Service';
import { BuscadorCIE11 } from './BuscadorCIE11';
import { DetalleDiagnosticoCIE11 } from './DetalleDiagnosticoCIE11';
import { DiagnosticosPersonalizados } from './DiagnosticosPersonalizados';
// Importar nuevos componentes de diagnósticos
import { DiagnosticSearch } from '../../../components/diagnosticos/DiagnosticSearch';
import { TransitionHelper } from '../../../components/diagnosticos/TransitionHelper';
import { Diagnosis } from '../../../types/diagnosis.types';

interface DiagnosticoSelectorProps {
  onSelectDiagnostico: (codigo: string, descripcion?: string) => void;
  especialidadId?: number;
  initialCodigo?: string;
  initialDescripcion?: string;
  // Nuevas props para el sistema mejorado
  version?: 'CIE-10' | 'CIE-11' | 'BOTH';
  showTransitionHelper?: boolean;
  allowMultiple?: boolean;
  label?: string;
}

/**
 * Componente selector de diagnósticos que integra búsqueda CIE-11 y diagnósticos personalizados
 */
export const DiagnosticoSelector = ({
  onSelectDiagnostico,
  especialidadId,
  initialCodigo = '',
  initialDescripcion = '',
  version = 'BOTH',
  showTransitionHelper = true,
  allowMultiple = false,
  label = 'Diagnóstico'
}: DiagnosticoSelectorProps) => {
  const [mostrarDetalle, setMostrarDetalle] = useState(false);
  const [mostrarPersonalizados, setMostrarPersonalizados] = useState(false);
  const [codigoSeleccionado, setCodigoSeleccionado] = useState(initialCodigo);
  const [descripcionSeleccionada, setDescripcionSeleccionada] = useState(initialDescripcion);
  const [diagnosticoDetalle, setDiagnosticoDetalle] = useState<string>('');
  const [useNewSearch, setUseNewSearch] = useState(true);
  const [selectedDiagnosis, setSelectedDiagnosis] = useState<Diagnosis | null>(null);

  // Manejar selección de diagnóstico CIE-11 (sistema anterior)
  const handleSelectDiagnostico = (diagnostico: DiagnosticoCIE11) => {
    setCodigoSeleccionado(diagnostico.codigo);
    setDescripcionSeleccionada(diagnostico.titulo);
    onSelectDiagnostico(diagnostico.codigo, diagnostico.titulo);
  };

  // Manejar selección del nuevo sistema unificado
  const handleSelectNewDiagnosis = (diagnosis: Diagnosis) => {
    setSelectedDiagnosis(diagnosis);
    setCodigoSeleccionado(diagnosis.code);
    setDescripcionSeleccionada(diagnosis.title);
    onSelectDiagnostico(diagnosis.code, diagnosis.title);
  };

  // Manejar selección de diagnóstico personalizado
  const handleSelectPersonalizado = (diagnostico: DiagnosticoPersonalizado) => {
    const codigo = diagnostico.codigo_personalizado || diagnostico.codigo_original;
    const descripcion = diagnostico.descripcion_personalizada || diagnostico.titulo;
    
    setCodigoSeleccionado(codigo);
    setDescripcionSeleccionada(descripcion);
    onSelectDiagnostico(codigo);
    setMostrarPersonalizados(false);
  };

  // Mostrar detalle de diagnóstico
  const verDetalle = (codigo: string) => {
    setDiagnosticoDetalle(codigo);
    setMostrarDetalle(true);
  };

  return (
    <div className="space-y-4">
      {/* Selector de sistema de búsqueda */}
      <div className="flex items-center justify-between">
        <label className="block text-sm font-medium text-text-primary">{label} *</label>

        <div className="flex items-center space-x-4">
          <div className="flex items-center space-x-2">
            <button
              type="button"
              onClick={() => setUseNewSearch(!useNewSearch)}
              className={`px-3 py-1 rounded-full text-xs font-medium transition-colors ${
                useNewSearch
                  ? 'bg-primary text-white'
                  : 'bg-border text-text-secondary hover:bg-border/80'
              }`}
            >
              {useNewSearch ? 'Búsqueda Unificada' : 'Búsqueda Clásica'}
            </button>

            <button
              type="button"
              onClick={() => setMostrarPersonalizados(true)}
              className="text-sm text-primary hover:text-primary-dark"
            >
              Personalizados
            </button>
          </div>
        </div>
      </div>

      {/* Buscador principal */}
      <div className="space-y-2">
        {useNewSearch ? (
          // Nuevo sistema de búsqueda unificada
          <DiagnosticSearch
            onSelect={handleSelectNewDiagnosis}
            version={version}
            placeholder={`Buscar diagnóstico ${version === 'BOTH' ? 'CIE-10/CIE-11' : version}...`}
            showFilters={true}
            showSuggestions={true}
            maxResults={10}
            className="w-full"
          />
        ) : (
          // Sistema anterior CIE-11
          <BuscadorCIE11
            onSelectDiagnostico={handleSelectDiagnostico}
            placeholder="Buscar diagnóstico por código o descripción..."
            mostrarRecientes={true}
          />
        )}
      </div>
      
      {/* Diagnóstico seleccionado */}
      {codigoSeleccionado && (
        <div className="p-3 bg-primary/10 border border-primary/20 rounded-lg">
          <div className="flex justify-between items-center">
            <div>
              <div className="flex items-center gap-2">
                <span className="font-semibold text-primary">{codigoSeleccionado}</span>
                {selectedDiagnosis && (
                  <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                    selectedDiagnosis.source === 'CIE-11'
                      ? 'bg-secondary/20 text-secondary'
                      : 'bg-accent/20 text-accent'
                  }`}>
                    {selectedDiagnosis.source}
                  </span>
                )}
              </div>
              <p className="text-sm text-text-secondary mt-1">{descripcionSeleccionada}</p>
              {selectedDiagnosis?.category && (
                <p className="text-xs text-text-muted mt-1">
                  Categoría: {selectedDiagnosis.category}
                </p>
              )}
            </div>

            <div className="flex items-center space-x-2">
              <button
                type="button"
                onClick={() => verDetalle(codigoSeleccionado)}
                className="text-xs px-3 py-1 bg-info/20 hover:bg-info/30 text-info rounded-lg transition-colors"
              >
                Ver detalles
              </button>

              <button
                type="button"
                onClick={() => {
                  setCodigoSeleccionado('');
                  setDescripcionSeleccionada('');
                  setSelectedDiagnosis(null);
                  onSelectDiagnostico('');
                }}
                className="text-xs px-3 py-1 bg-error/20 hover:bg-error/30 text-error rounded-lg transition-colors"
              >
                Quitar
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Herramienta de transición CIE-10 → CIE-11 */}
      {showTransitionHelper && selectedDiagnosis?.source === 'CIE-10' && (
        <div className="mt-4">
          <TransitionHelper
            cie10Code={selectedDiagnosis.code}
            onMappingSelect={(mapping) => {
              if (mapping.cie11Code && mapping.cie11Title) {
                // Crear un diagnóstico CIE-11 a partir del mapeo
                const cie11Diagnosis: Diagnosis = {
                  id: `mapped-${mapping.cie11Code}`,
                  code: mapping.cie11Code,
                  title: mapping.cie11Title,
                  source: 'CIE-11',
                  language: 'es',
                  mappings: {
                    cie10: selectedDiagnosis.code,
                    cie11: mapping.cie11Code
                  }
                };
                handleSelectNewDiagnosis(cie11Diagnosis);
              }
            }}
            showAlternatives={true}
            showConfidence={true}
            className="border border-border rounded-lg"
          />
        </div>
      )}
      
      {/* Modal de detalle de diagnóstico */}
      {mostrarDetalle && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex justify-center items-center z-50 p-4">
          <div className="max-w-3xl w-full">
            <DetalleDiagnosticoCIE11
              codigo={diagnosticoDetalle}
              onClose={() => setMostrarDetalle(false)}
              onSelectRelacionado={(diagnostico) => {
                handleSelectDiagnostico(diagnostico);
                setMostrarDetalle(false);
              }}
            />
          </div>
        </div>
      )}
      
      {/* Modal de diagnósticos personalizados */}
      {mostrarPersonalizados && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex justify-center items-center z-50 p-4">
          <div className="max-w-4xl w-full">
            <div className="bg-white rounded-lg shadow-xl overflow-hidden">
              <div className="p-4 bg-gray-50 border-b flex justify-between items-center">
                <h3 className="text-lg font-semibold text-gray-800">Diagnósticos Personalizados</h3>
                <button
                  onClick={() => setMostrarPersonalizados(false)}
                  className="text-gray-500 hover:text-gray-700 text-xl"
                >
                  &times;
                </button>
              </div>
              
              <div className="p-6 max-h-[80vh] overflow-y-auto">
                <DiagnosticosPersonalizados
                  onSelectDiagnostico={handleSelectPersonalizado}
                  especialidadId={especialidadId}
                />
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};
