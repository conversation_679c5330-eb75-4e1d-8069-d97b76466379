import React, { useState } from 'react';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import {
  faCalendarCheck,
  faPlus,
  faSearch,
  faEdit,
  faTrash,
  faEye,
  faCheckCircle,
  faTimesCircle,
  faClock,
  faCalendarAlt,
  faUser,
  faFileAlt,
  faExclamationTriangle,
  faTimes
} from '@fortawesome/free-solid-svg-icons';
import { Button } from '../../../components/ui/Button';
import { Input } from '../../../components/ui/Input';
import { toast } from 'react-hot-toast';

// Datos mock de permisos
const mockPermisos = [
  {
    id: 1,
    empleado_nombre: '<PERSON>',
    tipo_permiso: 'Médico',
    fecha_solicitud: '2024-01-10',
    fecha_inicio: '2024-01-15',
    fecha_fin: '2024-01-16',
    horas_solicitadas: 16,
    motivo: 'Cita médica especializada',
    estado: 'Aprobado',
    aprobado_por: '<PERSON>',
    fecha_aprobacion: '2024-01-12',
    observaciones: 'Aprobado para cita médica urgente',
    documento_soporte: 'cita_medica.pdf'
  },
  {
    id: 2,
    empleado_nombre: 'María Alejandra López',
    tipo_permiso: 'Personal',
    fecha_solicitud: '2024-01-20',
    fecha_inicio: '2024-01-25',
    fecha_fin: '2024-01-25',
    horas_solicitadas: 4,
    motivo: 'Trámites bancarios',
    estado: 'Pendiente',
    observaciones: 'Pendiente de aprobación por jefe inmediato'
  },
  {
    id: 3,
    empleado_nombre: 'Ana María González',
    tipo_permiso: 'Familiar',
    fecha_solicitud: '2024-01-18',
    fecha_inicio: '2024-01-22',
    fecha_fin: '2024-01-23',
    horas_solicitadas: 16,
    motivo: 'Cuidado de familiar enfermo',
    estado: 'Rechazado',
    aprobado_por: 'Patricia Hernández',
    fecha_aprobacion: '2024-01-19',
    observaciones: 'Rechazado por falta de documentación'
  },
  {
    id: 4,
    empleado_nombre: 'Luis Fernando Martínez',
    tipo_permiso: 'Académico',
    fecha_solicitud: '2024-02-01',
    fecha_inicio: '2024-02-10',
    fecha_fin: '2024-02-10',
    horas_solicitadas: 8,
    motivo: 'Examen de especialización',
    estado: 'Aprobado',
    aprobado_por: 'Patricia Hernández',
    fecha_aprobacion: '2024-02-02',
    observaciones: 'Aprobado para desarrollo profesional',
    documento_soporte: 'convocatoria_examen.pdf'
  },
  {
    id: 5,
    empleado_nombre: 'Roberto Silva',
    tipo_permiso: 'Compensatorio',
    fecha_solicitud: '2024-02-05',
    fecha_inicio: '2024-02-12',
    fecha_fin: '2024-02-12',
    horas_solicitadas: 8,
    motivo: 'Compensación por horas extras trabajadas',
    estado: 'Aprobado',
    aprobado_por: 'María López',
    fecha_aprobacion: '2024-02-06',
    observaciones: 'Compensación por trabajo en día festivo'
  }
];

const GestionPermisos: React.FC = () => {
  const [permisos, setPermisos] = useState(mockPermisos);
  const [searchTerm, setSearchTerm] = useState('');
  const [filtroTipo, setFiltroTipo] = useState<string>('todos');
  const [filtroEstado, setFiltroEstado] = useState<string>('todos');
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [editingPermiso, setEditingPermiso] = useState<any>(null);

  const filteredPermisos = permisos.filter(permiso => {
    const matchesSearch = permiso.empleado_nombre.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         permiso.motivo.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesTipo = filtroTipo === 'todos' || permiso.tipo_permiso === filtroTipo;
    const matchesEstado = filtroEstado === 'todos' || permiso.estado === filtroEstado;

    return matchesSearch && matchesTipo && matchesEstado;
  });

  const getEstadoColor = (estado: string) => {
    switch (estado) {
      case 'Aprobado':
        return 'bg-success/20 text-success';
      case 'Pendiente':
        return 'bg-warning/20 text-warning';
      case 'Rechazado':
        return 'bg-error/20 text-error';
      case 'Usado':
        return 'bg-info/20 text-info';
      case 'Vencido':
        return 'bg-gray/20 text-gray-600';
      default:
        return 'bg-secondary/20 text-secondary';
    }
  };

  const getEstadoIcon = (estado: string) => {
    switch (estado) {
      case 'Aprobado':
        return faCheckCircle;
      case 'Pendiente':
        return faClock;
      case 'Rechazado':
        return faTimesCircle;
      case 'Usado':
        return faCalendarCheck;
      case 'Vencido':
        return faExclamationTriangle;
      default:
        return faFileAlt;
    }
  };

  const getTipoColor = (tipo: string) => {
    switch (tipo) {
      case 'Médico':
        return 'text-red-600';
      case 'Personal':
        return 'text-blue-600';
      case 'Familiar':
        return 'text-green-600';
      case 'Académico':
        return 'text-purple-600';
      case 'Compensatorio':
        return 'text-orange-600';
      case 'Calamidad':
        return 'text-pink-600';
      default:
        return 'text-gray-600';
    }
  };

  const handleApprove = (id: number) => {
    setPermisos(prev =>
      prev.map(permiso =>
        permiso.id === id
          ? {
              ...permiso,
              estado: 'Aprobado',
              aprobado_por: 'Usuario Actual',
              fecha_aprobacion: new Date().toISOString().split('T')[0]
            }
          : permiso
      )
    );
    toast.success('Permiso aprobado exitosamente');
  };

  const handleReject = (id: number) => {
    setPermisos(prev =>
      prev.map(permiso =>
        permiso.id === id
          ? {
              ...permiso,
              estado: 'Rechazado',
              aprobado_por: 'Usuario Actual',
              fecha_aprobacion: new Date().toISOString().split('T')[0]
            }
          : permiso
      )
    );
    toast.success('Permiso rechazado');
  };

  const handleEdit = (permiso: any) => {
    setEditingPermiso(permiso);
    setIsModalOpen(true);
    toast.info(`Editando permiso de ${permiso.empleado_nombre}`);
  };

  const handleDelete = (id: number) => {
    const permiso = permisos.find(p => p.id === id);
    if (window.confirm(`¿Está seguro de eliminar el permiso de ${permiso?.empleado_nombre}?`)) {
      setPermisos(prev => prev.filter(p => p.id !== id));
      toast.success('Permiso eliminado exitosamente');
    }
  };

  const totalPermisos = permisos.length;
  const permisosPendientes = permisos.filter(p => p.estado === 'Pendiente').length;
  const permisosAprobados = permisos.filter(p => p.estado === 'Aprobado').length;
  const totalHoras = permisos.reduce((sum, p) => sum + p.horas_solicitadas, 0);

  const tiposPermiso = [...new Set(permisos.map(p => p.tipo_permiso))];
  const estados = [...new Set(permisos.map(p => p.estado))];

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="bg-card p-6 rounded-lg shadow-lg">
        <div className="flex justify-between items-center">
          <div>
            <h2 className="text-2xl font-bold text-primary flex items-center">
              <FontAwesomeIcon icon={faCalendarCheck} className="mr-2" />
              Gestión de Permisos
            </h2>
            <p className="text-muted mt-1">
              Solicitudes y aprobación de permisos
            </p>
          </div>
          <Button onClick={() => setIsModalOpen(true)}>
            <FontAwesomeIcon icon={faPlus} className="mr-2" />
            Nuevo Permiso
          </Button>
        </div>
      </div>

      {/* Estadísticas */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <div className="bg-card p-6 rounded-lg shadow">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-muted">Total Permisos</p>
              <p className="text-2xl font-bold text-primary">{totalPermisos}</p>
            </div>
            <FontAwesomeIcon icon={faCalendarCheck} className="text-primary text-2xl" />
          </div>
        </div>

        <div className="bg-card p-6 rounded-lg shadow">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-muted">Pendientes</p>
              <p className="text-2xl font-bold text-warning">{permisosPendientes}</p>
            </div>
            <FontAwesomeIcon icon={faClock} className="text-warning text-2xl" />
          </div>
        </div>

        <div className="bg-card p-6 rounded-lg shadow">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-muted">Aprobados</p>
              <p className="text-2xl font-bold text-success">{permisosAprobados}</p>
            </div>
            <FontAwesomeIcon icon={faCheckCircle} className="text-success text-2xl" />
          </div>
        </div>

        <div className="bg-card p-6 rounded-lg shadow">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-muted">Total Horas</p>
              <p className="text-2xl font-bold text-info">{totalHoras}</p>
            </div>
            <FontAwesomeIcon icon={faCalendarAlt} className="text-info text-2xl" />
          </div>
        </div>
      </div>

      {/* Filtros */}
      <div className="bg-card p-6 rounded-lg shadow-lg">
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div>
            <label className="block text-sm font-medium text-secondary mb-1">
              Buscar
            </label>
            <div className="relative">
              <FontAwesomeIcon icon={faSearch} className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted" />
              <Input
                type="text"
                placeholder="Empleado, motivo..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10"
              />
            </div>
          </div>

          <div>
            <label className="block text-sm font-medium text-secondary mb-1">
              Tipo de Permiso
            </label>
            <select
              value={filtroTipo}
              onChange={(e) => setFiltroTipo(e.target.value)}
              className="w-full p-3 rounded-lg bg-secondary border border-color text-primary focus:ring-2 focus:ring-primary"
            >
              <option value="todos">Todos los tipos</option>
              {tiposPermiso.map((tipo) => (
                <option key={tipo} value={tipo}>
                  {tipo}
                </option>
              ))}
            </select>
          </div>

          <div>
            <label className="block text-sm font-medium text-secondary mb-1">
              Estado
            </label>
            <select
              value={filtroEstado}
              onChange={(e) => setFiltroEstado(e.target.value)}
              className="w-full p-3 rounded-lg bg-secondary border border-color text-primary focus:ring-2 focus:ring-primary"
            >
              <option value="todos">Todos los estados</option>
              {estados.map((estado) => (
                <option key={estado} value={estado}>
                  {estado}
                </option>
              ))}
            </select>
          </div>
        </div>
      </div>

      {/* Tabla de Permisos */}
      <div className="bg-card rounded-lg shadow-lg overflow-hidden">
        <div className="overflow-x-auto">
          <table className="w-full">
            <thead className="bg-secondary/50">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-secondary uppercase tracking-wider">
                  Empleado
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-secondary uppercase tracking-wider">
                  Tipo
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-secondary uppercase tracking-wider">
                  Período
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-secondary uppercase tracking-wider">
                  Horas
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-secondary uppercase tracking-wider">
                  Motivo
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-secondary uppercase tracking-wider">
                  Estado
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-secondary uppercase tracking-wider">
                  Aprobación
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-secondary uppercase tracking-wider">
                  Acciones
                </th>
              </tr>
            </thead>
            <tbody className="bg-card divide-y divide-color">
              {filteredPermisos.map((permiso) => (
                <tr key={permiso.id} className="hover:bg-secondary/20 transition-colors">
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="flex items-center">
                      <FontAwesomeIcon icon={faUser} className="mr-2 text-muted" />
                      <div>
                        <div className="text-sm font-medium text-primary">
                          {permiso.empleado_nombre}
                        </div>
                        <div className="text-sm text-muted">
                          Solicitud: {new Date(permiso.fecha_solicitud).toLocaleDateString()}
                        </div>
                      </div>
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <span className={`text-sm font-medium ${getTipoColor(permiso.tipo_permiso)}`}>
                      {permiso.tipo_permiso}
                    </span>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="text-sm text-primary">
                      <div className="flex items-center">
                        <FontAwesomeIcon icon={faCalendarAlt} className="mr-2 text-muted" />
                        {new Date(permiso.fecha_inicio).toLocaleDateString()}
                      </div>
                      {permiso.fecha_fin !== permiso.fecha_inicio && (
                        <div className="text-sm text-muted">
                          hasta {new Date(permiso.fecha_fin).toLocaleDateString()}
                        </div>
                      )}
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="text-sm font-medium text-primary">
                      {permiso.horas_solicitadas}h
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="text-sm text-primary max-w-xs truncate">
                      {permiso.motivo}
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getEstadoColor(permiso.estado)}`}>
                      <FontAwesomeIcon icon={getEstadoIcon(permiso.estado)} className="mr-1" />
                      {permiso.estado}
                    </span>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    {permiso.aprobado_por ? (
                      <div className="text-sm text-primary">
                        <div>{permiso.aprobado_por}</div>
                        <div className="text-xs text-muted">
                          {permiso.fecha_aprobacion && new Date(permiso.fecha_aprobacion).toLocaleDateString()}
                        </div>
                      </div>
                    ) : (
                      <span className="text-sm text-muted">Pendiente</span>
                    )}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                    <div className="flex space-x-2">
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => toast.info(`Viendo detalles del permiso de ${permiso.empleado_nombre}`)}
                        className="text-blue-600 border-blue-600 hover:bg-blue-50"
                      >
                        <FontAwesomeIcon icon={faEye} />
                      </Button>
                      {permiso.estado === 'Pendiente' && (
                        <>
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => handleApprove(permiso.id)}
                            className="text-green-600 border-green-600 hover:bg-green-50"
                          >
                            <FontAwesomeIcon icon={faCheckCircle} />
                          </Button>
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => handleReject(permiso.id)}
                            className="text-red-600 border-red-600 hover:bg-red-50"
                          >
                            <FontAwesomeIcon icon={faTimesCircle} />
                          </Button>
                        </>
                      )}
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => handleEdit(permiso)}
                        className="text-purple-600 border-purple-600 hover:bg-purple-50"
                      >
                        <FontAwesomeIcon icon={faEdit} />
                      </Button>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => handleDelete(permiso.id)}
                        className="text-red-600 border-red-600 hover:bg-red-50"
                      >
                        <FontAwesomeIcon icon={faTrash} />
                      </Button>
                    </div>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>

        {filteredPermisos.length === 0 && (
          <div className="text-center py-12">
            <FontAwesomeIcon icon={faCalendarCheck} className="text-muted text-4xl mb-4" />
            <p className="text-muted text-lg">No se encontraron permisos</p>
            <p className="text-muted">Intenta ajustar los filtros de búsqueda</p>
          </div>
        )}
      </div>

      {/* Modal Simple */}
      {isModalOpen && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-card rounded-lg shadow-xl max-w-md w-full mx-4">
            <div className="p-6">
              <div className="flex justify-between items-center mb-4">
                <h3 className="text-lg font-semibold text-primary">
                  {editingPermiso ? 'Editar Permiso' : 'Nuevo Permiso'}
                </h3>
                <button
                  onClick={() => {
                    setIsModalOpen(false);
                    setEditingPermiso(null);
                  }}
                  className="text-muted hover:text-primary"
                >
                  <FontAwesomeIcon icon={faTimes} className="text-xl" />
                </button>
              </div>

              <div className="text-center py-8">
                <FontAwesomeIcon icon={faCalendarCheck} className="text-muted text-4xl mb-4" />
                <p className="text-muted">Modal de permiso próximamente</p>
                <p className="text-sm text-muted mt-2">
                  Funcionalidad de {editingPermiso ? 'edición' : 'creación'} en desarrollo
                </p>
              </div>

              <div className="flex justify-end">
                <Button
                  variant="outline"
                  onClick={() => {
                    setIsModalOpen(false);
                    setEditingPermiso(null);
                  }}
                >
                  Cerrar
                </Button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default GestionPermisos;
