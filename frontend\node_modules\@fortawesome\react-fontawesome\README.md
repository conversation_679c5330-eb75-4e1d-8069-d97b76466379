<a href="https://fontawesome.com">
  <img align="right" width="100" height="100" alt="Official Javascript Component" src="https://img.fortawesome.com/349cfdf6/official-javascript-component.svg">
</a>

# react-fontawesome

[![npm](https://img.shields.io/npm/v/@fortawesome/react-fontawesome.svg?style=flat-square)](https://www.npmjs.com/package/@fortawesome/react-fontawesome)

> Font Awesome React component using SVG with JS

<!-- toc -->

- [Documentation](#documentation)
- [How to Help](#how-to-help)
- [Contributors](#contributors)
- [Releasing this project (only project owners can do this)](#releasing-this-project-only-project-owners-can-do-this)

<!-- tocstop -->

## Documentation

`react-fontawesome` now supports `forwardRef` for version `0.2.x` or above. This was a breaking change so if you are using React older than version 16.3.0 choose the `0.1.x` version of this component.

## Compatibility
| React version | react-fontawesome version |
| - | - |
| < 16.3.0 | 0.1.x |
| >= 16.3.0 | 0.2.x |

Official documentation is hosted at fontawesome.com:

[Check it out here](https://fontawesome.com/v6/docs/web/use-with/react/)

## How to Help

Review the following docs before diving in:

- [CONTRIBUTING.md](CONTRIBUTING.md)
- [CODE_OF_CONDUCT.md](CODE_OF_CONDUCT.md)

And then:

1.  Check the existing issue and see if you can help!

## Contributors

The following contributors have either helped to start this project, have contributed
code, are actively maintaining it (including documentation), or in other ways
being awesome contributors to this project. **We'd like to take a moment to recognize them.**

| Name              | GitHub                                                    |
| ----------------- | --------------------------------------------------------- |
| Nate Radebaugh    | [@NateRadebaugh](https://github.com/NateRadebaugh)        |
| Kirk Ross         | [@kirkbross](https://github.com/kirkbross)                |
| Prateek Goel      | [@prateekgoel](https://github.com/prateekgoel)            |
| Naor Torgeman     | [@naortor](https://github.com/naortor)                    |
| Matthew Hand      | [@mmhand123](https://github.com/mmhand123)                |
| calvinf           | [@calvinf](https://github.com/calvinf)                    |
| Bill Parrott      | [@chimericdream](https://github.com/chimericdream)        |
| Mike Lynch        | [@baelec](https://github.com/baelec)                      |
| Lukáš Rod         | [@rodlukas](https://github.com/rodlukas)                  |
| Proudust          | [@proudust](https://github.com/proudust)                  |
| Tiago Sousa       | [@TiagoPortfolio](https://github.com/TiagoPortfolio)      |
| Alexey Victorov   | [@AliMamed](https://github.com/AliMamed)                  |
| Calum Smith       | [@cpmsmith](https://github.com/cpmsmith)                  |
| squiaios          | [@squiaios](https://github.com/squiaios)                  |
| WyvernDrexx       | [@WyvernDrexx](https://github.com/WyvernDrexx)            |
| Font Awesome Team | [@FortAwesome](https://github.com/orgs/FortAwesome/people)|

If we've missed someone (which is quite likely) submit a Pull Request to us and we'll get it resolved.

## Releasing this project (only project owners can do this)

See [DEVELOPMENT.md](DEVELOPMENT.md#release)
