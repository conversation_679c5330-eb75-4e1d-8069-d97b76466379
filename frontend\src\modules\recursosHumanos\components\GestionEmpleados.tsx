import React, { useState } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import {
  faUsers,
  faPlus,
  faSearch,
  faEdit,
  faTrash,
  faEye,
  faUserCheck,
  faUserTimes,
  faUserClock,
  faPlane,
  faFilter,
  faDownload,
  faUpload,
  faFileExcel,
  faFilePdf,
  faIdCard,
  faEnvelope,
  faPhone,
  faMapMarkerAlt,
  faBriefcase,
  faCalendarAlt,
  faMoneyBillWave
} from '@fortawesome/free-solid-svg-icons';
import { Button } from '../../../components/ui/Button';
import { Input } from '../../../components/ui/Input';
import { toast } from 'react-hot-toast';
import { useAuth } from '../../../services/authService';
import { useQuery } from '@tanstack/react-query';
import * as recursosHumanosService from '../../../services/recursosHumanosService';
import { Empleado, EmpleadoFormData } from '../../../types/recursosHumanos';

// Schema de validación
const empleadoSchema = z.object({
  tipo_documento: z.string().min(1, 'Tipo de documento requerido'),
  numero_documento: z.string().min(1, 'Número de documento requerido'),
  nombres: z.string().min(1, 'Nombres requeridos'),
  apellidos: z.string().min(1, 'Apellidos requeridos'),
  fecha_nacimiento: z.string().min(1, 'Fecha de nacimiento requerida'),
  genero: z.enum(['M', 'F', 'O']),
  direccion: z.string().min(1, 'Dirección requerida'),
  telefono: z.string().min(1, 'Teléfono requerido'),
  email: z.string().email('Email inválido'),
  departamento_id: z.number().min(1, 'Debe seleccionar un departamento'),
  cargo_id: z.number().min(1, 'Debe seleccionar un cargo'),
  fecha_ingreso: z.string().min(1, 'Fecha de ingreso requerida'),
  fecha_salida: z.string().optional(),
  estado: z.enum(['Activo', 'Inactivo', 'Vacaciones', 'Licencia']),
  tipo_contrato: z.enum(['Indefinido', 'Fijo', 'Prestación de Servicios', 'Aprendizaje']),
  salario_base: z.number().min(0, 'Salario debe ser mayor o igual a 0'),
  eps: z.string().min(1, 'EPS requerida'),
  arl: z.string().min(1, 'ARL requerida'),
  fondo_pension: z.string().min(1, 'Fondo de pensión requerido'),
  cuenta_bancaria: z.string().optional(),
  banco: z.string().optional(),
  observaciones: z.string().optional(),
});

const GestionEmpleados: React.FC = () => {
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [editingEmpleado, setEditingEmpleado] = useState<Empleado | null>(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [filtroEstado, setFiltroEstado] = useState<string>('todos');
  const [filtroDepartamento, setFiltroDepartamento] = useState<string>('todos');
  const [filtroContrato, setFiltroContrato] = useState<string>('todos');
  const { user } = useAuth();
  const hospitalId = user?.hospital_id || 1;

  const {
    register,
    handleSubmit,
    formState: { errors },
    reset,
    setValue,
    watch
  } = useForm<EmpleadoFormData>({
    resolver: zodResolver(empleadoSchema),
    defaultValues: {
      hospital_id: hospitalId,
      estado: 'Activo',
      tipo_contrato: 'Indefinido',
      genero: 'M'
    }
  });

  // Queries
  const { data: empleados = [], isLoading: isLoadingEmpleados, refetch: refetchEmpleados } = useQuery({
    queryKey: ['empleados', hospitalId],
    queryFn: () => recursosHumanosService.getEmpleados(hospitalId),
    enabled: !!hospitalId,
  });

  const { data: departamentos = [] } = useQuery({
    queryKey: ['departamentos', hospitalId],
    queryFn: () => recursosHumanosService.getDepartamentos(hospitalId),
    enabled: !!hospitalId,
  });

  const { data: cargos = [] } = useQuery({
    queryKey: ['cargos', hospitalId],
    queryFn: () => recursosHumanosService.getCargos(hospitalId),
    enabled: !!hospitalId,
  });

  const departamentoSeleccionado = watch('departamento_id');

  // Filtrar cargos por departamento
  const cargosFiltrados = cargos.filter(cargo => 
    !departamentoSeleccionado || cargo.departamento_id === departamentoSeleccionado
  );

  const onSubmit = async (data: EmpleadoFormData) => {
    try {
      if (editingEmpleado) {
        await recursosHumanosService.updateEmpleado(editingEmpleado.id, data, hospitalId);
        toast.success('Empleado actualizado exitosamente');
      } else {
        await recursosHumanosService.createEmpleado(data, hospitalId);
        toast.success('Empleado creado exitosamente');
      }
      
      refetchEmpleados();
      setIsModalOpen(false);
      setEditingEmpleado(null);
      reset();
    } catch (error) {
      toast.error('Error al procesar el empleado');
    }
  };

  const handleEdit = (empleado: Empleado) => {
    setEditingEmpleado(empleado);
    setValue('tipo_documento', empleado.tipo_documento);
    setValue('numero_documento', empleado.numero_documento);
    setValue('nombres', empleado.nombres);
    setValue('apellidos', empleado.apellidos);
    setValue('fecha_nacimiento', empleado.fecha_nacimiento);
    setValue('genero', empleado.genero);
    setValue('direccion', empleado.direccion);
    setValue('telefono', empleado.telefono);
    setValue('email', empleado.email);
    setValue('departamento_id', empleado.departamento_id);
    setValue('cargo_id', empleado.cargo_id);
    setValue('fecha_ingreso', empleado.fecha_ingreso);
    setValue('fecha_salida', empleado.fecha_salida || '');
    setValue('estado', empleado.estado);
    setValue('tipo_contrato', empleado.tipo_contrato);
    setValue('salario_base', empleado.salario_base);
    setValue('eps', empleado.eps);
    setValue('arl', empleado.arl);
    setValue('fondo_pension', empleado.fondo_pension);
    setValue('cuenta_bancaria', empleado.cuenta_bancaria || '');
    setValue('banco', empleado.banco || '');
    setValue('observaciones', empleado.observaciones || '');
    setIsModalOpen(true);
  };

  const handleDelete = async (id: number) => {
    if (window.confirm('¿Está seguro de eliminar este empleado?')) {
      try {
        await recursosHumanosService.deleteEmpleado(id);
        toast.success('Empleado eliminado exitosamente');
        refetchEmpleados();
      } catch (error) {
        toast.error('Error al eliminar el empleado');
      }
    }
  };

  const filteredEmpleados = empleados.filter(empleado => {
    const matchesSearch = empleado.nombres.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         empleado.apellidos.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         empleado.numero_documento.includes(searchTerm) ||
                         empleado.email.toLowerCase().includes(searchTerm.toLowerCase());
    
    const matchesEstado = filtroEstado === 'todos' || empleado.estado === filtroEstado;
    const matchesDepartamento = filtroDepartamento === 'todos' || empleado.departamento_id.toString() === filtroDepartamento;
    const matchesContrato = filtroContrato === 'todos' || empleado.tipo_contrato === filtroContrato;
    
    return matchesSearch && matchesEstado && matchesDepartamento && matchesContrato;
  });

  const getEstadoColor = (estado: string) => {
    switch (estado) {
      case 'Activo':
        return 'bg-success/20 text-success';
      case 'Inactivo':
        return 'bg-error/20 text-error';
      case 'Vacaciones':
        return 'bg-warning/20 text-warning';
      case 'Licencia':
        return 'bg-info/20 text-info';
      default:
        return 'bg-secondary/20 text-secondary';
    }
  };

  const getEstadoIcon = (estado: string) => {
    switch (estado) {
      case 'Activo':
        return faUserCheck;
      case 'Inactivo':
        return faUserTimes;
      case 'Vacaciones':
        return faPlane;
      case 'Licencia':
        return faUserClock;
      default:
        return faUsers;
    }
  };

  const formatCurrency = (value: number) => {
    return new Intl.NumberFormat('es-CO', {
      style: 'currency',
      currency: 'COP',
      minimumFractionDigits: 0
    }).format(value);
  };

  const exportarEmpleados = (formato: 'excel' | 'pdf') => {
    toast.success(`Exportando empleados en formato ${formato.toUpperCase()}`);
    // TODO: Implementar exportación real
  };

  const totalEmpleados = empleados.length;
  const empleadosActivos = empleados.filter(e => e.estado === 'Activo').length;
  const empleadosVacaciones = empleados.filter(e => e.estado === 'Vacaciones').length;
  const empleadosLicencia = empleados.filter(e => e.estado === 'Licencia').length;

  if (isLoadingEmpleados) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="bg-card p-6 rounded-lg shadow-lg">
        <div className="flex justify-between items-center">
          <div>
            <h2 className="text-2xl font-bold text-primary flex items-center">
              <FontAwesomeIcon icon={faUsers} className="mr-2" />
              Gestión de Empleados
            </h2>
            <p className="text-muted mt-1">
              Administración completa del personal del hospital
            </p>
          </div>
          <div className="flex gap-2">
            <Button variant="outline" onClick={() => exportarEmpleados('excel')}>
              <FontAwesomeIcon icon={faFileExcel} className="mr-2" />
              Excel
            </Button>
            <Button variant="outline" onClick={() => exportarEmpleados('pdf')}>
              <FontAwesomeIcon icon={faFilePdf} className="mr-2" />
              PDF
            </Button>
            <Button onClick={() => setIsModalOpen(true)}>
              <FontAwesomeIcon icon={faPlus} className="mr-2" />
              Nuevo Empleado
            </Button>
          </div>
        </div>
      </div>

      {/* Estadísticas Rápidas */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <div className="bg-card p-6 rounded-lg shadow">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-muted">Total Empleados</p>
              <p className="text-2xl font-bold text-primary">{totalEmpleados}</p>
            </div>
            <FontAwesomeIcon icon={faUsers} className="text-primary text-2xl" />
          </div>
        </div>

        <div className="bg-card p-6 rounded-lg shadow">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-muted">Activos</p>
              <p className="text-2xl font-bold text-success">{empleadosActivos}</p>
            </div>
            <FontAwesomeIcon icon={faUserCheck} className="text-success text-2xl" />
          </div>
        </div>

        <div className="bg-card p-6 rounded-lg shadow">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-muted">En Vacaciones</p>
              <p className="text-2xl font-bold text-warning">{empleadosVacaciones}</p>
            </div>
            <FontAwesomeIcon icon={faPlane} className="text-warning text-2xl" />
          </div>
        </div>

        <div className="bg-card p-6 rounded-lg shadow">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-muted">En Licencia</p>
              <p className="text-2xl font-bold text-info">{empleadosLicencia}</p>
            </div>
            <FontAwesomeIcon icon={faUserClock} className="text-info text-2xl" />
          </div>
        </div>
      </div>
