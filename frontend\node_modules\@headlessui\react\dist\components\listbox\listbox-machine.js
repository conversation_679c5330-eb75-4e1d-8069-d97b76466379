var g=Object.defineProperty;var h=(e,i,t)=>i in e?g(e,i,{enumerable:!0,configurable:!0,writable:!0,value:t}):e[i]=t;var O=(e,i,t)=>(h(e,typeof i!="symbol"?i+"":i,t),t);import{Machine as y,batch as v}from'../../machine.js';import{Focus as f,calculateActiveIndex as b}from'../../utils/calculate-active-index.js';import{sortByDomNode as I}from'../../utils/focus-management.js';import{match as S}from'../../utils/match.js';var R=(t=>(t[t.Open=0]="Open",t[t.Closed=1]="Closed",t))(R||{}),A=(t=>(t[t.Single=0]="Single",t[t.Multi=1]="Multi",t))(A||{}),E=(t=>(t[t.Pointer=0]="Pointer",t[t.Other=1]="Other",t))(E||{}),L=(r=>(r[r.OpenListbox=0]="OpenListbox",r[r.CloseListbox=1]="CloseListbox",r[r.GoToOption=2]="GoToOption",r[r.Search=3]="Search",r[r.ClearSearch=4]="ClearSearch",r[r.RegisterOptions=5]="RegisterOptions",r[r.UnregisterOptions=6]="UnregisterOptions",r[r.SetButtonElement=7]="SetButtonElement",r[r.SetOptionsElement=8]="SetOptionsElement",r[r.SortOptions=9]="SortOptions",r))(L||{});function m(e,i=t=>t){let t=e.activeOptionIndex!==null?e.options[e.activeOptionIndex]:null,n=I(i(e.options.slice()),l=>l.dataRef.current.domRef.current),o=t?n.indexOf(t):null;return o===-1&&(o=null),{options:n,activeOptionIndex:o}}let M={[1](e){return e.dataRef.current.disabled||e.listboxState===1?e:{...e,activeOptionIndex:null,listboxState:1,__demoMode:!1}},[0](e){if(e.dataRef.current.disabled||e.listboxState===0)return e;let i=e.activeOptionIndex,{isSelected:t}=e.dataRef.current,n=e.options.findIndex(o=>t(o.dataRef.current.value));return n!==-1&&(i=n),{...e,listboxState:0,activeOptionIndex:i,__demoMode:!1}},[2](e,i){var l,s,u,d,c;if(e.dataRef.current.disabled||e.listboxState===1)return e;let t={...e,searchQuery:"",activationTrigger:(l=i.trigger)!=null?l:1,__demoMode:!1};if(i.focus===f.Nothing)return{...t,activeOptionIndex:null};if(i.focus===f.Specific)return{...t,activeOptionIndex:e.options.findIndex(r=>r.id===i.id)};if(i.focus===f.Previous){let r=e.activeOptionIndex;if(r!==null){let x=e.options[r].dataRef.current.domRef,a=b(i,{resolveItems:()=>e.options,resolveActiveIndex:()=>e.activeOptionIndex,resolveId:p=>p.id,resolveDisabled:p=>p.dataRef.current.disabled});if(a!==null){let p=e.options[a].dataRef.current.domRef;if(((s=x.current)==null?void 0:s.previousElementSibling)===p.current||((u=p.current)==null?void 0:u.previousElementSibling)===null)return{...t,activeOptionIndex:a}}}}else if(i.focus===f.Next){let r=e.activeOptionIndex;if(r!==null){let x=e.options[r].dataRef.current.domRef,a=b(i,{resolveItems:()=>e.options,resolveActiveIndex:()=>e.activeOptionIndex,resolveId:p=>p.id,resolveDisabled:p=>p.dataRef.current.disabled});if(a!==null){let p=e.options[a].dataRef.current.domRef;if(((d=x.current)==null?void 0:d.nextElementSibling)===p.current||((c=p.current)==null?void 0:c.nextElementSibling)===null)return{...t,activeOptionIndex:a}}}}let n=m(e),o=b(i,{resolveItems:()=>n.options,resolveActiveIndex:()=>n.activeOptionIndex,resolveId:r=>r.id,resolveDisabled:r=>r.dataRef.current.disabled});return{...t,...n,activeOptionIndex:o}},[3]:(e,i)=>{if(e.dataRef.current.disabled||e.listboxState===1)return e;let n=e.searchQuery!==""?0:1,o=e.searchQuery+i.value.toLowerCase(),s=(e.activeOptionIndex!==null?e.options.slice(e.activeOptionIndex+n).concat(e.options.slice(0,e.activeOptionIndex+n)):e.options).find(d=>{var c;return!d.dataRef.current.disabled&&((c=d.dataRef.current.textValue)==null?void 0:c.startsWith(o))}),u=s?e.options.indexOf(s):-1;return u===-1||u===e.activeOptionIndex?{...e,searchQuery:o}:{...e,searchQuery:o,activeOptionIndex:u,activationTrigger:1}},[4](e){return e.dataRef.current.disabled||e.listboxState===1||e.searchQuery===""?e:{...e,searchQuery:""}},[5]:(e,i)=>{let t=e.options.concat(i.options),n=e.activeOptionIndex;if(e.activeOptionIndex===null){let{isSelected:o}=e.dataRef.current;if(o){let l=t.findIndex(s=>o==null?void 0:o(s.dataRef.current.value));l!==-1&&(n=l)}}return{...e,options:t,activeOptionIndex:n,pendingShouldSort:!0}},[6]:(e,i)=>{let t=e.options,n=[],o=new Set(i.options);for(let[l,s]of t.entries())if(o.has(s.id)&&(n.push(l),o.delete(s.id),o.size===0))break;if(n.length>0){t=t.slice();for(let l of n.reverse())t.splice(l,1)}return{...e,options:t,activationTrigger:1}},[7]:(e,i)=>e.buttonElement===i.element?e:{...e,buttonElement:i.element},[8]:(e,i)=>e.optionsElement===i.element?e:{...e,optionsElement:i.element},[9]:e=>e.pendingShouldSort?{...e,...m(e),pendingShouldSort:!1}:e};class T extends y{constructor(t){super(t);O(this,"actions",{onChange:t=>{let{onChange:n,compare:o,mode:l,value:s}=this.state.dataRef.current;return S(l,{[0]:()=>n==null?void 0:n(t),[1]:()=>{let u=s.slice(),d=u.findIndex(c=>o(c,t));return d===-1?u.push(t):u.splice(d,1),n==null?void 0:n(u)}})},registerOption:v(()=>{let t=[],n=new Set;return[(o,l)=>{n.has(l)||(n.add(l),t.push({id:o,dataRef:l}))},()=>(n.clear(),this.send({type:5,options:t.splice(0)}))]}),unregisterOption:v(()=>{let t=[];return[n=>t.push(n),()=>{this.send({type:6,options:t.splice(0)})}]}),goToOption:v(()=>{let t=null;return[(n,o)=>{t={type:2,...n,trigger:o}},()=>t&&this.send(t)]}),closeListbox:()=>{this.send({type:1})},openListbox:()=>{this.send({type:0})},selectActiveOption:()=>{if(this.state.activeOptionIndex!==null){let{dataRef:t,id:n}=this.state.options[this.state.activeOptionIndex];this.actions.onChange(t.current.value),this.send({type:2,focus:f.Specific,id:n})}},selectOption:t=>{let n=this.state.options.find(o=>o.id===t);n&&this.actions.onChange(n.dataRef.current.value)},search:t=>{this.send({type:3,value:t})},clearSearch:()=>{this.send({type:4})},setButtonElement:t=>{this.send({type:7,element:t})},setOptionsElement:t=>{this.send({type:8,element:t})}});O(this,"selectors",{activeDescendantId(t){var l;let n=t.activeOptionIndex,o=t.options;return n===null||(l=o[n])==null?void 0:l.id},isActive(t,n){var s;let o=t.activeOptionIndex,l=t.options;return o!==null?((s=l[o])==null?void 0:s.id)===n:!1},shouldScrollIntoView(t,n){return t.__demoMode||t.listboxState!==0||t.activationTrigger===0?!1:this.isActive(t,n)}});this.on(5,()=>{requestAnimationFrame(()=>{this.send({type:9})})})}static new({__demoMode:t=!1}={}){return new T({dataRef:{current:{}},listboxState:t?0:1,options:[],searchQuery:"",activeOptionIndex:null,activationTrigger:1,buttonElement:null,optionsElement:null,__demoMode:t})}reduce(t,n){return S(n.type,M,t,n)}}export{L as ActionTypes,E as ActivationTrigger,T as ListboxMachine,R as ListboxStates,A as ValueMode};
