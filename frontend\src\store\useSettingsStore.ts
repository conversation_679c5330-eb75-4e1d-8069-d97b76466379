import { create } from 'zustand';
import { persist } from 'zustand/middleware';

export type Theme = 'light' | 'dark';
export type ContentBackground = 'elegant' | 'glass' | 'solid';

interface SettingsState {
  // Sistema de temas
  theme: Theme;
  toggleTheme: () => void;
  setTheme: (theme: Theme) => void;

  // Configuración de fondo
  backgroundContrast: number; // 0 a 100
  backgroundTint: number; // -100 (blanco) a 100 (negro), 0 es neutral
  backgroundOpacity: number; // 0 a 100 - Opacidad de la imagen de fondo
  updateBackgroundContrast: (value: number) => void;
  updateBackgroundTint: (value: number) => void;
  updateBackgroundOpacity: (value: number) => void;

  // Glassmorphism (solo para header, sidebar, footer)
  glassmorphismOpacity: number; // 0 a 100
  glassmorphismColor: string; // Color base del glassmorphism
  updateGlassmorphismOpacity: (value: number) => void;
  updateGlassmorphismColor: (value: string) => void;

  // Configuración de contenido
  contentBackground: ContentBackground; // Tipo de fondo para el área de contenido
  updateContentBackground: (value: ContentBackground) => void;

  // Contraste de texto
  textContrast: number; // 0 a 100
  updateTextContrast: (value: number) => void;

  // Configuración de accesibilidad
  highContrast: boolean;
  reducedMotion: boolean;
  toggleHighContrast: () => void;
  toggleReducedMotion: () => void;
}

// Función para detectar preferencia del sistema
const getSystemTheme = (): Theme => {
  if (typeof window !== 'undefined' && window.matchMedia) {
    return window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light';
  }
  return 'light';
};

// Función para aplicar el tema al DOM
const applyTheme = (theme: Theme) => {
  if (typeof document !== 'undefined') {
    document.documentElement.setAttribute('data-theme', theme);
    document.documentElement.classList.remove('light', 'dark');
    document.documentElement.classList.add(theme);
  }
};

// Función para aplicar variables CSS dinámicas
const applyDynamicVariables = (state: Partial<SettingsState>) => {
  if (typeof document !== 'undefined') {
    const root = document.documentElement;

    // Aplicar contraste de fondo
    if (state.backgroundContrast !== undefined) {
      root.style.setProperty('--background-contrast', `${state.backgroundContrast}%`);
    }

    // Aplicar tinte de fondo
    if (state.backgroundTint !== undefined) {
      root.style.setProperty('--background-tint', state.backgroundTint.toString());

      // Calcular color de tinte
      let tintColor = 'rgba(0, 0, 0, 0)';
      if (state.backgroundTint < 0) {
        const opacity = Math.abs(state.backgroundTint) / 200;
        tintColor = `rgba(255, 255, 255, ${opacity})`;
      } else if (state.backgroundTint > 0) {
        const opacity = state.backgroundTint / 200;
        tintColor = `rgba(0, 0, 0, ${opacity})`;
      }
      root.style.setProperty('--background-tint-color', tintColor);
    }

    // Aplicar opacidad de glassmorphism
    if (state.glassmorphismOpacity !== undefined) {
      root.style.setProperty('--glassmorphism-opacity', (state.glassmorphismOpacity / 100).toString());
    }

    // Aplicar color de glassmorphism
    if (state.glassmorphismColor !== undefined) {
      root.style.setProperty('--glassmorphism-color', state.glassmorphismColor);
    }

    // Aplicar contraste de texto
    if (state.textContrast !== undefined) {
      root.style.setProperty('--text-contrast', `${state.textContrast}%`);
    }
  }
};

// Usar middleware persist para guardar configuración en localStorage
export const useSettingsStore = create<SettingsState>()(
  persist(
    (set, get) => ({
      // Sistema de temas
      theme: getSystemTheme(),
      toggleTheme: () => {
        const newTheme = get().theme === 'light' ? 'dark' : 'light';
        set({ theme: newTheme });
        applyTheme(newTheme);
      },
      setTheme: (theme: Theme) => {
        set({ theme });
        applyTheme(theme);
      },

      // Configuración de fondo
      backgroundContrast: 80,
      backgroundTint: 0, // 0 es neutral, negativo tiende a blanco, positivo a negro
      backgroundOpacity: 100, // 100% de opacidad por defecto (imagen completamente visible)
      updateBackgroundContrast: (value) => {
        set({ backgroundContrast: value });
        applyDynamicVariables({ backgroundContrast: value });
      },
      updateBackgroundTint: (value) => {
        set({ backgroundTint: value });
        applyDynamicVariables({ backgroundTint: value });
      },
      updateBackgroundOpacity: (value) => {
        set({ backgroundOpacity: value });
        applyDynamicVariables({ backgroundOpacity: value });
      },

      // Glassmorphism (solo para header, sidebar, footer)
      glassmorphismOpacity: 60,
      glassmorphismColor: '26, 34, 54', // RGB del color base
      updateGlassmorphismOpacity: (value) => {
        set({ glassmorphismOpacity: value });
        applyDynamicVariables({ glassmorphismOpacity: value });
      },
      updateGlassmorphismColor: (value) => {
        set({ glassmorphismColor: value });
        applyDynamicVariables({ glassmorphismColor: value });
      },

      // Configuración de contenido
      contentBackground: 'elegant',
      updateContentBackground: (value) => set({ contentBackground: value }),

      // Contraste de texto
      textContrast: 90,
      updateTextContrast: (value) => {
        set({ textContrast: value });
        applyDynamicVariables({ textContrast: value });
      },

      // Configuración de accesibilidad
      highContrast: false,
      reducedMotion: false,
      toggleHighContrast: () => {
        const newValue = !get().highContrast;
        set({ highContrast: newValue });
        if (typeof document !== 'undefined') {
          document.documentElement.classList.toggle('high-contrast', newValue);
        }
      },
      toggleReducedMotion: () => {
        const newValue = !get().reducedMotion;
        set({ reducedMotion: newValue });
        if (typeof document !== 'undefined') {
          document.documentElement.classList.toggle('reduced-motion', newValue);
        }
      },
    }),
    {
      name: 'hipocrates-ui-settings', // nombre para localStorage
      onRehydrateStorage: () => (state) => {
        // Aplicar tema al cargar desde localStorage
        if (state?.theme) {
          applyTheme(state.theme);
        }

        // Aplicar todas las variables CSS dinámicas
        if (state) {
          applyDynamicVariables(state);
        }

        // Aplicar configuraciones de accesibilidad
        if (state?.highContrast && typeof document !== 'undefined') {
          document.documentElement.classList.add('high-contrast');
        }
        if (state?.reducedMotion && typeof document !== 'undefined') {
          document.documentElement.classList.add('reduced-motion');
        }
      },
    }
  )
);
