import React from 'react';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faBriefcase, faPlus } from '@fortawesome/free-solid-svg-icons';
import { Button } from '../../../components/ui/Button';

const GestionCargos: React.FC = () => {
  return (
    <div className="space-y-6">
      <div className="bg-card p-6 rounded-lg shadow-lg">
        <div className="flex justify-between items-center">
          <div>
            <h2 className="text-2xl font-bold text-primary flex items-center">
              <FontAwesomeIcon icon={faBriefcase} className="mr-2" />
              Gestión de Cargos
            </h2>
            <p className="text-muted mt-1">
              Administración de cargos y posiciones
            </p>
          </div>
          <Button>
            <FontAwesomeIcon icon={faPlus} className="mr-2" />
            Nuevo Cargo
          </Button>
        </div>
      </div>
      
      <div className="bg-card p-6 rounded-lg shadow-lg">
        <p className="text-center text-muted">Componente en desarrollo...</p>
      </div>
    </div>
  );
};

export default GestionCargos;
