import React, { useState } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import {
  faTools,
  faPlus,
  faSearch,
  faEdit,
  faTrash,
  faEye,
  faShieldAlt,
  faUserTie,
  faLaptop,
  faHardHat,
  faTshirt,
  faMoneyBillWave,
  faCalendarAlt,
  faFilter,
  faCheckCircle,
  faTimesCircle,
  faExclamationTriangle,
  faUndo,
  faBoxes,
  faTimes
} from '@fortawesome/free-solid-svg-icons';
import { Button } from '../../../components/ui/Button';
import { Input } from '../../../components/ui/Input';
import { toast } from 'react-hot-toast';
import { useAuth } from '../../../services/authService';
import { <PERSON><PERSON>ion, DotacionFormData } from '../../../types/recursosHumanos';

// Schema de validación
const dotacionSchema = z.object({
  empleado_id: z.number().min(1, 'Debe seleccionar un empleado'),
  tipo_dotacion: z.enum(['Uniforme', 'Calzado', 'EPP', 'Herramientas', 'Tecnología']),
  descripcion: z.string().min(1, 'Descripción requerida'),
  cantidad: z.number().min(1, 'Cantidad debe ser mayor a 0'),
  valor_unitario: z.number().min(0, 'Valor unitario debe ser mayor o igual a 0'),
  fecha_entrega: z.string().min(1, 'Fecha de entrega requerida'),
  observaciones: z.string().optional(),
});

// Datos mock
const mockDotaciones: Dotacion[] = [
  {
    id: 1,
    hospital_id: 1,
    empleado_id: 1,
    empleado_nombre: 'Carlos Andrés Rodríguez',
    tipo_dotacion: 'Uniforme',
    descripcion: 'Uniforme médico completo - Talla M',
    cantidad: 3,
    valor_unitario: 85000,
    valor_total: 255000,
    fecha_entrega: '2024-01-15',
    estado: 'En Uso',
    observaciones: 'Uniforme para área de consulta externa',
    created_at: '2024-01-15T08:00:00Z',
    updated_at: '2024-01-15T08:00:00Z'
  },
  {
    id: 2,
    hospital_id: 1,
    empleado_id: 2,
    empleado_nombre: 'María Alejandra López',
    tipo_dotacion: 'EPP',
    descripcion: 'Kit de protección personal - Mascarillas N95',
    cantidad: 50,
    valor_unitario: 3500,
    valor_total: 175000,
    fecha_entrega: '2024-01-18',
    estado: 'Entregada',
    observaciones: 'EPP para área de urgencias',
    created_at: '2024-01-18T10:30:00Z',
    updated_at: '2024-01-18T10:30:00Z'
  },
  {
    id: 3,
    hospital_id: 1,
    empleado_id: 3,
    empleado_nombre: 'Ana María González',
    tipo_dotacion: 'Calzado',
    descripcion: 'Zapatos antideslizantes - Talla 37',
    cantidad: 1,
    valor_unitario: 120000,
    valor_total: 120000,
    fecha_entrega: '2024-01-20',
    fecha_devolucion: '2024-01-25',
    estado: 'Devuelta',
    observaciones: 'Cambio por talla incorrecta',
    created_at: '2024-01-20T09:00:00Z',
    updated_at: '2024-01-25T14:00:00Z'
  },
  {
    id: 4,
    hospital_id: 1,
    empleado_id: 4,
    empleado_nombre: 'Luis Fernando Martínez',
    tipo_dotacion: 'Tecnología',
    descripcion: 'Tablet para registro médico - Samsung Galaxy Tab',
    cantidad: 1,
    valor_unitario: 850000,
    valor_total: 850000,
    fecha_entrega: '2024-01-22',
    estado: 'En Uso',
    observaciones: 'Tablet para rondas médicas',
    created_at: '2024-01-22T11:00:00Z',
    updated_at: '2024-01-22T11:00:00Z'
  },
  {
    id: 5,
    hospital_id: 1,
    empleado_id: 5,
    empleado_nombre: 'Patricia Hernández',
    tipo_dotacion: 'Herramientas',
    descripcion: 'Estetoscopio Littmann Classic III',
    cantidad: 1,
    valor_unitario: 450000,
    valor_total: 450000,
    fecha_entrega: '2024-01-25',
    estado: 'Perdida',
    observaciones: 'Reportada como perdida el 2024-02-01',
    created_at: '2024-01-25T15:00:00Z',
    updated_at: '2024-02-01T16:00:00Z'
  }
];

const mockEmpleados = [
  { id: 1, nombre: 'Carlos Andrés Rodríguez' },
  { id: 2, nombre: 'María Alejandra López' },
  { id: 3, nombre: 'Ana María González' },
  { id: 4, nombre: 'Luis Fernando Martínez' },
  { id: 5, nombre: 'Patricia Hernández' }
];

const GestionDotaciones: React.FC = () => {
  const [dotaciones, setDotaciones] = useState<Dotacion[]>(mockDotaciones);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [editingDotacion, setEditingDotacion] = useState<Dotacion | null>(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [filtroEstado, setFiltroEstado] = useState<string>('todos');
  const [filtroTipo, setFiltroTipo] = useState<string>('todos');
  const { user } = useAuth();

  const {
    register,
    handleSubmit,
    formState: { errors },
    reset,
    setValue,
    watch
  } = useForm<DotacionFormData>({
    resolver: zodResolver(dotacionSchema),
    defaultValues: {
      hospital_id: user?.hospital_id || 1,
      cantidad: 1,
      valor_unitario: 0
    }
  });

  const cantidad = watch('cantidad');
  const valorUnitario = watch('valor_unitario');

  const onSubmit = async (data: DotacionFormData) => {
    try {
      const valorTotal = data.cantidad * data.valor_unitario;

      if (editingDotacion) {
        // Actualizar dotación existente
        const updatedDotacion: Dotacion = {
          ...editingDotacion,
          ...data,
          valor_total: valorTotal,
          updated_at: new Date().toISOString()
        };
        
        setDotaciones(prev => 
          prev.map(dot => dot.id === editingDotacion.id ? updatedDotacion : dot)
        );
        toast.success('Dotación actualizada exitosamente');
      } else {
        // Crear nueva dotación
        const newDotacion: Dotacion = {
          id: Math.max(...dotaciones.map(d => d.id)) + 1,
          ...data,
          empleado_nombre: mockEmpleados.find(e => e.id === data.empleado_id)?.nombre || '',
          valor_total: valorTotal,
          estado: 'Entregada',
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        };
        
        setDotaciones(prev => [...prev, newDotacion]);
        toast.success('Dotación registrada exitosamente');
      }
      
      setIsModalOpen(false);
      setEditingDotacion(null);
      reset();
    } catch (error) {
      toast.error('Error al procesar la dotación');
    }
  };

  const handleEdit = (dotacion: Dotacion) => {
    setEditingDotacion(dotacion);
    setValue('empleado_id', dotacion.empleado_id);
    setValue('tipo_dotacion', dotacion.tipo_dotacion);
    setValue('descripcion', dotacion.descripcion);
    setValue('cantidad', dotacion.cantidad);
    setValue('valor_unitario', dotacion.valor_unitario);
    setValue('fecha_entrega', dotacion.fecha_entrega);
    setValue('observaciones', dotacion.observaciones || '');
    setIsModalOpen(true);
  };

  const handleDelete = (id: number) => {
    if (window.confirm('¿Está seguro de eliminar esta dotación?')) {
      setDotaciones(prev => prev.filter(dot => dot.id !== id));
      toast.success('Dotación eliminada exitosamente');
    }
  };

  const handleChangeStatus = (id: number, newStatus: Dotacion['estado']) => {
    setDotaciones(prev => 
      prev.map(dot => 
        dot.id === id 
          ? { 
              ...dot, 
              estado: newStatus,
              fecha_devolucion: newStatus === 'Devuelta' ? new Date().toISOString().split('T')[0] : undefined,
              updated_at: new Date().toISOString()
            }
          : dot
      )
    );
    toast.success(`Estado cambiado a ${newStatus}`);
  };

  const filteredDotaciones = dotaciones.filter(dotacion => {
    const matchesSearch = dotacion.empleado_nombre?.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         dotacion.descripcion.toLowerCase().includes(searchTerm.toLowerCase());
    
    const matchesEstado = filtroEstado === 'todos' || dotacion.estado === filtroEstado;
    const matchesTipo = filtroTipo === 'todos' || dotacion.tipo_dotacion === filtroTipo;
    
    return matchesSearch && matchesEstado && matchesTipo;
  });

  const getEstadoColor = (estado: string) => {
    switch (estado) {
      case 'Entregada':
        return 'bg-success/20 text-success';
      case 'En Uso':
        return 'bg-info/20 text-info';
      case 'Devuelta':
        return 'bg-warning/20 text-warning';
      case 'Perdida':
        return 'bg-error/20 text-error';
      case 'Dañada':
        return 'bg-error/20 text-error';
      default:
        return 'bg-secondary/20 text-secondary';
    }
  };

  const getEstadoIcon = (estado: string) => {
    switch (estado) {
      case 'Entregada':
        return faCheckCircle;
      case 'En Uso':
        return faTools;
      case 'Devuelta':
        return faUndo;
      case 'Perdida':
        return faTimesCircle;
      case 'Dañada':
        return faExclamationTriangle;
      default:
        return faBoxes;
    }
  };

  const getTipoIcon = (tipo: string) => {
    switch (tipo) {
      case 'Uniforme':
        return faTshirt;
      case 'Calzado':
        return faUserTie;
      case 'EPP':
        return faShieldAlt;
      case 'Herramientas':
        return faTools;
      case 'Tecnología':
        return faLaptop;
      default:
        return faBoxes;
    }
  };

  const getTipoColor = (tipo: string) => {
    switch (tipo) {
      case 'Uniforme':
        return 'text-blue-600';
      case 'Calzado':
        return 'text-brown-600';
      case 'EPP':
        return 'text-red-600';
      case 'Herramientas':
        return 'text-gray-600';
      case 'Tecnología':
        return 'text-purple-600';
      default:
        return 'text-gray-600';
    }
  };

  const formatCurrency = (value: number) => {
    return new Intl.NumberFormat('es-CO', {
      style: 'currency',
      currency: 'COP',
      minimumFractionDigits: 0
    }).format(value);
  };

  const totalDotaciones = dotaciones.length;
  const dotacionesEnUso = dotaciones.filter(d => d.estado === 'En Uso').length;
  const dotacionesPerdidas = dotaciones.filter(d => d.estado === 'Perdida').length;
  const valorTotalInventario = dotaciones.reduce((sum, d) => sum + d.valor_total, 0);

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="bg-card p-6 rounded-lg shadow-lg">
        <div className="flex justify-between items-center">
          <div>
            <h2 className="text-2xl font-bold text-primary flex items-center">
              <FontAwesomeIcon icon={faTools} className="mr-2" />
              Gestión de Dotaciones
            </h2>
            <p className="text-muted mt-1">
              Control de uniformes, EPP, herramientas y equipos entregados al personal
            </p>
          </div>
          <Button onClick={() => setIsModalOpen(true)}>
            <FontAwesomeIcon icon={faPlus} className="mr-2" />
            Nueva Dotación
          </Button>
        </div>
      </div>

      {/* Estadísticas */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <div className="bg-card p-6 rounded-lg shadow">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-muted">Total Dotaciones</p>
              <p className="text-2xl font-bold text-primary">{totalDotaciones}</p>
            </div>
            <FontAwesomeIcon icon={faBoxes} className="text-primary text-2xl" />
          </div>
        </div>

        <div className="bg-card p-6 rounded-lg shadow">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-muted">En Uso</p>
              <p className="text-2xl font-bold text-info">{dotacionesEnUso}</p>
            </div>
            <FontAwesomeIcon icon={faTools} className="text-info text-2xl" />
          </div>
        </div>

        <div className="bg-card p-6 rounded-lg shadow">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-muted">Perdidas</p>
              <p className="text-2xl font-bold text-error">{dotacionesPerdidas}</p>
            </div>
            <FontAwesomeIcon icon={faTimesCircle} className="text-error text-2xl" />
          </div>
        </div>

        <div className="bg-card p-6 rounded-lg shadow">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-muted">Valor Total</p>
              <p className="text-xl font-bold text-success">{formatCurrency(valorTotalInventario)}</p>
            </div>
            <FontAwesomeIcon icon={faMoneyBillWave} className="text-success text-2xl" />
          </div>
        </div>
      </div>

      {/* Filtros */}
      <div className="bg-card p-6 rounded-lg shadow-lg">
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <div>
            <label className="block text-sm font-medium text-secondary mb-1">
              Buscar
            </label>
            <div className="relative">
              <FontAwesomeIcon icon={faSearch} className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted" />
              <Input
                type="text"
                placeholder="Empleado, descripción..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10"
              />
            </div>
          </div>

          <div>
            <label className="block text-sm font-medium text-secondary mb-1">
              Estado
            </label>
            <select
              value={filtroEstado}
              onChange={(e) => setFiltroEstado(e.target.value)}
              className="w-full p-3 rounded-lg bg-secondary border border-color text-primary focus:ring-2 focus:ring-primary"
            >
              <option value="todos">Todos los estados</option>
              <option value="Entregada">Entregada</option>
              <option value="En Uso">En Uso</option>
              <option value="Devuelta">Devuelta</option>
              <option value="Perdida">Perdida</option>
              <option value="Dañada">Dañada</option>
            </select>
          </div>

          <div>
            <label className="block text-sm font-medium text-secondary mb-1">
              Tipo
            </label>
            <select
              value={filtroTipo}
              onChange={(e) => setFiltroTipo(e.target.value)}
              className="w-full p-3 rounded-lg bg-secondary border border-color text-primary focus:ring-2 focus:ring-primary"
            >
              <option value="todos">Todos los tipos</option>
              <option value="Uniforme">Uniforme</option>
              <option value="Calzado">Calzado</option>
              <option value="EPP">EPP</option>
              <option value="Herramientas">Herramientas</option>
              <option value="Tecnología">Tecnología</option>
            </select>
          </div>

          <div className="flex items-end">
            <Button variant="outline" className="w-full">
              <FontAwesomeIcon icon={faFilter} className="mr-2" />
              Limpiar Filtros
            </Button>
          </div>
        </div>
      </div>

      {/* Tabla de Dotaciones */}
      <div className="bg-card rounded-lg shadow-lg overflow-hidden">
        <div className="overflow-x-auto">
          <table className="w-full">
            <thead className="bg-secondary/50">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-secondary uppercase tracking-wider">
                  Empleado
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-secondary uppercase tracking-wider">
                  Tipo
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-secondary uppercase tracking-wider">
                  Descripción
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-secondary uppercase tracking-wider">
                  Cantidad
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-secondary uppercase tracking-wider">
                  Valor
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-secondary uppercase tracking-wider">
                  Fecha Entrega
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-secondary uppercase tracking-wider">
                  Estado
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-secondary uppercase tracking-wider">
                  Acciones
                </th>
              </tr>
            </thead>
            <tbody className="bg-card divide-y divide-color">
              {filteredDotaciones.map((dotacion) => (
                <tr key={dotacion.id} className="hover:bg-secondary/20 transition-colors">
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="text-sm font-medium text-primary">
                      {dotacion.empleado_nombre}
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="flex items-center">
                      <FontAwesomeIcon
                        icon={getTipoIcon(dotacion.tipo_dotacion)}
                        className={`mr-2 ${getTipoColor(dotacion.tipo_dotacion)}`}
                      />
                      <span className={`text-sm font-medium ${getTipoColor(dotacion.tipo_dotacion)}`}>
                        {dotacion.tipo_dotacion}
                      </span>
                    </div>
                  </td>
                  <td className="px-6 py-4">
                    <div className="text-sm text-primary">{dotacion.descripcion}</div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="text-sm font-medium text-primary">
                      {dotacion.cantidad}
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="text-sm font-medium text-primary">
                      {formatCurrency(dotacion.valor_total)}
                    </div>
                    <div className="text-sm text-muted">
                      {formatCurrency(dotacion.valor_unitario)}/unidad
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-primary">
                    <div>{new Date(dotacion.fecha_entrega).toLocaleDateString()}</div>
                    {dotacion.fecha_devolucion && (
                      <div className="text-muted">
                        Dev: {new Date(dotacion.fecha_devolucion).toLocaleDateString()}
                      </div>
                    )}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getEstadoColor(dotacion.estado)}`}>
                      <FontAwesomeIcon icon={getEstadoIcon(dotacion.estado)} className="mr-1" />
                      {dotacion.estado}
                    </span>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                    <div className="flex space-x-2">
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => handleEdit(dotacion)}
                        className="text-blue-600 border-blue-600 hover:bg-blue-50"
                      >
                        <FontAwesomeIcon icon={faEdit} />
                      </Button>
                      <Button
                        variant="outline"
                        size="sm"
                        className="text-green-600 border-green-600 hover:bg-green-50"
                      >
                        <FontAwesomeIcon icon={faEye} />
                      </Button>
                      {dotacion.estado === 'En Uso' && (
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => handleChangeStatus(dotacion.id, 'Devuelta')}
                          className="text-orange-600 border-orange-600 hover:bg-orange-50"
                        >
                          <FontAwesomeIcon icon={faUndo} />
                        </Button>
                      )}
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => handleDelete(dotacion.id)}
                        className="text-red-600 border-red-600 hover:bg-red-50"
                      >
                        <FontAwesomeIcon icon={faTrash} />
                      </Button>
                    </div>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>

      {/* Modal de Dotación */}
      {isModalOpen && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-card rounded-lg shadow-xl max-w-2xl w-full mx-4 max-h-[90vh] overflow-y-auto">
            <div className="p-6">
              <div className="flex justify-between items-center mb-6">
                <h3 className="text-lg font-semibold text-primary">
                  {editingDotacion ? 'Editar Dotación' : 'Nueva Dotación'}
                </h3>
                <button
                  onClick={() => {
                    setIsModalOpen(false);
                    setEditingDotacion(null);
                    reset();
                  }}
                  className="text-muted hover:text-primary"
                >
                  <FontAwesomeIcon icon={faTimes} className="text-xl" />
                </button>
              </div>

              <form onSubmit={handleSubmit(onSubmit)} className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-secondary mb-1">
                      Empleado *
                    </label>
                    <select
                      {...register('empleado_id', { valueAsNumber: true })}
                      className="w-full p-3 rounded-lg bg-secondary border border-color text-primary focus:ring-2 focus:ring-primary"
                    >
                      <option value="">Seleccionar empleado</option>
                      {mockEmpleados.map((empleado) => (
                        <option key={empleado.id} value={empleado.id}>
                          {empleado.nombre}
                        </option>
                      ))}
                    </select>
                    {errors.empleado_id && (
                      <p className="mt-1 text-sm text-error">{errors.empleado_id.message}</p>
                    )}
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-secondary mb-1">
                      Tipo de Dotación *
                    </label>
                    <select
                      {...register('tipo_dotacion')}
                      className="w-full p-3 rounded-lg bg-secondary border border-color text-primary focus:ring-2 focus:ring-primary"
                    >
                      <option value="">Seleccionar tipo</option>
                      <option value="Uniforme">Uniforme</option>
                      <option value="Calzado">Calzado</option>
                      <option value="EPP">EPP</option>
                      <option value="Herramientas">Herramientas</option>
                      <option value="Tecnología">Tecnología</option>
                    </select>
                    {errors.tipo_dotacion && (
                      <p className="mt-1 text-sm text-error">{errors.tipo_dotacion.message}</p>
                    )}
                  </div>

                  <div className="md:col-span-2">
                    <label className="block text-sm font-medium text-secondary mb-1">
                      Descripción *
                    </label>
                    <Input
                      {...register('descripcion')}
                      placeholder="Descripción detallada del elemento"
                      error={errors.descripcion?.message}
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-secondary mb-1">
                      Cantidad *
                    </label>
                    <Input
                      {...register('cantidad', { valueAsNumber: true })}
                      type="number"
                      min="1"
                      placeholder="1"
                      error={errors.cantidad?.message}
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-secondary mb-1">
                      Valor Unitario *
                    </label>
                    <Input
                      {...register('valor_unitario', { valueAsNumber: true })}
                      type="number"
                      min="0"
                      step="0.01"
                      placeholder="0.00"
                      error={errors.valor_unitario?.message}
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-secondary mb-1">
                      Fecha de Entrega *
                    </label>
                    <Input
                      {...register('fecha_entrega')}
                      type="date"
                      error={errors.fecha_entrega?.message}
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-secondary mb-1">
                      Valor Total
                    </label>
                    <div className="p-3 bg-secondary/50 rounded-lg text-primary font-bold">
                      {formatCurrency((cantidad || 0) * (valorUnitario || 0))}
                    </div>
                  </div>

                  <div className="md:col-span-2">
                    <label className="block text-sm font-medium text-secondary mb-1">
                      Observaciones
                    </label>
                    <textarea
                      {...register('observaciones')}
                      rows={3}
                      className="w-full p-3 rounded-lg bg-secondary border border-color text-primary focus:ring-2 focus:ring-primary resize-none"
                      placeholder="Observaciones adicionales sobre la dotación"
                    />
                  </div>
                </div>

                <div className="flex justify-end space-x-3 pt-6">
                  <Button
                    type="button"
                    variant="outline"
                    onClick={() => {
                      setIsModalOpen(false);
                      setEditingDotacion(null);
                      reset();
                    }}
                  >
                    Cancelar
                  </Button>
                  <Button type="submit">
                    {editingDotacion ? 'Actualizar' : 'Registrar'} Dotación
                  </Button>
                </div>
              </form>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default GestionDotaciones;
