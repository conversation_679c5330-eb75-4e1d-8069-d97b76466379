import RRHH from './RRHH';
import RRHHIntegral from './RRHHIntegral';
import EmpleadoModal from './components/EmpleadoModal';
import DepartamentoModal from './components/DepartamentoModal';
import CargoModal from './components/CargoModal';
import NominaModal from './components/NominaModal';
import ContratoModal from './components/ContratoModal';
import VacacionesModal from './components/VacacionesModal';
import PermisoModal from './components/PermisoModal';

// Nuevos componentes integrales
import DashboardRRHH from './components/DashboardRRHH';
import GestionEmpleadosSimple from './components/GestionEmpleadosSimple';
import GestionIncapacidades from './components/GestionIncapacidades';
import GestionHorasExtrasSimple from './components/GestionHorasExtrasSimple';
import GestionDotaciones from './components/GestionDotaciones';
import GestionDepartamentos from './components/GestionDepartamentos';
import GestionCargos from './components/GestionCargos';
import GestionNomina from './components/GestionNomina';
import GestionContratos from './components/GestionContratos';
import GestionVacaciones from './components/GestionVacaciones';
import GestionPermisos from './components/GestionPermisos';
import GestionTurnos from './components/GestionTurnos';
import GestionCapacitaciones from './components/GestionCapacitaciones';
import GestionEvaluaciones from './components/GestionEvaluaciones';
import GestionPrestaciones from './components/GestionPrestaciones';
import ReportesRRHH from './components/ReportesRRHH';

export {
  RRHH,
  RRHHIntegral,
  EmpleadoModal,
  DepartamentoModal,
  CargoModal,
  NominaModal,
  ContratoModal,
  VacacionesModal,
  PermisoModal,
  DashboardRRHH,
  GestionEmpleadosSimple,
  GestionIncapacidades,
  GestionHorasExtrasSimple,
  GestionDotaciones,
  GestionDepartamentos,
  GestionCargos,
  GestionNomina,
  GestionContratos,
  GestionVacaciones,
  GestionPermisos,
  GestionTurnos,
  GestionCapacitaciones,
  GestionEvaluaciones,
  GestionPrestaciones,
  ReportesRRHH
};

export default RRHHIntegral;
