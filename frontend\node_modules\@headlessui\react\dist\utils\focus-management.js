import{disposables as N}from'./disposables.js';import{match as L}from'./match.js';import{getOwnerDocument as E}from'./owner.js';let f=["[contentEditable=true]","[tabindex]","a[href]","area[href]","button:not([disabled])","iframe","input:not([disabled])","select:not([disabled])","textarea:not([disabled])"].map(e=>`${e}:not([tabindex='-1'])`).join(","),p=["[data-autofocus]"].map(e=>`${e}:not([tabindex='-1'])`).join(",");var F=(n=>(n[n.First=1]="First",n[n.Previous=2]="Previous",n[n.Next=4]="Next",n[n.Last=8]="Last",n[n.WrapAround=16]="WrapAround",n[n.NoScroll=32]="NoScroll",n[n.AutoFocus=64]="AutoFocus",n))(F||{}),T=(o=>(o[o.Error=0]="Error",o[o.Overflow=1]="Overflow",o[o.Success=2]="Success",o[o.Underflow=3]="Underflow",o))(T||{}),y=(t=>(t[t.Previous=-1]="Previous",t[t.Next=1]="Next",t))(y||{});function b(e=document.body){return e==null?[]:Array.from(e.querySelectorAll(f)).sort((r,t)=>Math.sign((r.tabIndex||Number.MAX_SAFE_INTEGER)-(t.tabIndex||Number.MAX_SAFE_INTEGER)))}function S(e=document.body){return e==null?[]:Array.from(e.querySelectorAll(p)).sort((r,t)=>Math.sign((r.tabIndex||Number.MAX_SAFE_INTEGER)-(t.tabIndex||Number.MAX_SAFE_INTEGER)))}var h=(t=>(t[t.Strict=0]="Strict",t[t.Loose=1]="Loose",t))(h||{});function A(e,r=0){var t;return e===((t=E(e))==null?void 0:t.body)?!1:L(r,{[0](){return e.matches(f)},[1](){let u=e;for(;u!==null;){if(u.matches(f))return!0;u=u.parentElement}return!1}})}function G(e){let r=E(e);N().nextFrame(()=>{r&&!A(r.activeElement,0)&&I(e)})}var H=(t=>(t[t.Keyboard=0]="Keyboard",t[t.Mouse=1]="Mouse",t))(H||{});typeof window!="undefined"&&typeof document!="undefined"&&(document.addEventListener("keydown",e=>{e.metaKey||e.altKey||e.ctrlKey||(document.documentElement.dataset.headlessuiFocusVisible="")},!0),document.addEventListener("click",e=>{e.detail===1?delete document.documentElement.dataset.headlessuiFocusVisible:e.detail===0&&(document.documentElement.dataset.headlessuiFocusVisible="")},!0));function I(e){e==null||e.focus({preventScroll:!0})}let w=["textarea","input"].join(",");function O(e){var r,t;return(t=(r=e==null?void 0:e.matches)==null?void 0:r.call(e,w))!=null?t:!1}function _(e,r=t=>t){return e.slice().sort((t,u)=>{let o=r(t),c=r(u);if(o===null||c===null)return 0;let l=o.compareDocumentPosition(c);return l&Node.DOCUMENT_POSITION_FOLLOWING?-1:l&Node.DOCUMENT_POSITION_PRECEDING?1:0})}function j(e,r){return P(b(),r,{relativeTo:e})}function P(e,r,{sorted:t=!0,relativeTo:u=null,skipElements:o=[]}={}){let c=Array.isArray(e)?e.length>0?e[0].ownerDocument:document:e.ownerDocument,l=Array.isArray(e)?t?_(e):e:r&64?S(e):b(e);o.length>0&&l.length>1&&(l=l.filter(s=>!o.some(a=>a!=null&&"current"in a?(a==null?void 0:a.current)===s:a===s))),u=u!=null?u:c.activeElement;let n=(()=>{if(r&5)return 1;if(r&10)return-1;throw new Error("Missing Focus.First, Focus.Previous, Focus.Next or Focus.Last")})(),x=(()=>{if(r&1)return 0;if(r&2)return Math.max(0,l.indexOf(u))-1;if(r&4)return Math.max(0,l.indexOf(u))+1;if(r&8)return l.length-1;throw new Error("Missing Focus.First, Focus.Previous, Focus.Next or Focus.Last")})(),M=r&32?{preventScroll:!0}:{},m=0,d=l.length,i;do{if(m>=d||m+d<=0)return 0;let s=x+m;if(r&16)s=(s+d)%d;else{if(s<0)return 3;if(s>=d)return 1}i=l[s],i==null||i.focus(M),m+=n}while(i!==c.activeElement);return r&6&&O(i)&&i.select(),2}export{F as Focus,T as FocusResult,h as FocusableMode,I as focusElement,j as focusFrom,P as focusIn,f as focusableSelector,S as getAutoFocusableElements,b as getFocusableElements,A as isFocusableElement,G as restoreFocusIfNecessary,_ as sortByDomNode};
