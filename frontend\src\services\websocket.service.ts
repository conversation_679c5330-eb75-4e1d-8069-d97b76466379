// import { io, Socket } from 'socket.io-client';
import toast from 'react-hot-toast';

// Tipos para eventos WebSocket
export interface AmbulanceLocationUpdate {
  ambulanceId: string;
  latitude: number;
  longitude: number;
  speed: number;
  heading: number;
  timestamp: string;
  status: 'available' | 'en_route' | 'on_scene' | 'returning' | 'offline';
}

export interface SystemNotification {
  id: string;
  type: 'info' | 'warning' | 'error' | 'success';
  title: string;
  message: string;
  timestamp: string;
  userId?: string;
  hospitalId?: string;
}

export interface PatientAlert {
  id: string;
  patientId: string;
  type: 'vital_signs' | 'emergency' | 'medication' | 'appointment';
  severity: 'low' | 'medium' | 'high' | 'critical';
  message: string;
  timestamp: string;
  acknowledged: boolean;
}

export interface EmergencyCall {
  id: string;
  location: {
    latitude: number;
    longitude: number;
    address: string;
  };
  priority: 'low' | 'medium' | 'high' | 'critical';
  type: 'medical' | 'trauma' | 'cardiac' | 'respiratory' | 'other';
  description: string;
  timestamp: string;
  assignedAmbulanceId?: string;
}

export interface WebSocketConfig {
  url: string;
  reconnectAttempts: number;
  reconnectDelay: number;
  timeout: number;
  enableLogging: boolean;
}

class WebSocketService {
  private socket: any | null = null;
  private config: WebSocketConfig;
  private reconnectCount = 0;
  private isConnecting = false;
  private eventListeners = new Map<string, Set<Function>>();

  constructor(config?: Partial<WebSocketConfig>) {
    this.config = {
      url: process.env.VITE_WS_URL || 'ws://localhost:3000',
      reconnectAttempts: 5,
      reconnectDelay: 1000,
      timeout: 10000,
      enableLogging: true,
      ...config
    };
  }

  /**
   * Conectar al servidor WebSocket
   */
  connect(token: string, hospitalId: string): Promise<void> {
    return new Promise((resolve, reject) => {
      if (this.socket?.connected) {
        resolve();
        return;
      }

      if (this.isConnecting) {
        reject(new Error('Ya hay una conexión en progreso'));
        return;
      }

      this.isConnecting = true;
      this.log('Conectando a WebSocket...', { url: this.config.url });

      // Temporalmente comentado hasta instalar socket.io-client
      // this.socket = io(this.config.url, {
      //   auth: {
      //     token,
      //     hospitalId
      //   },
      //   transports: ['websocket'],
      //   timeout: this.config.timeout,
      //   reconnection: true,
      //   reconnectionAttempts: this.config.reconnectAttempts,
      //   reconnectionDelay: this.config.reconnectDelay
      // });

      // Mock para desarrollo
      this.socket = {
        connected: true,
        on: () => {},
        emit: () => {},
        disconnect: () => {}
      };

      // Eventos de conexión
      this.socket.on('connect', () => {
        this.isConnecting = false;
        this.reconnectCount = 0;
        this.log('WebSocket conectado exitosamente');
        toast.success('Conexión en tiempo real establecida');
        resolve();
      });

      this.socket.on('connect_error', (error) => {
        this.isConnecting = false;
        this.log('Error de conexión WebSocket', error);
        reject(error);
      });

      this.socket.on('disconnect', (reason) => {
        this.log('WebSocket desconectado', { reason });
        if (reason === 'io server disconnect') {
          // Reconexión manual requerida
          toast.error('Conexión perdida. Reconectando...');
        }
      });

      this.socket.on('reconnect', (attemptNumber) => {
        this.log('WebSocket reconectado', { attemptNumber });
        toast.success('Conexión restablecida');
      });

      this.socket.on('reconnect_error', (error) => {
        this.reconnectCount++;
        this.log('Error de reconexión', { error, attempt: this.reconnectCount });
        
        if (this.reconnectCount >= this.config.reconnectAttempts) {
          toast.error('No se pudo restablecer la conexión');
        }
      });

      // Configurar manejadores de eventos
      this.setupEventHandlers();
    });
  }

  /**
   * Desconectar del servidor WebSocket
   */
  disconnect(): void {
    if (this.socket) {
      this.log('Desconectando WebSocket...');
      this.socket.disconnect();
      this.socket = null;
      this.eventListeners.clear();
    }
  }

  /**
   * Verificar estado de conexión
   */
  isConnected(): boolean {
    return this.socket?.connected || false;
  }

  /**
   * Configurar manejadores de eventos del servidor
   */
  private setupEventHandlers(): void {
    if (!this.socket) return;

    // Actualizaciones de ubicación de ambulancias
    this.socket.on('ambulance:location:update', (data: AmbulanceLocationUpdate) => {
      this.log('Actualización de ubicación de ambulancia', data);
      this.emit('ambulance:location:update', data);
    });

    // Notificaciones del sistema
    this.socket.on('system:notification', (notification: SystemNotification) => {
      this.log('Notificación del sistema', notification);
      this.showNotification(notification);
      this.emit('system:notification', notification);
    });

    // Alertas de pacientes
    this.socket.on('patient:alert', (alert: PatientAlert) => {
      this.log('Alerta de paciente', alert);
      this.showPatientAlert(alert);
      this.emit('patient:alert', alert);
    });

    // Llamadas de emergencia
    this.socket.on('emergency:call', (call: EmergencyCall) => {
      this.log('Llamada de emergencia', call);
      this.showEmergencyCall(call);
      this.emit('emergency:call', call);
    });

    // Eventos de ambulancias
    this.socket.on('ambulance:assigned', (data) => {
      this.log('Ambulancia asignada', data);
      toast.success(`Ambulancia ${data.ambulanceId} asignada a emergencia`);
      this.emit('ambulance:assigned', data);
    });

    this.socket.on('ambulance:status:changed', (data) => {
      this.log('Estado de ambulancia cambiado', data);
      this.emit('ambulance:status:changed', data);
    });
  }

  /**
   * Mostrar notificación del sistema
   */
  private showNotification(notification: SystemNotification): void {
    const options = {
      duration: 5000,
      position: 'top-right' as const
    };

    switch (notification.type) {
      case 'success':
        toast.success(notification.message, options);
        break;
      case 'warning':
        toast.error(notification.message, options);
        break;
      case 'error':
        toast.error(notification.message, options);
        break;
      default:
        toast(notification.message, options);
    }
  }

  /**
   * Mostrar alerta de paciente
   */
  private showPatientAlert(alert: PatientAlert): void {
    const severityColors = {
      low: 'info',
      medium: 'warning',
      high: 'error',
      critical: 'error'
    };

    const message = `Alerta de paciente: ${alert.message}`;
    
    if (alert.severity === 'critical') {
      toast.error(message, { 
        duration: 0, // No auto-dismiss para críticas
        position: 'top-center'
      });
    } else {
      toast.error(message, { 
        duration: 8000,
        position: 'top-right'
      });
    }
  }

  /**
   * Mostrar llamada de emergencia
   */
  private showEmergencyCall(call: EmergencyCall): void {
    const message = `🚨 Emergencia ${call.priority.toUpperCase()}: ${call.description}`;
    
    toast.error(message, {
      duration: 0, // No auto-dismiss
      position: 'top-center'
    });
  }

  /**
   * Suscribirse a eventos
   */
  on(event: string, callback: Function): void {
    if (!this.eventListeners.has(event)) {
      this.eventListeners.set(event, new Set());
    }
    this.eventListeners.get(event)!.add(callback);
  }

  /**
   * Desuscribirse de eventos
   */
  off(event: string, callback: Function): void {
    const listeners = this.eventListeners.get(event);
    if (listeners) {
      listeners.delete(callback);
    }
  }

  /**
   * Emitir evento a listeners locales
   */
  private emit(event: string, data: any): void {
    const listeners = this.eventListeners.get(event);
    if (listeners) {
      listeners.forEach(callback => {
        try {
          callback(data);
        } catch (error) {
          this.log('Error en callback de evento', { event, error });
        }
      });
    }
  }

  /**
   * Enviar evento al servidor
   */
  send(event: string, data?: any): void {
    if (!this.socket?.connected) {
      this.log('No se puede enviar evento: WebSocket no conectado', { event, data });
      toast.error('Conexión perdida. Reintentando...');
      return;
    }

    this.log('Enviando evento', { event, data });
    this.socket.emit(event, data);
  }

  // Métodos específicos para ambulancias
  trackAmbulance(ambulanceId: string): void {
    this.send('ambulance:track', { ambulanceId });
  }

  untrackAmbulance(ambulanceId: string): void {
    this.send('ambulance:untrack', { ambulanceId });
  }

  updateAmbulanceLocation(ambulanceId: string, location: Omit<AmbulanceLocationUpdate, 'ambulanceId' | 'timestamp'>): void {
    this.send('ambulance:location:update', {
      ambulanceId,
      ...location,
      timestamp: new Date().toISOString()
    });
  }

  assignAmbulance(ambulanceId: string, emergencyId: string): void {
    this.send('ambulance:assign', { ambulanceId, emergencyId });
  }

  updateAmbulanceStatus(ambulanceId: string, status: AmbulanceLocationUpdate['status']): void {
    this.send('ambulance:status:update', { ambulanceId, status });
  }

  // Métodos para emergencias
  createEmergencyCall(call: Omit<EmergencyCall, 'id' | 'timestamp'>): void {
    this.send('emergency:create', {
      ...call,
      timestamp: new Date().toISOString()
    });
  }

  acknowledgeAlert(alertId: string): void {
    this.send('alert:acknowledge', { alertId });
  }

  /**
   * Logging interno
   */
  private log(message: string, data?: any): void {
    if (this.config.enableLogging) {
      console.log(`[WebSocket] ${message}`, data || '');
    }
  }

  /**
   * Obtener estadísticas de conexión
   */
  getConnectionStats() {
    return {
      connected: this.isConnected(),
      reconnectCount: this.reconnectCount,
      eventListeners: Array.from(this.eventListeners.keys()),
      config: this.config
    };
  }
}

// Instancia singleton
export const wsService = new WebSocketService();
export default wsService;
