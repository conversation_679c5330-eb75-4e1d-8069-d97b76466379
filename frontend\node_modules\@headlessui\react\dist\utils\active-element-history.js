import{onDocumentReady as d}from'./document-ready.js';import{focusableSelector as u}from'./focus-management.js';let r=[];d(()=>{function e(t){if(!(t.target instanceof HTMLElement)||t.target===document.body||r[0]===t.target)return;let n=t.target;n=n.closest(u),r.unshift(n!=null?n:t.target),r=r.filter(o=>o!=null&&o.isConnected),r.splice(10)}window.addEventListener("click",e,{capture:!0}),window.addEventListener("mousedown",e,{capture:!0}),window.addEventListener("focus",e,{capture:!0}),document.body.addEventListener("click",e,{capture:!0}),document.body.addEventListener("mousedown",e,{capture:!0}),document.body.addEventListener("focus",e,{capture:!0})});export{r as history};
