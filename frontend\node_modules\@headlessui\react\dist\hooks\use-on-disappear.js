import{useEffect as o}from"react";import{disposables as u}from'../utils/disposables.js';import{useLatestValue as c}from'./use-latest-value.js';function m(s,n,l){let i=c(t=>{let e=t.getBoundingClientRect();e.x===0&&e.y===0&&e.width===0&&e.height===0&&l()});o(()=>{if(!s)return;let t=n===null?null:n instanceof HTMLElement?n:n.current;if(!t)return;let e=u();if(typeof ResizeObserver!="undefined"){let r=new ResizeObserver(()=>i.current(t));r.observe(t),e.add(()=>r.disconnect())}if(typeof IntersectionObserver!="undefined"){let r=new IntersectionObserver(()=>i.current(t));r.observe(t),e.add(()=>r.disconnect())}return()=>e.dispose()},[n,i,s])}export{m as useOnDisappear};
