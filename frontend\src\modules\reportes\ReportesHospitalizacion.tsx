import React, { useState } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { Button } from '../../components/ui/Button';
import { Input } from '../../components/ui/Input';
import { toast } from 'react-hot-toast';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { 
  faHospital, 
  faCalendarAlt, 
  faDownload, 
  faFilter,
  faChartBar,
  faFileExcel,
  faFilePdf,
  faSearch,
  faBed,
  faUserMd,
  faClipboardList
} from '@fortawesome/free-solid-svg-icons';

// Schema de validación para filtros de reporte
const reporteSchema = z.object({
  fecha_inicio: z.string().min(1, 'Fecha de inicio requerida'),
  fecha_fin: z.string().min(1, 'Fecha de fin requerida'),
  tipo_reporte: z.enum(['general', 'por_servicio', 'por_medico', 'estancia_promedio', 'ocupacion_camas'], {
    required_error: 'Debe seleccionar un tipo de reporte'
  }),
  servicio_id: z.string().optional(),
  medico_id: z.string().optional(),
  estado_hospitalizacion: z.enum(['todas', 'activas', 'finalizadas']).default('todas'),
  formato_exportacion: z.enum(['excel', 'pdf', 'csv']).default('excel'),
});

type ReporteFormData = z.infer<typeof reporteSchema>;

// Datos mock para el reporte
const datosHospitalizacion = [
  {
    id: 1,
    paciente: 'Juan Pérez',
    documento: '12345678',
    fecha_ingreso: '2024-01-15',
    fecha_alta: '2024-01-20',
    dias_estancia: 5,
    servicio: 'Medicina Interna',
    medico: 'Dr. García',
    diagnostico: 'Neumonía',
    cama: 'A-101',
    estado: 'Finalizada'
  },
  {
    id: 2,
    paciente: 'María González',
    documento: '87654321',
    fecha_ingreso: '2024-01-18',
    fecha_alta: null,
    dias_estancia: 3,
    servicio: 'Cardiología',
    medico: 'Dr. Rodríguez',
    diagnostico: 'Infarto Agudo de Miocardio',
    cama: 'B-205',
    estado: 'Activa'
  },
  {
    id: 3,
    paciente: 'Carlos López',
    documento: '11223344',
    fecha_ingreso: '2024-01-10',
    fecha_alta: '2024-01-25',
    dias_estancia: 15,
    servicio: 'Cirugía',
    medico: 'Dr. Martínez',
    diagnostico: 'Apendicitis Aguda',
    cama: 'C-301',
    estado: 'Finalizada'
  }
];

const estadisticas = {
  total_hospitalizaciones: 156,
  hospitalizaciones_activas: 23,
  promedio_estancia: 7.2,
  ocupacion_camas: 78.5,
  ingresos_mes: 45,
  altas_mes: 42
};

export const ReportesHospitalizacion: React.FC = () => {
  const [isGenerating, setIsGenerating] = useState(false);
  const [reporteGenerado, setReporteGenerado] = useState(false);
  const [datosReporte, setDatosReporte] = useState(datosHospitalizacion);

  const {
    register,
    handleSubmit,
    formState: { errors },
    watch
  } = useForm<ReporteFormData>({
    resolver: zodResolver(reporteSchema),
    defaultValues: {
      fecha_inicio: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
      fecha_fin: new Date().toISOString().split('T')[0],
      tipo_reporte: 'general',
      estado_hospitalizacion: 'todas',
      formato_exportacion: 'excel'
    }
  });

  const tipoReporte = watch('tipo_reporte');
  const formatoExportacion = watch('formato_exportacion');

  const onSubmit = async (data: ReporteFormData) => {
    setIsGenerating(true);
    try {
      console.log('Generando reporte con filtros:', data);
      
      // Simular generación de reporte
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      // Filtrar datos según criterios
      let datosFiltrados = datosHospitalizacion;
      
      if (data.estado_hospitalizacion !== 'todas') {
        datosFiltrados = datosFiltrados.filter(item => 
          data.estado_hospitalizacion === 'activas' ? item.estado === 'Activa' : item.estado === 'Finalizada'
        );
      }
      
      setDatosReporte(datosFiltrados);
      setReporteGenerado(true);
      toast.success('Reporte generado exitosamente');
    } catch (error) {
      console.error('Error al generar reporte:', error);
      toast.error('Error al generar el reporte');
    } finally {
      setIsGenerating(false);
    }
  };

  const exportarReporte = (formato: string) => {
    toast.success(`Exportando reporte en formato ${formato.toUpperCase()}`);
    // TODO: Implementar exportación real
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="bg-card p-6 rounded-lg shadow-lg">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-2xl font-bold text-primary flex items-center">
              <FontAwesomeIcon icon={faHospital} className="mr-2" />
              Reportes de Hospitalización
            </h1>
            <p className="text-muted mt-1">
              Genere reportes detallados sobre hospitalizaciones, estancias y ocupación de camas
            </p>
          </div>
          <div className="text-right">
            <div className="text-sm text-muted">Última actualización</div>
            <div className="text-primary font-medium">{new Date().toLocaleString()}</div>
          </div>
        </div>
      </div>

      {/* Estadísticas Rápidas */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-6 gap-4">
        <div className="bg-card p-4 rounded-lg shadow">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-muted">Total</p>
              <p className="text-2xl font-bold text-primary">{estadisticas.total_hospitalizaciones}</p>
            </div>
            <FontAwesomeIcon icon={faHospital} className="text-primary text-xl" />
          </div>
        </div>

        <div className="bg-card p-4 rounded-lg shadow">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-muted">Activas</p>
              <p className="text-2xl font-bold text-warning">{estadisticas.hospitalizaciones_activas}</p>
            </div>
            <FontAwesomeIcon icon={faBed} className="text-warning text-xl" />
          </div>
        </div>

        <div className="bg-card p-4 rounded-lg shadow">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-muted">Estancia Promedio</p>
              <p className="text-2xl font-bold text-info">{estadisticas.promedio_estancia} días</p>
            </div>
            <FontAwesomeIcon icon={faCalendarAlt} className="text-info text-xl" />
          </div>
        </div>

        <div className="bg-card p-4 rounded-lg shadow">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-muted">Ocupación</p>
              <p className="text-2xl font-bold text-success">{estadisticas.ocupacion_camas}%</p>
            </div>
            <FontAwesomeIcon icon={faChartBar} className="text-success text-xl" />
          </div>
        </div>

        <div className="bg-card p-4 rounded-lg shadow">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-muted">Ingresos/Mes</p>
              <p className="text-2xl font-bold text-primary">{estadisticas.ingresos_mes}</p>
            </div>
            <FontAwesomeIcon icon={faUserMd} className="text-primary text-xl" />
          </div>
        </div>

        <div className="bg-card p-4 rounded-lg shadow">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-muted">Altas/Mes</p>
              <p className="text-2xl font-bold text-success">{estadisticas.altas_mes}</p>
            </div>
            <FontAwesomeIcon icon={faClipboardList} className="text-success text-xl" />
          </div>
        </div>
      </div>

      {/* Formulario de Filtros */}
      <div className="bg-card p-6 rounded-lg shadow-lg">
        <h3 className="text-lg font-semibold text-primary mb-4 flex items-center">
          <FontAwesomeIcon icon={faFilter} className="mr-2" />
          🔍 Filtros de Reporte
        </h3>

        <form onSubmit={handleSubmit(onSubmit)} className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            <div>
              <label className="block text-sm font-medium text-secondary mb-1">
                Fecha de Inicio *
              </label>
              <Input
                {...register('fecha_inicio')}
                type="date"
                error={errors.fecha_inicio?.message}
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-secondary mb-1">
                Fecha de Fin *
              </label>
              <Input
                {...register('fecha_fin')}
                type="date"
                error={errors.fecha_fin?.message}
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-secondary mb-1">
                Tipo de Reporte *
              </label>
              <select
                {...register('tipo_reporte')}
                className="w-full p-3 rounded-lg bg-secondary border border-color text-primary focus:ring-2 focus:ring-primary focus:border-primary transition-colors"
              >
                <option value="general">📊 Reporte General</option>
                <option value="por_servicio">🏥 Por Servicio</option>
                <option value="por_medico">👨‍⚕️ Por Médico</option>
                <option value="estancia_promedio">📅 Estancia Promedio</option>
                <option value="ocupacion_camas">🛏️ Ocupación de Camas</option>
              </select>
              {errors.tipo_reporte && (
                <p className="mt-1 text-sm text-error">{errors.tipo_reporte.message}</p>
              )}
            </div>

            <div>
              <label className="block text-sm font-medium text-secondary mb-1">
                Estado
              </label>
              <select
                {...register('estado_hospitalizacion')}
                className="w-full p-3 rounded-lg bg-secondary border border-color text-primary focus:ring-2 focus:ring-primary focus:border-primary transition-colors"
              >
                <option value="todas">Todas</option>
                <option value="activas">Activas</option>
                <option value="finalizadas">Finalizadas</option>
              </select>
            </div>
          </div>

          <div className="flex justify-between items-center pt-4">
            <div className="flex items-center gap-4">
              <label className="text-sm font-medium text-secondary">Formato de Exportación:</label>
              <div className="flex gap-2">
                <label className="flex items-center">
                  <input
                    {...register('formato_exportacion')}
                    type="radio"
                    value="excel"
                    className="mr-2"
                  />
                  <FontAwesomeIcon icon={faFileExcel} className="text-success mr-1" />
                  Excel
                </label>
                <label className="flex items-center">
                  <input
                    {...register('formato_exportacion')}
                    type="radio"
                    value="pdf"
                    className="mr-2"
                  />
                  <FontAwesomeIcon icon={faFilePdf} className="text-error mr-1" />
                  PDF
                </label>
              </div>
            </div>

            <Button
              type="submit"
              disabled={isGenerating}
              className="min-w-[150px]"
            >
              {isGenerating ? (
                <>
                  <span className="mr-2 inline-block w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"></span>
                  Generando...
                </>
              ) : (
                <>
                  <FontAwesomeIcon icon={faSearch} className="mr-2" />
                  Generar Reporte
                </>
              )}
            </Button>
          </div>
        </form>
      </div>

      {/* Resultados del Reporte */}
      {reporteGenerado && (
        <div className="bg-card p-6 rounded-lg shadow-lg">
          <div className="flex justify-between items-center mb-4">
            <h3 className="text-lg font-semibold text-primary flex items-center">
              <FontAwesomeIcon icon={faChartBar} className="mr-2" />
              📋 Resultados del Reporte
            </h3>
            <div className="flex gap-2">
              <Button
                variant="outline"
                onClick={() => exportarReporte('excel')}
                className="text-success border-success hover:bg-success/10"
              >
                <FontAwesomeIcon icon={faFileExcel} className="mr-2" />
                Excel
              </Button>
              <Button
                variant="outline"
                onClick={() => exportarReporte('pdf')}
                className="text-error border-error hover:bg-error/10"
              >
                <FontAwesomeIcon icon={faFilePdf} className="mr-2" />
                PDF
              </Button>
            </div>
          </div>

          {/* Tabla de Resultados */}
          <div className="overflow-x-auto">
            <table className="w-full border-collapse">
              <thead>
                <tr className="bg-secondary/50">
                  <th className="border border-color p-3 text-left text-secondary font-medium">Paciente</th>
                  <th className="border border-color p-3 text-left text-secondary font-medium">Documento</th>
                  <th className="border border-color p-3 text-left text-secondary font-medium">Fecha Ingreso</th>
                  <th className="border border-color p-3 text-left text-secondary font-medium">Fecha Alta</th>
                  <th className="border border-color p-3 text-left text-secondary font-medium">Días</th>
                  <th className="border border-color p-3 text-left text-secondary font-medium">Servicio</th>
                  <th className="border border-color p-3 text-left text-secondary font-medium">Médico</th>
                  <th className="border border-color p-3 text-left text-secondary font-medium">Diagnóstico</th>
                  <th className="border border-color p-3 text-left text-secondary font-medium">Cama</th>
                  <th className="border border-color p-3 text-left text-secondary font-medium">Estado</th>
                </tr>
              </thead>
              <tbody>
                {datosReporte.map((item) => (
                  <tr key={item.id} className="hover:bg-secondary/20 transition-colors">
                    <td className="border border-color p-3 text-primary">{item.paciente}</td>
                    <td className="border border-color p-3 text-primary">{item.documento}</td>
                    <td className="border border-color p-3 text-primary">{item.fecha_ingreso}</td>
                    <td className="border border-color p-3 text-primary">
                      {item.fecha_alta || <span className="text-warning">En curso</span>}
                    </td>
                    <td className="border border-color p-3 text-primary font-medium">{item.dias_estancia}</td>
                    <td className="border border-color p-3 text-primary">{item.servicio}</td>
                    <td className="border border-color p-3 text-primary">{item.medico}</td>
                    <td className="border border-color p-3 text-primary">{item.diagnostico}</td>
                    <td className="border border-color p-3 text-primary">{item.cama}</td>
                    <td className="border border-color p-3">
                      <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                        item.estado === 'Activa'
                          ? 'bg-warning/20 text-warning'
                          : 'bg-success/20 text-success'
                      }`}>
                        {item.estado}
                      </span>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>

          {/* Resumen del Reporte */}
          <div className="mt-6 grid grid-cols-1 md:grid-cols-4 gap-4">
            <div className="bg-secondary/30 p-4 rounded-lg">
              <h4 className="font-medium text-secondary mb-2">Total de Registros</h4>
              <p className="text-2xl font-bold text-primary">{datosReporte.length}</p>
            </div>
            <div className="bg-secondary/30 p-4 rounded-lg">
              <h4 className="font-medium text-secondary mb-2">Estancia Promedio</h4>
              <p className="text-2xl font-bold text-info">
                {(datosReporte.reduce((acc, item) => acc + item.dias_estancia, 0) / datosReporte.length).toFixed(1)} días
              </p>
            </div>
            <div className="bg-secondary/30 p-4 rounded-lg">
              <h4 className="font-medium text-secondary mb-2">Hospitalizaciones Activas</h4>
              <p className="text-2xl font-bold text-warning">
                {datosReporte.filter(item => item.estado === 'Activa').length}
              </p>
            </div>
            <div className="bg-secondary/30 p-4 rounded-lg">
              <h4 className="font-medium text-secondary mb-2">Hospitalizaciones Finalizadas</h4>
              <p className="text-2xl font-bold text-success">
                {datosReporte.filter(item => item.estado === 'Finalizada').length}
              </p>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};
