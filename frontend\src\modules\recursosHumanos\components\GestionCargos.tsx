import React, { useState } from 'react';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import {
  faBriefcase,
  faPlus,
  faSearch,
  faEdit,
  faTrash,
  faEye,
  faUsers,
  faMoneyBillWave,
  faGraduationCap,
  faBuilding,
  faTimes,
  faUserTie,
  faFileAlt,
  faClock
} from '@fortawesome/free-solid-svg-icons';
import { Button } from '../../../components/ui/Button';
import { Input } from '../../../components/ui/Input';
import { toast } from 'react-hot-toast';

// Datos mock de cargos
const mockCargos = [
  {
    id: 1,
    nombre: 'Médico Especialista',
    departamento: 'Medicina Interna',
    descripcion: 'Médico especialista en medicina interna y diagnóstico',
    salario_base: 8500000,
    nivel_jerarquico: 'Senior',
    requisitos_educacion: '<PERSON><PERSON><PERSON><PERSON> de <PERSON>na + Especialización',
    experiencia_requerida: '5 años',
    total_empleados: 8,
    estado: 'Activo'
  },
  {
    id: 2,
    nombre: 'Jefe de Enfermería',
    departamento: 'Enfermería',
    descripcion: 'Coordinación y supervisión del personal de enfermería',
    salario_base: 4500000,
    nivel_jerarquico: 'Jefe',
    requisitos_educacion: 'Título de Enfermería + Especialización',
    experiencia_requerida: '8 años',
    total_empleados: 3,
    estado: 'Activo'
  },
  {
    id: 3,
    nombre: 'Enfermero Profesional',
    departamento: 'Enfermería',
    descripcion: 'Atención directa al paciente y procedimientos de enfermería',
    salario_base: 3200000,
    nivel_jerarquico: 'Profesional',
    requisitos_educacion: 'Título de Enfermería',
    experiencia_requerida: '2 años',
    total_empleados: 42,
    estado: 'Activo'
  },
  {
    id: 4,
    nombre: 'Médico General',
    departamento: 'Urgencias',
    descripcion: 'Atención médica general y de urgencias',
    salario_base: 6500000,
    nivel_jerarquico: 'Profesional',
    requisitos_educacion: 'Título de Medicina',
    experiencia_requerida: '3 años',
    total_empleados: 15,
    estado: 'Activo'
  },
  {
    id: 5,
    nombre: 'Cirujano',
    departamento: 'Cirugía',
    descripcion: 'Especialista en procedimientos quirúrgicos',
    salario_base: 12000000,
    nivel_jerarquico: 'Senior',
    requisitos_educacion: 'Título de Medicina + Especialización en Cirugía',
    experiencia_requerida: '7 años',
    total_empleados: 6,
    estado: 'Activo'
  },
  {
    id: 6,
    nombre: 'Coordinador RRHH',
    departamento: 'Administración',
    descripcion: 'Gestión de recursos humanos y administración de personal',
    salario_base: 5500000,
    nivel_jerarquico: 'Coordinador',
    requisitos_educacion: 'Título en Administración o Psicología',
    experiencia_requerida: '5 años',
    total_empleados: 2,
    estado: 'Activo'
  },
  {
    id: 7,
    nombre: 'Auxiliar de Enfermería',
    departamento: 'Enfermería',
    descripcion: 'Apoyo en procedimientos básicos de enfermería',
    salario_base: 1800000,
    nivel_jerarquico: 'Auxiliar',
    requisitos_educacion: 'Técnico en Auxiliar de Enfermería',
    experiencia_requerida: '1 año',
    total_empleados: 25,
    estado: 'Activo'
  }
];

const GestionCargos: React.FC = () => {
  const [cargos, setCargos] = useState(mockCargos);
  const [searchTerm, setSearchTerm] = useState('');
  const [filtroDepartamento, setFiltroDepartamento] = useState<string>('todos');
  const [filtroNivel, setFiltroNivel] = useState<string>('todos');
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [editingCargo, setEditingCargo] = useState<any>(null);

  const filteredCargos = cargos.filter(cargo => {
    const matchesSearch = cargo.nombre.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         cargo.descripcion.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesDepartamento = filtroDepartamento === 'todos' || cargo.departamento === filtroDepartamento;
    const matchesNivel = filtroNivel === 'todos' || cargo.nivel_jerarquico === filtroNivel;

    return matchesSearch && matchesDepartamento && matchesNivel;
  });

  const formatCurrency = (value: number) => {
    return new Intl.NumberFormat('es-CO', {
      style: 'currency',
      currency: 'COP',
      minimumFractionDigits: 0
    }).format(value);
  };

  const getNivelColor = (nivel: string) => {
    switch (nivel) {
      case 'Senior':
        return 'bg-purple/20 text-purple-600';
      case 'Jefe':
        return 'bg-blue/20 text-blue-600';
      case 'Coordinador':
        return 'bg-green/20 text-green-600';
      case 'Profesional':
        return 'bg-info/20 text-info';
      case 'Auxiliar':
        return 'bg-warning/20 text-warning';
      default:
        return 'bg-secondary/20 text-secondary';
    }
  };

  const handleEdit = (cargo: any) => {
    setEditingCargo(cargo);
    setIsModalOpen(true);
    toast.info(`Editando cargo: ${cargo.nombre}`);
  };

  const handleDelete = (id: number) => {
    const cargo = cargos.find(c => c.id === id);
    if (window.confirm(`¿Está seguro de eliminar el cargo "${cargo?.nombre}"?`)) {
      setCargos(prev => prev.filter(c => c.id !== id));
      toast.success('Cargo eliminado exitosamente');
    }
  };

  const totalCargos = cargos.length;
  const totalEmpleados = cargos.reduce((sum, c) => sum + c.total_empleados, 0);
  const salarioPromedio = cargos.reduce((sum, c) => sum + c.salario_base, 0) / cargos.length;
  const cargosActivos = cargos.filter(c => c.estado === 'Activo').length;

  const departamentos = [...new Set(cargos.map(c => c.departamento))];
  const niveles = [...new Set(cargos.map(c => c.nivel_jerarquico))];

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="bg-card p-6 rounded-lg shadow-lg">
        <div className="flex justify-between items-center">
          <div>
            <h2 className="text-2xl font-bold text-primary flex items-center">
              <FontAwesomeIcon icon={faBriefcase} className="mr-2" />
              Gestión de Cargos
            </h2>
            <p className="text-muted mt-1">
              Administración de cargos y posiciones
            </p>
          </div>
          <Button onClick={() => setIsModalOpen(true)}>
            <FontAwesomeIcon icon={faPlus} className="mr-2" />
            Nuevo Cargo
          </Button>
        </div>
      </div>

      {/* Estadísticas */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <div className="bg-card p-6 rounded-lg shadow">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-muted">Total Cargos</p>
              <p className="text-2xl font-bold text-primary">{totalCargos}</p>
            </div>
            <FontAwesomeIcon icon={faBriefcase} className="text-primary text-2xl" />
          </div>
        </div>

        <div className="bg-card p-6 rounded-lg shadow">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-muted">Cargos Activos</p>
              <p className="text-2xl font-bold text-success">{cargosActivos}</p>
            </div>
            <FontAwesomeIcon icon={faUserTie} className="text-success text-2xl" />
          </div>
        </div>

        <div className="bg-card p-6 rounded-lg shadow">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-muted">Total Empleados</p>
              <p className="text-2xl font-bold text-info">{totalEmpleados}</p>
            </div>
            <FontAwesomeIcon icon={faUsers} className="text-info text-2xl" />
          </div>
        </div>

        <div className="bg-card p-6 rounded-lg shadow">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-muted">Salario Promedio</p>
              <p className="text-lg font-bold text-warning">{formatCurrency(salarioPromedio)}</p>
            </div>
            <FontAwesomeIcon icon={faMoneyBillWave} className="text-warning text-2xl" />
          </div>
        </div>
      </div>

      {/* Filtros */}
      <div className="bg-card p-6 rounded-lg shadow-lg">
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div>
            <label className="block text-sm font-medium text-secondary mb-1">
              Buscar
            </label>
            <div className="relative">
              <FontAwesomeIcon icon={faSearch} className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted" />
              <Input
                type="text"
                placeholder="Nombre del cargo..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10"
              />
            </div>
          </div>

          <div>
            <label className="block text-sm font-medium text-secondary mb-1">
              Departamento
            </label>
            <select
              value={filtroDepartamento}
              onChange={(e) => setFiltroDepartamento(e.target.value)}
              className="w-full p-3 rounded-lg bg-secondary border border-color text-primary focus:ring-2 focus:ring-primary"
            >
              <option value="todos">Todos los departamentos</option>
              {departamentos.map((dept) => (
                <option key={dept} value={dept}>
                  {dept}
                </option>
              ))}
            </select>
          </div>

          <div>
            <label className="block text-sm font-medium text-secondary mb-1">
              Nivel Jerárquico
            </label>
            <select
              value={filtroNivel}
              onChange={(e) => setFiltroNivel(e.target.value)}
              className="w-full p-3 rounded-lg bg-secondary border border-color text-primary focus:ring-2 focus:ring-primary"
            >
              <option value="todos">Todos los niveles</option>
              {niveles.map((nivel) => (
                <option key={nivel} value={nivel}>
                  {nivel}
                </option>
              ))}
            </select>
          </div>
        </div>
      </div>

      {/* Tabla de Cargos */}
      <div className="bg-card rounded-lg shadow-lg overflow-hidden">
        <div className="overflow-x-auto">
          <table className="w-full">
            <thead className="bg-secondary/50">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-secondary uppercase tracking-wider">
                  Cargo
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-secondary uppercase tracking-wider">
                  Departamento
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-secondary uppercase tracking-wider">
                  Nivel
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-secondary uppercase tracking-wider">
                  Salario Base
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-secondary uppercase tracking-wider">
                  Empleados
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-secondary uppercase tracking-wider">
                  Requisitos
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-secondary uppercase tracking-wider">
                  Acciones
                </th>
              </tr>
            </thead>
            <tbody className="bg-card divide-y divide-color">
              {filteredCargos.map((cargo) => (
                <tr key={cargo.id} className="hover:bg-secondary/20 transition-colors">
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div>
                      <div className="text-sm font-medium text-primary flex items-center">
                        <FontAwesomeIcon icon={faBriefcase} className="mr-2 text-muted" />
                        {cargo.nombre}
                      </div>
                      <div className="text-sm text-muted">
                        {cargo.descripcion}
                      </div>
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="text-sm text-primary flex items-center">
                      <FontAwesomeIcon icon={faBuilding} className="mr-2 text-muted" />
                      {cargo.departamento}
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getNivelColor(cargo.nivel_jerarquico)}`}>
                      {cargo.nivel_jerarquico}
                    </span>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="text-sm font-medium text-primary">
                      {formatCurrency(cargo.salario_base)}
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="text-sm font-medium text-primary flex items-center">
                      <FontAwesomeIcon icon={faUsers} className="mr-2 text-muted" />
                      {cargo.total_empleados}
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="text-sm text-primary">
                      <div className="flex items-center mb-1">
                        <FontAwesomeIcon icon={faGraduationCap} className="mr-2 text-muted" />
                        <span className="text-xs">{cargo.requisitos_educacion}</span>
                      </div>
                      <div className="text-xs text-muted">
                        Exp: {cargo.experiencia_requerida}
                      </div>
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                    <div className="flex space-x-2">
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => toast.info(`Viendo detalles de ${cargo.nombre}`)}
                        className="text-blue-600 border-blue-600 hover:bg-blue-50"
                      >
                        <FontAwesomeIcon icon={faEye} />
                      </Button>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => handleEdit(cargo)}
                        className="text-green-600 border-green-600 hover:bg-green-50"
                      >
                        <FontAwesomeIcon icon={faEdit} />
                      </Button>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => handleDelete(cargo.id)}
                        className="text-red-600 border-red-600 hover:bg-red-50"
                      >
                        <FontAwesomeIcon icon={faTrash} />
                      </Button>
                    </div>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>

        {filteredCargos.length === 0 && (
          <div className="text-center py-12">
            <FontAwesomeIcon icon={faBriefcase} className="text-muted text-4xl mb-4" />
            <p className="text-muted text-lg">No se encontraron cargos</p>
            <p className="text-muted">Intenta ajustar los filtros de búsqueda</p>
          </div>
        )}
      </div>

      {/* Modal Funcional con Glassmorphism */}
      {isModalOpen && (
        <div className="fixed inset-0 bg-black/60 backdrop-blur-sm flex items-center justify-center z-50 p-4 animate-in fade-in duration-300">
          <div className="glass-modal modal-container max-h-[90vh] overflow-y-auto custom-scrollbar animate-in slide-in-from-bottom-4 duration-300">
            <div className="p-8">
              <div className="flex justify-between items-center mb-8">
                <div className="flex items-center space-x-4">
                  <div className="w-12 h-12 rounded-xl bg-gradient-to-br from-blue-500/20 to-purple-600/20 flex items-center justify-center">
                    <FontAwesomeIcon icon={faBriefcase} className="text-xl text-blue-600 dark:text-blue-400" />
                  </div>
                  <div>
                    <h3 className="text-2xl font-bold text-adaptive">
                      {editingCargo ? 'Editar Cargo' : 'Nuevo Cargo'}
                    </h3>
                    <p className="text-adaptive-muted text-sm">
                      {editingCargo ? 'Modifica la información del cargo' : 'Completa la información para crear un nuevo cargo'}
                    </p>
                  </div>
                </div>
                <button
                  onClick={() => {
                    setIsModalOpen(false);
                    setEditingCargo(null);
                  }}
                  className="glass-card p-3 rounded-xl text-adaptive-muted hover:text-adaptive transition-all duration-200 transform hover:scale-105"
                >
                  <FontAwesomeIcon icon={faTimes} className="text-lg" />
                </button>
              </div>

              <form onSubmit={(e) => {
                e.preventDefault();
                const formData = new FormData(e.target as HTMLFormElement);
                const nuevoCargo = {
                  id: editingCargo ? editingCargo.id : Date.now(),
                  nombre: formData.get('nombre') as string,
                  departamento: formData.get('departamento') as string,
                  descripcion: formData.get('descripcion') as string,
                  salario_base: parseInt(formData.get('salario_base') as string),
                  nivel_jerarquico: formData.get('nivel_jerarquico') as string,
                  requisitos_educacion: formData.get('requisitos_educacion') as string,
                  experiencia_requerida: formData.get('experiencia_requerida') as string,
                  total_empleados: editingCargo ? editingCargo.total_empleados : 0,
                  estado: 'Activo'
                };

                if (editingCargo) {
                  setCargos(prev => prev.map(c => c.id === editingCargo.id ? nuevoCargo : c));
                  toast.success('Cargo actualizado exitosamente');
                } else {
                  setCargos(prev => [...prev, nuevoCargo]);
                  toast.success('Cargo creado exitosamente');
                }

                setIsModalOpen(false);
                setEditingCargo(null);
              }}>
                <div className="form-grid">
                  {/* Nombre del Cargo */}
                  <div className="form-grid-full">
                    <label className="block text-sm font-semibold text-adaptive mb-3 flex items-center">
                      <div className="w-6 h-6 rounded-lg bg-blue-500/10 flex items-center justify-center mr-3">
                        <FontAwesomeIcon icon={faBriefcase} className="text-xs text-blue-600 dark:text-blue-400" />
                      </div>
                      Nombre del Cargo *
                    </label>
                    <Input
                      name="nombre"
                      type="text"
                      required
                      defaultValue={editingCargo?.nombre || ''}
                      placeholder="Ej: Médico Especialista"
                      className="w-full"
                    />
                  </div>

                  {/* Departamento */}
                  <div>
                    <label className="block text-sm font-medium text-secondary mb-2">
                      <FontAwesomeIcon icon={faBuilding} className="mr-2" />
                      Departamento *
                    </label>
                    <select
                      name="departamento"
                      required
                      defaultValue={editingCargo?.departamento || ''}
                      className="w-full p-3 rounded-lg bg-secondary border border-color text-primary focus:ring-2 focus:ring-primary"
                    >
                      <option value="">Seleccionar departamento</option>
                      {departamentos.map((dept) => (
                        <option key={dept} value={dept}>
                          {dept}
                        </option>
                      ))}
                    </select>
                  </div>

                  {/* Nivel Jerárquico */}
                  <div>
                    <label className="block text-sm font-medium text-secondary mb-2">
                      <FontAwesomeIcon icon={faUserTie} className="mr-2" />
                      Nivel Jerárquico *
                    </label>
                    <select
                      name="nivel_jerarquico"
                      required
                      defaultValue={editingCargo?.nivel_jerarquico || ''}
                      className="w-full p-3 rounded-lg bg-secondary border border-color text-primary focus:ring-2 focus:ring-primary"
                    >
                      <option value="">Seleccionar nivel</option>
                      <option value="Senior">Senior</option>
                      <option value="Jefe">Jefe</option>
                      <option value="Coordinador">Coordinador</option>
                      <option value="Profesional">Profesional</option>
                      <option value="Auxiliar">Auxiliar</option>
                    </select>
                  </div>

                  {/* Salario Base */}
                  <div>
                    <label className="block text-sm font-medium text-secondary mb-2">
                      <FontAwesomeIcon icon={faMoneyBillWave} className="mr-2" />
                      Salario Base *
                    </label>
                    <Input
                      name="salario_base"
                      type="number"
                      required
                      min="0"
                      step="1000"
                      defaultValue={editingCargo?.salario_base || ''}
                      placeholder="Ej: 5000000"
                      className="w-full"
                    />
                  </div>

                  {/* Experiencia Requerida */}
                  <div>
                    <label className="block text-sm font-medium text-secondary mb-2">
                      <FontAwesomeIcon icon={faClock} className="mr-2" />
                      Experiencia Requerida *
                    </label>
                    <Input
                      name="experiencia_requerida"
                      type="text"
                      required
                      defaultValue={editingCargo?.experiencia_requerida || ''}
                      placeholder="Ej: 3 años"
                      className="w-full"
                    />
                  </div>

                  {/* Requisitos de Educación */}
                  <div className="md:col-span-2">
                    <label className="block text-sm font-medium text-secondary mb-2">
                      <FontAwesomeIcon icon={faGraduationCap} className="mr-2" />
                      Requisitos de Educación *
                    </label>
                    <Input
                      name="requisitos_educacion"
                      type="text"
                      required
                      defaultValue={editingCargo?.requisitos_educacion || ''}
                      placeholder="Ej: Título de Medicina + Especialización"
                      className="w-full"
                    />
                  </div>

                  {/* Descripción */}
                  <div className="md:col-span-2">
                    <label className="block text-sm font-medium text-secondary mb-2">
                      <FontAwesomeIcon icon={faFileAlt} className="mr-2" />
                      Descripción del Cargo *
                    </label>
                    <textarea
                      name="descripcion"
                      required
                      rows={3}
                      defaultValue={editingCargo?.descripcion || ''}
                      placeholder="Descripción detallada de las responsabilidades y funciones del cargo..."
                      className="w-full p-3 rounded-lg bg-secondary border border-color text-primary focus:ring-2 focus:ring-primary resize-none"
                    />
                  </div>
                </div>

                <div className="flex justify-end gap-4 mt-8 pt-6 border-t border-white/10 dark:border-black/10">
                  <Button
                    type="button"
                    variant="ghost"
                    onClick={() => {
                      setIsModalOpen(false);
                      setEditingCargo(null);
                    }}
                    className="min-w-[120px]"
                  >
                    <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M6 18L18 6M6 6l12 12" />
                    </svg>
                    Cancelar
                  </Button>
                  <Button type="submit" className="min-w-[140px]">
                    <FontAwesomeIcon icon={editingCargo ? faEdit : faPlus} className="mr-2" />
                    {editingCargo ? 'Actualizar' : 'Crear'} Cargo
                  </Button>
                </div>
              </form>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default GestionCargos;
