import React, { useState } from 'react';
import { Button } from '../../components/ui/Button';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { 
  faBoxes, 
  faChartLine, 
  faExclamationTriangle,
  faCheckCircle,
  faClock,
  faWarehouse,
  faPills,
  faStethoscope,
  faTruck,
  faCalendarAlt,
  faArrowUp,
  faArrowDown,
  faDollarSign,
  faPercent,
  faRefresh
} from '@fortawesome/free-solid-svg-icons';

// Datos mock para el dashboard
const dashboardData = {
  resumenGeneral: {
    totalItems: 1247,
    valorTotal: 45678900,
    itemsCriticos: 121,
    itemsBajos: 234,
    itemsNormales: 892,
    rotacionPromedio: 15.3,
    itemsProximosVencer: 67,
    itemsSinMovimiento: 89
  },
  tendencias: {
    consumoMensual: [
      { mes: 'Ene', valor: 2500000, items: 145 },
      { mes: 'Feb', valor: 2750000, items: 167 },
      { mes: 'Mar', valor: 2300000, items: 134 },
      { mes: 'Abr', valor: 2900000, items: 178 },
      { mes: 'May', valor: 3100000, items: 189 },
      { mes: 'Jun', valor: 2800000, items: 156 }
    ],
    topConsumo: [
      { nombre: 'Paracetamol 500mg', consumo: 450, valor: 112500 },
      { nombre: 'Jeringas 10ml', consumo: 320, valor: 48000 },
      { nombre: 'Gasas Estériles', consumo: 280, valor: 140000 },
      { nombre: 'Suero Fisiológico', consumo: 250, valor: 75000 },
      { nombre: 'Guantes Látex', consumo: 200, valor: 30000 }
    ],
    alertasStock: [
      { item: 'Gasas Estériles', stock: 5, minimo: 30, criticidad: 'Crítico' },
      { item: 'Jeringas 10ml', stock: 25, minimo: 50, criticidad: 'Bajo' },
      { item: 'Alcohol Antiséptico', stock: 8, minimo: 40, criticidad: 'Crítico' },
      { item: 'Vendas Elásticas', stock: 15, minimo: 25, criticidad: 'Bajo' }
    ]
  },
  distribucionTipos: [
    { tipo: 'Medicamentos', cantidad: 456, porcentaje: 36.6, valor: 18500000 },
    { tipo: 'Material Médico', cantidad: 312, porcentaje: 25.0, valor: 12300000 },
    { tipo: 'Insumos', cantidad: 289, porcentaje: 23.2, valor: 8900000 },
    { tipo: 'Equipamiento', cantidad: 134, porcentaje: 10.7, valor: 4800000 },
    { tipo: 'Otros', cantidad: 56, porcentaje: 4.5, valor: 1178900 }
  ]
};

export const DashboardInventario: React.FC = () => {
  const [ultimaActualizacion, setUltimaActualizacion] = useState(new Date());

  const actualizarDatos = () => {
    setUltimaActualizacion(new Date());
    // TODO: Implementar actualización real de datos
  };

  const formatCurrency = (value: number) => {
    return new Intl.NumberFormat('es-CO', {
      style: 'currency',
      currency: 'COP',
      minimumFractionDigits: 0
    }).format(value);
  };

  const getCriticidadColor = (criticidad: string) => {
    switch (criticidad) {
      case 'Crítico':
        return 'text-error';
      case 'Bajo':
        return 'text-warning';
      default:
        return 'text-success';
    }
  };

  return (
    <div className="space-y-6">
      {/* Header del Dashboard */}
      <div className="bg-card p-6 rounded-lg shadow-lg">
        <div className="flex justify-between items-center">
          <div>
            <h1 className="text-2xl font-bold text-primary flex items-center">
              <FontAwesomeIcon icon={faChartLine} className="mr-2" />
              Dashboard de Inventario
            </h1>
            <p className="text-muted mt-1">
              Vista general del estado del inventario en tiempo real
            </p>
          </div>
          <div className="flex items-center gap-4">
            <div className="text-right">
              <div className="text-sm text-muted">Última actualización</div>
              <div className="text-primary font-medium">{ultimaActualizacion.toLocaleString()}</div>
            </div>
            <Button variant="outline" onClick={actualizarDatos}>
              <FontAwesomeIcon icon={faRefresh} className="mr-2" />
              Actualizar
            </Button>
          </div>
        </div>
      </div>

      {/* Métricas Principales */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <div className="bg-card p-6 rounded-lg shadow-lg">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-muted">Total de Items</p>
              <p className="text-3xl font-bold text-primary">{dashboardData.resumenGeneral.totalItems}</p>
              <p className="text-sm text-success flex items-center mt-1">
                <FontAwesomeIcon icon={faArrowUp} className="mr-1" />
                +5.2% vs mes anterior
              </p>
            </div>
            <div className="bg-primary/20 p-3 rounded-full">
              <FontAwesomeIcon icon={faBoxes} className="text-primary text-2xl" />
            </div>
          </div>
        </div>

        <div className="bg-card p-6 rounded-lg shadow-lg">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-muted">Valor Total</p>
              <p className="text-2xl font-bold text-success">{formatCurrency(dashboardData.resumenGeneral.valorTotal)}</p>
              <p className="text-sm text-success flex items-center mt-1">
                <FontAwesomeIcon icon={faArrowUp} className="mr-1" />
                +8.1% vs mes anterior
              </p>
            </div>
            <div className="bg-success/20 p-3 rounded-full">
              <FontAwesomeIcon icon={faDollarSign} className="text-success text-2xl" />
            </div>
          </div>
        </div>

        <div className="bg-card p-6 rounded-lg shadow-lg">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-muted">Items Críticos</p>
              <p className="text-3xl font-bold text-error">{dashboardData.resumenGeneral.itemsCriticos}</p>
              <p className="text-sm text-error flex items-center mt-1">
                <FontAwesomeIcon icon={faArrowUp} className="mr-1" />
                +12 vs semana anterior
              </p>
            </div>
            <div className="bg-error/20 p-3 rounded-full">
              <FontAwesomeIcon icon={faExclamationTriangle} className="text-error text-2xl" />
            </div>
          </div>
        </div>

        <div className="bg-card p-6 rounded-lg shadow-lg">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-muted">Rotación Promedio</p>
              <p className="text-3xl font-bold text-info">{dashboardData.resumenGeneral.rotacionPromedio} días</p>
              <p className="text-sm text-success flex items-center mt-1">
                <FontAwesomeIcon icon={faArrowDown} className="mr-1" />
                -2.3 días vs mes anterior
              </p>
            </div>
            <div className="bg-info/20 p-3 rounded-full">
              <FontAwesomeIcon icon={faChartLine} className="text-info text-2xl" />
            </div>
          </div>
        </div>
      </div>

      {/* Distribución por Estado */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <div className="bg-card p-6 rounded-lg shadow-lg">
          <h3 className="text-lg font-semibold text-primary mb-4 flex items-center">
            <FontAwesomeIcon icon={faCheckCircle} className="mr-2" />
            📊 Distribución por Estado de Stock
          </h3>
          
          <div className="space-y-4">
            <div className="flex items-center justify-between p-3 bg-success/20 rounded-lg">
              <div className="flex items-center">
                <FontAwesomeIcon icon={faCheckCircle} className="text-success mr-3" />
                <span className="font-medium text-primary">Stock Normal</span>
              </div>
              <div className="text-right">
                <div className="text-xl font-bold text-success">{dashboardData.resumenGeneral.itemsNormales}</div>
                <div className="text-sm text-muted">
                  {((dashboardData.resumenGeneral.itemsNormales / dashboardData.resumenGeneral.totalItems) * 100).toFixed(1)}%
                </div>
              </div>
            </div>

            <div className="flex items-center justify-between p-3 bg-warning/20 rounded-lg">
              <div className="flex items-center">
                <FontAwesomeIcon icon={faClock} className="text-warning mr-3" />
                <span className="font-medium text-primary">Stock Bajo</span>
              </div>
              <div className="text-right">
                <div className="text-xl font-bold text-warning">{dashboardData.resumenGeneral.itemsBajos}</div>
                <div className="text-sm text-muted">
                  {((dashboardData.resumenGeneral.itemsBajos / dashboardData.resumenGeneral.totalItems) * 100).toFixed(1)}%
                </div>
              </div>
            </div>

            <div className="flex items-center justify-between p-3 bg-error/20 rounded-lg">
              <div className="flex items-center">
                <FontAwesomeIcon icon={faExclamationTriangle} className="text-error mr-3" />
                <span className="font-medium text-primary">Stock Crítico</span>
              </div>
              <div className="text-right">
                <div className="text-xl font-bold text-error">{dashboardData.resumenGeneral.itemsCriticos}</div>
                <div className="text-sm text-muted">
                  {((dashboardData.resumenGeneral.itemsCriticos / dashboardData.resumenGeneral.totalItems) * 100).toFixed(1)}%
                </div>
              </div>
            </div>
          </div>
        </div>

        <div className="bg-card p-6 rounded-lg shadow-lg">
          <h3 className="text-lg font-semibold text-primary mb-4 flex items-center">
            <FontAwesomeIcon icon={faBoxes} className="mr-2" />
            📈 Distribución por Tipo de Recurso
          </h3>
          
          <div className="space-y-3">
            {dashboardData.distribucionTipos.map((tipo, index) => (
              <div key={index} className="flex items-center justify-between p-3 bg-secondary/20 rounded-lg">
                <div className="flex items-center">
                  <div className="w-3 h-3 rounded-full mr-3" style={{
                    backgroundColor: ['#3B82F6', '#10B981', '#F59E0B', '#EF4444', '#8B5CF6'][index]
                  }}></div>
                  <span className="font-medium text-primary">{tipo.tipo}</span>
                </div>
                <div className="text-right">
                  <div className="text-lg font-bold text-primary">{tipo.cantidad}</div>
                  <div className="text-sm text-muted">{tipo.porcentaje}%</div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* Top Consumo y Alertas */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <div className="bg-card p-6 rounded-lg shadow-lg">
          <h3 className="text-lg font-semibold text-primary mb-4 flex items-center">
            <FontAwesomeIcon icon={faChartLine} className="mr-2" />
            🔥 Top 5 - Mayor Consumo
          </h3>
          
          <div className="space-y-3">
            {dashboardData.tendencias.topConsumo.map((item, index) => (
              <div key={index} className="flex items-center justify-between p-3 bg-secondary/20 rounded-lg">
                <div className="flex items-center">
                  <div className="bg-primary text-white rounded-full w-6 h-6 flex items-center justify-center text-sm font-bold mr-3">
                    {index + 1}
                  </div>
                  <div>
                    <div className="font-medium text-primary">{item.nombre}</div>
                    <div className="text-sm text-muted">{formatCurrency(item.valor)}</div>
                  </div>
                </div>
                <div className="text-right">
                  <div className="text-lg font-bold text-primary">{item.consumo}</div>
                  <div className="text-sm text-muted">unidades</div>
                </div>
              </div>
            ))}
          </div>
        </div>

        <div className="bg-card p-6 rounded-lg shadow-lg">
          <h3 className="text-lg font-semibold text-primary mb-4 flex items-center">
            <FontAwesomeIcon icon={faExclamationTriangle} className="mr-2" />
            🚨 Alertas de Stock
          </h3>
          
          <div className="space-y-3">
            {dashboardData.tendencias.alertasStock.map((alerta, index) => (
              <div key={index} className="flex items-center justify-between p-3 bg-secondary/20 rounded-lg border-l-4 border-l-error">
                <div>
                  <div className="font-medium text-primary">{alerta.item}</div>
                  <div className="text-sm text-muted">
                    Stock: {alerta.stock} / Mínimo: {alerta.minimo}
                  </div>
                </div>
                <div className="text-right">
                  <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                    alerta.criticidad === 'Crítico' ? 'bg-error/20 text-error' : 'bg-warning/20 text-warning'
                  }`}>
                    {alerta.criticidad}
                  </span>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* Indicadores Adicionales */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <div className="bg-card p-6 rounded-lg shadow-lg">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-muted">Próximos a Vencer</p>
              <p className="text-2xl font-bold text-warning">{dashboardData.resumenGeneral.itemsProximosVencer}</p>
              <p className="text-sm text-muted">En los próximos 30 días</p>
            </div>
            <FontAwesomeIcon icon={faCalendarAlt} className="text-warning text-3xl" />
          </div>
        </div>

        <div className="bg-card p-6 rounded-lg shadow-lg">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-muted">Sin Movimiento</p>
              <p className="text-2xl font-bold text-error">{dashboardData.resumenGeneral.itemsSinMovimiento}</p>
              <p className="text-sm text-muted">Últimos 90 días</p>
            </div>
            <FontAwesomeIcon icon={faWarehouse} className="text-error text-3xl" />
          </div>
        </div>

        <div className="bg-card p-6 rounded-lg shadow-lg">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-muted">Eficiencia de Stock</p>
              <p className="text-2xl font-bold text-success">87.3%</p>
              <p className="text-sm text-muted">Índice de optimización</p>
            </div>
            <FontAwesomeIcon icon={faPercent} className="text-success text-3xl" />
          </div>
        </div>
      </div>
    </div>
  );
};
