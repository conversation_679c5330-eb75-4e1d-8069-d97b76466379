import React, { useState } from 'react';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import {
  faFileInvoiceDollar,
  faDownload,
  faUsers,
  faMoneyBillWave,
  faCalendarAlt,
  faChartBar,
  faFileExcel,
  faFilePdf,
  faChartLine,
  faChartPie,
  faClock,
  faPlane,
  faHeart,
  faTools,
  faGraduationCap,
  faAward,
  faFilter,
  faEye
} from '@fortawesome/free-solid-svg-icons';
import { Button } from '../../../components/ui/Button';
import { toast } from 'react-hot-toast';

// Tipos de reportes disponibles
const tiposReportes = [
  {
    id: 'empleados',
    nombre: 'Reporte de Empleados',
    descripcion: 'Listado completo de empleados con información personal y laboral',
    icon: faUsers,
    color: 'text-blue-600',
    categoria: 'Personal'
  },
  {
    id: 'nomina',
    nombre: 'Reporte de Nómina',
    descripcion: 'Liquidación de nómina por período con detalles de pagos',
    icon: faMoneyBillWave,
    color: 'text-green-600',
    categoria: 'Financiero'
  },
  {
    id: 'asistencia',
    nombre: 'Reporte de Asistencia',
    descripcion: 'Control de asistencia, tardanzas y ausentismo',
    icon: faClock,
    color: 'text-orange-600',
    categoria: 'Operacional'
  },
  {
    id: 'vacaciones',
    nombre: 'Reporte de Vacaciones',
    descripcion: 'Estado de vacaciones, solicitudes y programación',
    icon: faPlane,
    color: 'text-cyan-600',
    categoria: 'Personal'
  },
  {
    id: 'incapacidades',
    nombre: 'Reporte de Incapacidades',
    descripcion: 'Incapacidades médicas y ausentismo por salud',
    icon: faHeart,
    color: 'text-red-600',
    categoria: 'Médico'
  },
  {
    id: 'dotaciones',
    nombre: 'Reporte de Dotaciones',
    descripcion: 'Inventario de dotaciones entregadas y estado',
    icon: faTools,
    color: 'text-purple-600',
    categoria: 'Inventario'
  },
  {
    id: 'capacitaciones',
    nombre: 'Reporte de Capacitaciones',
    descripcion: 'Programas de capacitación y desarrollo del personal',
    icon: faGraduationCap,
    color: 'text-indigo-600',
    categoria: 'Desarrollo'
  },
  {
    id: 'evaluaciones',
    nombre: 'Reporte de Evaluaciones',
    descripcion: 'Evaluaciones de desempeño y competencias',
    icon: faAward,
    color: 'text-yellow-600',
    categoria: 'Desarrollo'
  },
  {
    id: 'costos',
    nombre: 'Análisis de Costos',
    descripcion: 'Análisis de costos de personal por departamento',
    icon: faChartLine,
    color: 'text-emerald-600',
    categoria: 'Financiero'
  },
  {
    id: 'demografico',
    nombre: 'Análisis Demográfico',
    descripcion: 'Distribución demográfica del personal',
    icon: faChartPie,
    color: 'text-pink-600',
    categoria: 'Analítico'
  }
];

const ReportesRRHH: React.FC = () => {
  const [filtroCategoria, setFiltroCategoria] = useState<string>('todos');
  const [reporteSeleccionado, setReporteSeleccionado] = useState<string | null>(null);

  const categorias = [...new Set(tiposReportes.map(r => r.categoria))];

  const reportesFiltrados = tiposReportes.filter(reporte =>
    filtroCategoria === 'todos' || reporte.categoria === filtroCategoria
  );

  const generarReporte = (tipoReporte: string, formato: 'excel' | 'pdf') => {
    const reporte = tiposReportes.find(r => r.id === tipoReporte);
    toast.success(`Generando ${reporte?.nombre} en formato ${formato.toUpperCase()}`);
  };

  const previsualizarReporte = (tipoReporte: string) => {
    const reporte = tiposReportes.find(r => r.id === tipoReporte);
    setReporteSeleccionado(tipoReporte);
    toast.info(`Previsualizando ${reporte?.nombre}`);
  };

  const estadisticasReportes = {
    reportesGenerados: 156,
    reportesEstesMes: 23,
    formatoMasUsado: 'Excel',
    categoriaPopular: 'Personal'
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="bg-card p-6 rounded-lg shadow-lg">
        <div className="flex justify-between items-center">
          <div>
            <h2 className="text-2xl font-bold text-primary flex items-center">
              <FontAwesomeIcon icon={faFileInvoiceDollar} className="mr-2" />
              Reportes de RRHH
            </h2>
            <p className="text-muted mt-1">
              Reportes y análisis de recursos humanos
            </p>
          </div>
          <div className="flex gap-2">
            <Button variant="outline" onClick={() => toast.info('Programando reportes automáticos')}>
              <FontAwesomeIcon icon={faCalendarAlt} className="mr-2" />
              Programar
            </Button>
            <Button onClick={() => toast.info('Generando reporte personalizado')}>
              <FontAwesomeIcon icon={faDownload} className="mr-2" />
              Generar Reporte
            </Button>
          </div>
        </div>
      </div>

      {/* Estadísticas Rápidas */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <div className="bg-card p-6 rounded-lg shadow">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-muted">Reportes Generados</p>
              <p className="text-2xl font-bold text-primary">{estadisticasReportes.reportesGenerados}</p>
            </div>
            <FontAwesomeIcon icon={faChartBar} className="text-primary text-2xl" />
          </div>
        </div>

        <div className="bg-card p-6 rounded-lg shadow">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-muted">Este Mes</p>
              <p className="text-2xl font-bold text-success">{estadisticasReportes.reportesEstesMes}</p>
            </div>
            <FontAwesomeIcon icon={faCalendarAlt} className="text-success text-2xl" />
          </div>
        </div>

        <div className="bg-card p-6 rounded-lg shadow">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-muted">Formato Popular</p>
              <p className="text-lg font-bold text-info">{estadisticasReportes.formatoMasUsado}</p>
            </div>
            <FontAwesomeIcon icon={faFileExcel} className="text-info text-2xl" />
          </div>
        </div>

        <div className="bg-card p-6 rounded-lg shadow">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-muted">Categoría Popular</p>
              <p className="text-lg font-bold text-warning">{estadisticasReportes.categoriaPopular}</p>
            </div>
            <FontAwesomeIcon icon={faUsers} className="text-warning text-2xl" />
          </div>
        </div>
      </div>

      {/* Filtro por Categoría */}
      <div className="bg-card p-6 rounded-lg shadow-lg">
        <div className="flex items-center gap-4">
          <FontAwesomeIcon icon={faFilter} className="text-muted" />
          <label className="text-sm font-medium text-secondary">
            Filtrar por categoría:
          </label>
          <select
            value={filtroCategoria}
            onChange={(e) => setFiltroCategoria(e.target.value)}
            className="p-2 rounded-lg bg-secondary border border-color text-primary focus:ring-2 focus:ring-primary"
          >
            <option value="todos">Todas las categorías</option>
            {categorias.map((categoria) => (
              <option key={categoria} value={categoria}>
                {categoria}
              </option>
            ))}
          </select>
        </div>
      </div>

      {/* Grid de Reportes */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {reportesFiltrados.map((reporte) => (
          <div key={reporte.id} className="bg-card p-6 rounded-lg shadow-lg hover:shadow-xl transition-shadow">
            <div className="flex items-start justify-between mb-4">
              <div className="flex items-center">
                <div className={`p-3 rounded-lg bg-secondary/20 mr-3`}>
                  <FontAwesomeIcon icon={reporte.icon} className={`text-xl ${reporte.color}`} />
                </div>
                <div>
                  <h3 className="text-lg font-semibold text-primary">{reporte.nombre}</h3>
                  <span className={`text-xs px-2 py-1 rounded-full bg-secondary/20 ${reporte.color}`}>
                    {reporte.categoria}
                  </span>
                </div>
              </div>
            </div>

            <p className="text-muted text-sm mb-4">
              {reporte.descripcion}
            </p>

            <div className="flex gap-2">
              <Button
                variant="outline"
                size="sm"
                onClick={() => previsualizarReporte(reporte.id)}
                className="flex-1 text-blue-600 border-blue-600 hover:bg-blue-50"
              >
                <FontAwesomeIcon icon={faEye} className="mr-2" />
                Vista Previa
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={() => generarReporte(reporte.id, 'excel')}
                className="text-green-600 border-green-600 hover:bg-green-50"
              >
                <FontAwesomeIcon icon={faFileExcel} />
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={() => generarReporte(reporte.id, 'pdf')}
                className="text-red-600 border-red-600 hover:bg-red-50"
              >
                <FontAwesomeIcon icon={faFilePdf} />
              </Button>
            </div>
          </div>
        ))}
      </div>

      {/* Sección de Reportes Programados */}
      <div className="bg-card p-6 rounded-lg shadow-lg">
        <h3 className="text-lg font-semibold text-primary mb-4 flex items-center">
          <FontAwesomeIcon icon={faCalendarAlt} className="mr-2" />
          Reportes Programados
        </h3>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          <div className="border border-color rounded-lg p-4">
            <div className="flex items-center justify-between mb-2">
              <h4 className="font-medium text-primary">Nómina Mensual</h4>
              <span className="text-xs bg-success/20 text-success px-2 py-1 rounded-full">Activo</span>
            </div>
            <p className="text-sm text-muted mb-2">Cada último día del mes</p>
            <p className="text-xs text-muted">Próxima ejecución: 31/01/2024</p>
          </div>

          <div className="border border-color rounded-lg p-4">
            <div className="flex items-center justify-between mb-2">
              <h4 className="font-medium text-primary">Asistencia Semanal</h4>
              <span className="text-xs bg-success/20 text-success px-2 py-1 rounded-full">Activo</span>
            </div>
            <p className="text-sm text-muted mb-2">Cada lunes</p>
            <p className="text-xs text-muted">Próxima ejecución: 29/01/2024</p>
          </div>

          <div className="border border-color rounded-lg p-4">
            <div className="flex items-center justify-between mb-2">
              <h4 className="font-medium text-primary">Vacaciones Trimestrales</h4>
              <span className="text-xs bg-warning/20 text-warning px-2 py-1 rounded-full">Pausado</span>
            </div>
            <p className="text-sm text-muted mb-2">Cada trimestre</p>
            <p className="text-xs text-muted">Próxima ejecución: 31/03/2024</p>
          </div>
        </div>
      </div>

      {/* Historial de Reportes Recientes */}
      <div className="bg-card p-6 rounded-lg shadow-lg">
        <h3 className="text-lg font-semibold text-primary mb-4 flex items-center">
          <FontAwesomeIcon icon={faChartBar} className="mr-2" />
          Reportes Recientes
        </h3>

        <div className="overflow-x-auto">
          <table className="w-full">
            <thead className="bg-secondary/50">
              <tr>
                <th className="px-4 py-2 text-left text-xs font-medium text-secondary uppercase">
                  Reporte
                </th>
                <th className="px-4 py-2 text-left text-xs font-medium text-secondary uppercase">
                  Fecha
                </th>
                <th className="px-4 py-2 text-left text-xs font-medium text-secondary uppercase">
                  Formato
                </th>
                <th className="px-4 py-2 text-left text-xs font-medium text-secondary uppercase">
                  Usuario
                </th>
                <th className="px-4 py-2 text-left text-xs font-medium text-secondary uppercase">
                  Estado
                </th>
                <th className="px-4 py-2 text-left text-xs font-medium text-secondary uppercase">
                  Acciones
                </th>
              </tr>
            </thead>
            <tbody className="divide-y divide-color">
              <tr className="hover:bg-secondary/20">
                <td className="px-4 py-3 text-sm text-primary">Reporte de Nómina - Enero 2024</td>
                <td className="px-4 py-3 text-sm text-muted">25/01/2024 14:30</td>
                <td className="px-4 py-3">
                  <span className="text-xs bg-green-100 text-green-800 px-2 py-1 rounded-full">Excel</span>
                </td>
                <td className="px-4 py-3 text-sm text-muted">Patricia Hernández</td>
                <td className="px-4 py-3">
                  <span className="text-xs bg-success/20 text-success px-2 py-1 rounded-full">Completado</span>
                </td>
                <td className="px-4 py-3">
                  <Button variant="outline" size="sm" onClick={() => toast.info('Descargando reporte')}>
                    <FontAwesomeIcon icon={faDownload} />
                  </Button>
                </td>
              </tr>
              <tr className="hover:bg-secondary/20">
                <td className="px-4 py-3 text-sm text-primary">Análisis de Asistencia - Semana 3</td>
                <td className="px-4 py-3 text-sm text-muted">22/01/2024 09:15</td>
                <td className="px-4 py-3">
                  <span className="text-xs bg-red-100 text-red-800 px-2 py-1 rounded-full">PDF</span>
                </td>
                <td className="px-4 py-3 text-sm text-muted">Carlos Rodríguez</td>
                <td className="px-4 py-3">
                  <span className="text-xs bg-success/20 text-success px-2 py-1 rounded-full">Completado</span>
                </td>
                <td className="px-4 py-3">
                  <Button variant="outline" size="sm" onClick={() => toast.info('Descargando reporte')}>
                    <FontAwesomeIcon icon={faDownload} />
                  </Button>
                </td>
              </tr>
              <tr className="hover:bg-secondary/20">
                <td className="px-4 py-3 text-sm text-primary">Reporte de Dotaciones - Q4 2023</td>
                <td className="px-4 py-3 text-sm text-muted">20/01/2024 16:45</td>
                <td className="px-4 py-3">
                  <span className="text-xs bg-green-100 text-green-800 px-2 py-1 rounded-full">Excel</span>
                </td>
                <td className="px-4 py-3 text-sm text-muted">María López</td>
                <td className="px-4 py-3">
                  <span className="text-xs bg-warning/20 text-warning px-2 py-1 rounded-full">Procesando</span>
                </td>
                <td className="px-4 py-3">
                  <Button variant="outline" size="sm" disabled>
                    <FontAwesomeIcon icon={faClock} />
                  </Button>
                </td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>

      {/* Accesos Rápidos */}
      <div className="bg-card p-6 rounded-lg shadow-lg">
        <h3 className="text-lg font-semibold text-primary mb-4">
          Accesos Rápidos
        </h3>

        <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
          <Button
            variant="outline"
            className="h-20 flex flex-col items-center justify-center"
            onClick={() => generarReporte('nomina', 'excel')}
          >
            <FontAwesomeIcon icon={faMoneyBillWave} className="text-2xl text-green-600 mb-2" />
            <span className="text-sm">Nómina Actual</span>
          </Button>

          <Button
            variant="outline"
            className="h-20 flex flex-col items-center justify-center"
            onClick={() => generarReporte('asistencia', 'pdf')}
          >
            <FontAwesomeIcon icon={faClock} className="text-2xl text-orange-600 mb-2" />
            <span className="text-sm">Asistencia Hoy</span>
          </Button>

          <Button
            variant="outline"
            className="h-20 flex flex-col items-center justify-center"
            onClick={() => generarReporte('empleados', 'excel')}
          >
            <FontAwesomeIcon icon={faUsers} className="text-2xl text-blue-600 mb-2" />
            <span className="text-sm">Lista Personal</span>
          </Button>

          <Button
            variant="outline"
            className="h-20 flex flex-col items-center justify-center"
            onClick={() => generarReporte('vacaciones', 'pdf')}
          >
            <FontAwesomeIcon icon={faPlane} className="text-2xl text-cyan-600 mb-2" />
            <span className="text-sm">Vacaciones</span>
          </Button>
        </div>
      </div>
    </div>
  );
};

export default ReportesRRHH;
