import React, { useEffect, useState } from 'react';
import { 
  Truck, 
  Clock, 
  AlertTriangle, 
  CheckCircle, 
  Activity,
  MapPin,
  Phone,
  Users,
  TrendingUp,
  TrendingDown,
  Minus,
  RefreshCw
} from 'lucide-react';
import { useAmbulanceStore, useAmbulanceMetrics } from '../../store/ambulance.store';
import { wsService } from '../../services/websocket.service';
import AmbulanceMap from './AmbulanceMap';

// Componente de métrica
const MetricCard: React.FC<{
  title: string;
  value: number | string;
  icon: React.ReactNode;
  trend?: 'up' | 'down' | 'stable';
  trendValue?: string;
  color?: 'primary' | 'success' | 'warning' | 'error' | 'info';
}> = ({ title, value, icon, trend, trendValue, color = 'primary' }) => {
  const colorClasses = {
    primary: 'text-primary bg-primary/10',
    success: 'text-success bg-success/10',
    warning: 'text-warning bg-warning/10',
    error: 'text-error bg-error/10',
    info: 'text-info bg-info/10'
  };

  const trendIcons = {
    up: <TrendingUp className="h-4 w-4 text-success" />,
    down: <TrendingDown className="h-4 w-4 text-error" />,
    stable: <Minus className="h-4 w-4 text-muted" />
  };

  return (
    <div className="bg-bg-card border border-border rounded-lg p-4">
      <div className="flex items-center justify-between mb-2">
        <div className={`p-2 rounded-lg ${colorClasses[color]}`}>
          {icon}
        </div>
        {trend && trendValue && (
          <div className="flex items-center gap-1">
            {trendIcons[trend]}
            <span className="text-sm text-muted">{trendValue}</span>
          </div>
        )}
      </div>
      <div className="space-y-1">
        <p className="text-2xl font-bold text-text-primary">{value}</p>
        <p className="text-sm text-text-secondary">{title}</p>
      </div>
    </div>
  );
};

// Componente de lista de ambulancias
const AmbulanceList: React.FC = () => {
  const { ambulances, selectAmbulance, selectedAmbulanceId } = useAmbulanceStore();

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'available': return 'text-success bg-success/10';
      case 'en_route': return 'text-warning bg-warning/10';
      case 'on_scene': return 'text-error bg-error/10';
      case 'returning': return 'text-info bg-info/10';
      case 'offline': return 'text-muted bg-muted/10';
      case 'maintenance': return 'text-purple-600 bg-purple-100';
      default: return 'text-muted bg-muted/10';
    }
  };

  return (
    <div className="space-y-2">
      <h3 className="font-semibold text-lg mb-3">Ambulancias</h3>
      <div className="space-y-2 max-h-96 overflow-y-auto">
        {ambulances.map((ambulance) => (
          <div
            key={ambulance.id}
            className={`p-3 border rounded-lg cursor-pointer transition-colors ${
              selectedAmbulanceId === ambulance.id
                ? 'border-primary bg-primary/5'
                : 'border-border hover:border-primary/50'
            }`}
            onClick={() => selectAmbulance(ambulance.id)}
          >
            <div className="flex items-center justify-between mb-2">
              <div className="flex items-center gap-2">
                <Truck className="h-4 w-4 text-primary" />
                <span className="font-medium">{ambulance.code}</span>
              </div>
              <span className={`px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(ambulance.status)}`}>
                {ambulance.status.replace('_', ' ').toUpperCase()}
              </span>
            </div>
            
            <div className="space-y-1 text-sm text-text-secondary">
              <div className="flex items-center gap-2">
                <Users className="h-3 w-3" />
                <span>{ambulance.crew.driver}</span>
                {ambulance.crew.paramedic && <span>• {ambulance.crew.paramedic}</span>}
              </div>
              
              <div className="flex items-center gap-2">
                <MapPin className="h-3 w-3" />
                <span className="truncate">{ambulance.location.address || 'Ubicación no disponible'}</span>
              </div>
              
              <div className="flex items-center gap-2">
                <Clock className="h-3 w-3" />
                <span>{new Date(ambulance.location.lastUpdate).toLocaleTimeString()}</span>
              </div>
            </div>

            {ambulance.currentService && (
              <div className="mt-2 p-2 bg-warning/10 rounded text-xs">
                <div className="flex items-center gap-1">
                  <Activity className="h-3 w-3 text-warning" />
                  <span className="font-medium text-warning">Servicio Activo</span>
                </div>
                <p className="text-text-secondary mt-1">ID: {ambulance.currentService.emergencyId}</p>
              </div>
            )}
          </div>
        ))}
      </div>
    </div>
  );
};

// Componente de servicios de emergencia
const EmergencyServices: React.FC = () => {
  const { emergencyServices, selectService, selectedServiceId } = useAmbulanceStore();
  
  const activeServices = emergencyServices.filter(service => 
    ['pending', 'assigned', 'en_route', 'on_scene'].includes(service.status)
  );

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'critical': return 'text-error bg-error/10 border-error/20';
      case 'high': return 'text-warning bg-warning/10 border-warning/20';
      case 'medium': return 'text-info bg-info/10 border-info/20';
      case 'low': return 'text-success bg-success/10 border-success/20';
      default: return 'text-muted bg-muted/10 border-muted/20';
    }
  };

  return (
    <div className="space-y-2">
      <h3 className="font-semibold text-lg mb-3">Servicios Activos</h3>
      <div className="space-y-2 max-h-96 overflow-y-auto">
        {activeServices.map((service) => (
          <div
            key={service.id}
            className={`p-3 border rounded-lg cursor-pointer transition-colors ${
              selectedServiceId === service.id
                ? 'border-primary bg-primary/5'
                : 'border-border hover:border-primary/50'
            }`}
            onClick={() => selectService(service.id)}
          >
            <div className="flex items-center justify-between mb-2">
              <div className="flex items-center gap-2">
                <AlertTriangle className="h-4 w-4 text-error" />
                <span className="font-medium">#{service.id.slice(-6)}</span>
              </div>
              <span className={`px-2 py-1 rounded-full text-xs font-medium border ${getPriorityColor(service.priority)}`}>
                {service.priority.toUpperCase()}
              </span>
            </div>
            
            <div className="space-y-1 text-sm">
              <p className="font-medium text-text-primary">{service.description}</p>
              
              <div className="flex items-center gap-2 text-text-secondary">
                <MapPin className="h-3 w-3" />
                <span className="truncate">{service.location.address}</span>
              </div>
              
              <div className="flex items-center gap-2 text-text-secondary">
                <Clock className="h-3 w-3" />
                <span>{new Date(service.createdAt).toLocaleTimeString()}</span>
              </div>

              {service.assignedAmbulanceId && (
                <div className="flex items-center gap-2 text-success">
                  <Truck className="h-3 w-3" />
                  <span>Ambulancia: {service.assignedAmbulanceId}</span>
                </div>
              )}
            </div>

            <div className="mt-2 flex gap-2">
              <span className={`px-2 py-1 rounded text-xs font-medium ${
                service.status === 'pending' ? 'bg-warning/20 text-warning' :
                service.status === 'assigned' ? 'bg-info/20 text-info' :
                service.status === 'en_route' ? 'bg-primary/20 text-primary' :
                'bg-error/20 text-error'
              }`}>
                {service.status.replace('_', ' ').toUpperCase()}
              </span>
            </div>
          </div>
        ))}
        
        {activeServices.length === 0 && (
          <div className="text-center py-8 text-text-secondary">
            <CheckCircle className="h-8 w-8 mx-auto mb-2 opacity-50" />
            <p>No hay servicios activos</p>
          </div>
        )}
      </div>
    </div>
  );
};

// Componente principal del dashboard
export const AmbulanceDashboard: React.FC = () => {
  const metrics = useAmbulanceMetrics();
  const { isLoading, error, setLoading } = useAmbulanceStore();
  const [lastUpdate, setLastUpdate] = useState(new Date());

  // Simular carga de datos
  useEffect(() => {
    const loadData = async () => {
      setLoading(true);
      try {
        // Simular carga de datos
        await new Promise(resolve => setTimeout(resolve, 1000));
        setLastUpdate(new Date());
      } catch (error) {
        console.error('Error cargando datos:', error);
      } finally {
        setLoading(false);
      }
    };

    loadData();
    
    // Actualizar cada 30 segundos
    const interval = setInterval(loadData, 30000);
    return () => clearInterval(interval);
  }, [setLoading]);

  const handleRefresh = () => {
    setLastUpdate(new Date());
    // Aquí se podría disparar una actualización manual
  };

  if (error) {
    return (
      <div className="p-6 text-center">
        <AlertTriangle className="h-12 w-12 mx-auto mb-4 text-error" />
        <h2 className="text-xl font-semibold mb-2">Error al cargar datos</h2>
        <p className="text-text-secondary mb-4">{error}</p>
        <button
          onClick={handleRefresh}
          className="bg-primary text-white px-4 py-2 rounded-lg hover:bg-primary-dark transition-colors"
        >
          Reintentar
        </button>
      </div>
    );
  }

  return (
    <div className="h-full flex flex-col">
      {/* Header */}
      <div className="flex items-center justify-between p-6 border-b border-border">
        <div>
          <h1 className="text-2xl font-bold text-text-primary">Dashboard de Ambulancias</h1>
          <p className="text-text-secondary">
            Última actualización: {lastUpdate.toLocaleTimeString()}
          </p>
        </div>
        <button
          onClick={handleRefresh}
          disabled={isLoading}
          className="flex items-center gap-2 px-4 py-2 bg-primary text-white rounded-lg hover:bg-primary-dark transition-colors disabled:opacity-50"
        >
          <RefreshCw className={`h-4 w-4 ${isLoading ? 'animate-spin' : ''}`} />
          Actualizar
        </button>
      </div>

      {/* Métricas */}
      <div className="p-6 border-b border-border">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          <MetricCard
            title="Total Ambulancias"
            value={metrics.totalAmbulances}
            icon={<Truck className="h-5 w-5" />}
            color="primary"
          />
          <MetricCard
            title="Disponibles"
            value={metrics.availableAmbulances}
            icon={<CheckCircle className="h-5 w-5" />}
            color="success"
            trend="stable"
            trendValue="0%"
          />
          <MetricCard
            title="Servicios Activos"
            value={metrics.activeServices}
            icon={<Activity className="h-5 w-5" />}
            color="warning"
            trend="up"
            trendValue="+2"
          />
          <MetricCard
            title="Tiempo Promedio"
            value={`${metrics.averageResponseTime} min`}
            icon={<Clock className="h-5 w-5" />}
            color="info"
            trend="down"
            trendValue="-1.2 min"
          />
        </div>
      </div>

      {/* Contenido principal */}
      <div className="flex-1 flex">
        {/* Mapa */}
        <div className="flex-1 relative">
          <AmbulanceMap />
        </div>

        {/* Panel lateral */}
        <div className="w-80 border-l border-border bg-bg-secondary overflow-hidden flex flex-col">
          <div className="flex-1 overflow-y-auto">
            <div className="p-4">
              <AmbulanceList />
            </div>
            <div className="border-t border-border p-4">
              <EmergencyServices />
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default AmbulanceDashboard;
