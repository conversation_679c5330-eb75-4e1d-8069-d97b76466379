import React, { useState } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { useNavigate } from 'react-router-dom';
import { Button } from '../../components/ui/Button';
import { Input } from '../../components/ui/Input';
import { toast } from 'react-hot-toast';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { 
  faVideo, 
  faArrowLeft, 
  faSave, 
  faCalendarAlt, 
  faUserMd,
  faUser,
  faLaptop,
  faShieldAlt,
  faInfoCircle,
  faClock,
  faMapMarkerAlt
} from '@fortawesome/free-solid-svg-icons';

// Schema de validación basado en la estructura de la BD
const teleconsultaSchema = z.object({
  // Información básica
  paciente_id: z.string().min(1, 'Debe seleccionar un paciente'),
  profesional_id: z.string().min(1, 'Debe seleccionar un profesional'),
  
  // Programación
  fecha_programada: z.string().min(1, 'Fecha programada requerida'),
  hora_programada: z.string().min(1, 'Hora programada requerida'),
  duracion_estimada: z.number().min(15, 'Duración mínima: 15 minutos').max(120, 'Duración máxima: 2 horas'),
  
  // Tipo y modalidad
  tipo_consulta: z.enum(['primera_vez', 'control', 'seguimiento', 'urgencia'], {
    required_error: 'Debe seleccionar el tipo de consulta'
  }),
  modalidad: z.enum(['video', 'audio', 'chat'], {
    required_error: 'Debe seleccionar la modalidad'
  }),
  plataforma: z.enum(['zoom', 'teams', 'meet', 'webex', 'jitsi'], {
    required_error: 'Debe seleccionar la plataforma'
  }),
  
  // Motivo y observaciones
  motivo_consulta: z.string().min(10, 'El motivo debe tener al menos 10 caracteres'),
  observaciones_previas: z.string().optional(),
  
  // Información técnica del paciente
  dispositivo_paciente: z.enum(['móvil', 'tablet', 'computador'], {
    required_error: 'Debe especificar el dispositivo del paciente'
  }),
  navegador_paciente: z.string().optional(),
  
  // Consentimiento y autorización
  consentimiento_telemedicina: z.boolean().refine(val => val === true, {
    message: 'Debe aceptar el consentimiento de telemedicina'
  }),
  grabacion_autorizada: z.boolean().default(false),
  
  // Ubicación del paciente (opcional)
  ubicacion_paciente_lat: z.number().optional(),
  ubicacion_paciente_lng: z.number().optional(),
  direccion_paciente: z.string().optional(),
});

type TeleconsultaFormData = z.infer<typeof teleconsultaSchema>;

// Datos mock
const pacientes = [
  { id: '1', nombre: 'Juan Pérez', documento: '12345678', telefono: '3001234567' },
  { id: '2', nombre: 'María González', documento: '87654321', telefono: '3009876543' },
  { id: '3', nombre: 'Carlos López', documento: '11223344', telefono: '3005556677' }
];

const profesionales = [
  { id: '1', nombre: 'Dr. García', especialidad: 'Medicina General', disponible: true },
  { id: '2', nombre: 'Dra. Rodríguez', especialidad: 'Cardiología', disponible: true },
  { id: '3', nombre: 'Dr. Martínez', especialidad: 'Pediatría', disponible: false }
];

export const NuevaTeleconsulta: React.FC = () => {
  const navigate = useNavigate();
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [ubicacionPaciente, setUbicacionPaciente] = useState<{lat: number, lng: number} | null>(null);

  const {
    register,
    handleSubmit,
    formState: { errors },
    setValue,
    watch
  } = useForm<TeleconsultaFormData>({
    resolver: zodResolver(teleconsultaSchema),
    defaultValues: {
      duracion_estimada: 30,
      modalidad: 'video',
      plataforma: 'zoom',
      dispositivo_paciente: 'computador',
      grabacion_autorizada: false,
      consentimiento_telemedicina: false
    }
  });

  const modalidad = watch('modalidad');
  const plataforma = watch('plataforma');
  const tipoConsulta = watch('tipo_consulta');

  const onSubmit = async (data: TeleconsultaFormData) => {
    setIsSubmitting(true);
    try {
      // Combinar fecha y hora
      const fechaHoraProgramada = new Date(`${data.fecha_programada}T${data.hora_programada}`);
      
      console.log('Datos de teleconsulta:', {
        ...data,
        fecha_hora_programada: fechaHoraProgramada,
        ubicacion_paciente: ubicacionPaciente
      });
      
      // Simular llamada a API
      await new Promise(resolve => setTimeout(resolve, 1500));
      
      toast.success('Teleconsulta programada exitosamente');
      navigate('/telemedicina');
    } catch (error) {
      console.error('Error al programar teleconsulta:', error);
      toast.error('Error al programar la teleconsulta');
    } finally {
      setIsSubmitting(false);
    }
  };

  // Función para obtener ubicación del paciente
  const obtenerUbicacionPaciente = () => {
    if (navigator.geolocation) {
      navigator.geolocation.getCurrentPosition(
        (position) => {
          const lat = position.coords.latitude;
          const lng = position.coords.longitude;
          setValue('ubicacion_paciente_lat', lat);
          setValue('ubicacion_paciente_lng', lng);
          setUbicacionPaciente({ lat, lng });
          toast.success('Ubicación del paciente obtenida');
        },
        (error) => {
          console.error('Error al obtener ubicación:', error);
          toast.error('No se pudo obtener la ubicación del paciente');
        }
      );
    } else {
      toast.error('Geolocalización no soportada');
    }
  };

  return (
    <div className="bg-card p-6 rounded-lg shadow-lg max-w-6xl mx-auto">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold text-primary flex items-center">
          <FontAwesomeIcon icon={faVideo} className="mr-2" />
          Nueva Teleconsulta
        </h1>
        <Button variant="secondary" onClick={() => navigate('/telemedicina')}>
          <FontAwesomeIcon icon={faArrowLeft} className="mr-2" />
          Volver
        </Button>
      </div>

      <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
        {/* Información Básica */}
        <div className="bg-secondary/30 p-4 rounded-lg">
          <h3 className="text-lg font-semibold text-primary mb-4 flex items-center">
            <FontAwesomeIcon icon={faUser} className="mr-2" />
            👥 Información Básica
          </h3>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-secondary mb-1">
                Paciente *
              </label>
              <select
                {...register('paciente_id')}
                className="w-full p-3 rounded-lg bg-secondary border border-color text-primary focus:ring-2 focus:ring-primary focus:border-primary transition-colors"
              >
                <option value="">Seleccione un paciente</option>
                {pacientes.map((paciente) => (
                  <option key={paciente.id} value={paciente.id}>
                    {paciente.nombre} - {paciente.documento}
                  </option>
                ))}
              </select>
              {errors.paciente_id && (
                <p className="mt-1 text-sm text-error">{errors.paciente_id.message}</p>
              )}
            </div>

            <div>
              <label className="block text-sm font-medium text-secondary mb-1">
                Profesional *
              </label>
              <select
                {...register('profesional_id')}
                className="w-full p-3 rounded-lg bg-secondary border border-color text-primary focus:ring-2 focus:ring-primary focus:border-primary transition-colors"
              >
                <option value="">Seleccione un profesional</option>
                {profesionales.filter(p => p.disponible).map((profesional) => (
                  <option key={profesional.id} value={profesional.id}>
                    {profesional.nombre} - {profesional.especialidad}
                  </option>
                ))}
              </select>
              {errors.profesional_id && (
                <p className="mt-1 text-sm text-error">{errors.profesional_id.message}</p>
              )}
            </div>
          </div>
        </div>

        {/* Programación */}
        <div className="bg-secondary/30 p-4 rounded-lg">
          <h3 className="text-lg font-semibold text-primary mb-4 flex items-center">
            <FontAwesomeIcon icon={faCalendarAlt} className="mr-2" />
            📅 Programación
          </h3>
          
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div>
              <label className="block text-sm font-medium text-secondary mb-1">
                Fecha *
              </label>
              <Input
                {...register('fecha_programada')}
                type="date"
                min={new Date().toISOString().split('T')[0]}
                error={errors.fecha_programada?.message}
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-secondary mb-1">
                Hora *
              </label>
              <Input
                {...register('hora_programada')}
                type="time"
                error={errors.hora_programada?.message}
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-secondary mb-1">
                Duración (minutos) *
              </label>
              <Input
                {...register('duracion_estimada', { valueAsNumber: true })}
                type="number"
                min="15"
                max="120"
                step="15"
                error={errors.duracion_estimada?.message}
              />
            </div>
          </div>
        </div>

        {/* Tipo y Modalidad */}
        <div className="bg-secondary/30 p-4 rounded-lg">
          <h3 className="text-lg font-semibold text-primary mb-4 flex items-center">
            <FontAwesomeIcon icon={faVideo} className="mr-2" />
            🎥 Tipo y Modalidad
          </h3>
          
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div>
              <label className="block text-sm font-medium text-secondary mb-1">
                Tipo de Consulta *
              </label>
              <select
                {...register('tipo_consulta')}
                className="w-full p-3 rounded-lg bg-secondary border border-color text-primary focus:ring-2 focus:ring-primary focus:border-primary transition-colors"
              >
                <option value="">Seleccione tipo</option>
                <option value="primera_vez">🆕 Primera Vez</option>
                <option value="control">🔄 Control</option>
                <option value="seguimiento">📋 Seguimiento</option>
                <option value="urgencia">🚨 Urgencia</option>
              </select>
              {errors.tipo_consulta && (
                <p className="mt-1 text-sm text-error">{errors.tipo_consulta.message}</p>
              )}
            </div>

            <div>
              <label className="block text-sm font-medium text-secondary mb-1">
                Modalidad *
              </label>
              <select
                {...register('modalidad')}
                className="w-full p-3 rounded-lg bg-secondary border border-color text-primary focus:ring-2 focus:ring-primary focus:border-primary transition-colors"
              >
                <option value="video">📹 Video</option>
                <option value="audio">🎤 Solo Audio</option>
                <option value="chat">💬 Chat</option>
              </select>
              {errors.modalidad && (
                <p className="mt-1 text-sm text-error">{errors.modalidad.message}</p>
              )}
            </div>

            <div>
              <label className="block text-sm font-medium text-secondary mb-1">
                Plataforma *
              </label>
              <select
                {...register('plataforma')}
                className="w-full p-3 rounded-lg bg-secondary border border-color text-primary focus:ring-2 focus:ring-primary focus:border-primary transition-colors"
              >
                <option value="zoom">Zoom</option>
                <option value="teams">Microsoft Teams</option>
                <option value="meet">Google Meet</option>
                <option value="webex">Cisco Webex</option>
                <option value="jitsi">Jitsi Meet</option>
              </select>
              {errors.plataforma && (
                <p className="mt-1 text-sm text-error">{errors.plataforma.message}</p>
              )}
            </div>
          </div>

          {/* Información sobre la modalidad seleccionada */}
          {modalidad && (
            <div className="mt-4 p-3 bg-info/20 border border-info/30 rounded-lg">
              <h5 className="font-medium text-info mb-2 flex items-center">
                <FontAwesomeIcon icon={faInfoCircle} className="mr-2" />
                Información sobre {modalidad}
              </h5>
              <div className="text-xs text-muted">
                {modalidad === 'video' && (
                  <ul className="space-y-1">
                    <li>• Requiere cámara y micrófono funcionales</li>
                    <li>• Conexión de internet estable (mín. 1 Mbps)</li>
                    <li>• Permite examen visual del paciente</li>
                  </ul>
                )}
                {modalidad === 'audio' && (
                  <ul className="space-y-1">
                    <li>• Solo requiere micrófono y altavoces</li>
                    <li>• Menor consumo de datos</li>
                    <li>• Ideal para consultas de seguimiento</li>
                  </ul>
                )}
                {modalidad === 'chat' && (
                  <ul className="space-y-1">
                    <li>• Comunicación por texto únicamente</li>
                    <li>• Mínimo consumo de datos</li>
                    <li>• Permite envío de imágenes y documentos</li>
                  </ul>
                )}
              </div>
            </div>
          )}
        </div>

        {/* Motivo y Observaciones */}
        <div className="bg-secondary/30 p-4 rounded-lg">
          <h3 className="text-lg font-semibold text-primary mb-4 flex items-center">
            <FontAwesomeIcon icon={faUserMd} className="mr-2" />
            📝 Motivo y Observaciones
          </h3>

          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-secondary mb-1">
                Motivo de la Consulta *
              </label>
              <textarea
                {...register('motivo_consulta')}
                rows={3}
                className="w-full p-3 rounded-lg bg-secondary border border-color text-primary focus:ring-2 focus:ring-primary focus:border-primary transition-colors resize-none"
                placeholder="Describa el motivo principal de la teleconsulta..."
              />
              {errors.motivo_consulta && (
                <p className="mt-1 text-sm text-error">{errors.motivo_consulta.message}</p>
              )}
            </div>

            <div>
              <label className="block text-sm font-medium text-secondary mb-1">
                Observaciones Previas
              </label>
              <textarea
                {...register('observaciones_previas')}
                rows={3}
                className="w-full p-3 rounded-lg bg-secondary border border-color text-primary focus:ring-2 focus:ring-primary focus:border-primary transition-colors resize-none"
                placeholder="Información adicional relevante para la consulta..."
              />
            </div>
          </div>
        </div>

        {/* Información Técnica del Paciente */}
        <div className="bg-secondary/30 p-4 rounded-lg">
          <h3 className="text-lg font-semibold text-primary mb-4 flex items-center">
            <FontAwesomeIcon icon={faLaptop} className="mr-2" />
            💻 Información Técnica del Paciente
          </h3>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-secondary mb-1">
                Dispositivo del Paciente *
              </label>
              <select
                {...register('dispositivo_paciente')}
                className="w-full p-3 rounded-lg bg-secondary border border-color text-primary focus:ring-2 focus:ring-primary focus:border-primary transition-colors"
              >
                <option value="computador">💻 Computador</option>
                <option value="tablet">📱 Tablet</option>
                <option value="móvil">📱 Móvil</option>
              </select>
              {errors.dispositivo_paciente && (
                <p className="mt-1 text-sm text-error">{errors.dispositivo_paciente.message}</p>
              )}
            </div>

            <div>
              <label className="block text-sm font-medium text-secondary mb-1">
                Navegador del Paciente
              </label>
              <Input
                {...register('navegador_paciente')}
                placeholder="Chrome, Firefox, Safari, etc."
                error={errors.navegador_paciente?.message}
              />
            </div>
          </div>
        </div>

        {/* Consentimiento y Autorización */}
        <div className="bg-secondary/30 p-4 rounded-lg">
          <h3 className="text-lg font-semibold text-primary mb-4 flex items-center">
            <FontAwesomeIcon icon={faShieldAlt} className="mr-2" />
            🛡️ Consentimiento y Autorización
          </h3>

          <div className="space-y-4">
            <div className="flex items-start space-x-3">
              <input
                {...register('consentimiento_telemedicina')}
                type="checkbox"
                className="mt-1 w-4 h-4 text-primary border-color rounded focus:ring-primary"
              />
              <div>
                <label className="text-sm font-medium text-secondary">
                  Consentimiento de Telemedicina *
                </label>
                <p className="text-xs text-muted mt-1">
                  El paciente ha sido informado sobre los beneficios, riesgos y limitaciones de la telemedicina
                  y ha otorgado su consentimiento para la realización de la teleconsulta según la Resolución 2654/2019.
                </p>
              </div>
            </div>
            {errors.consentimiento_telemedicina && (
              <p className="text-sm text-error">{errors.consentimiento_telemedicina.message}</p>
            )}

            <div className="flex items-start space-x-3">
              <input
                {...register('grabacion_autorizada')}
                type="checkbox"
                className="mt-1 w-4 h-4 text-primary border-color rounded focus:ring-primary"
              />
              <div>
                <label className="text-sm font-medium text-secondary">
                  Autorización de Grabación
                </label>
                <p className="text-xs text-muted mt-1">
                  El paciente autoriza la grabación de la teleconsulta para fines médicos,
                  académicos o de control de calidad.
                </p>
              </div>
            </div>
          </div>

          <div className="mt-4 p-3 bg-warning/20 border border-warning/30 rounded-lg">
            <h5 className="font-medium text-warning mb-2 flex items-center">
              <FontAwesomeIcon icon={faInfoCircle} className="mr-2" />
              Información Legal
            </h5>
            <ul className="text-xs text-muted space-y-1">
              <li>• La teleconsulta cumple con la Resolución 2654/2019 del MinSalud</li>
              <li>• Se garantiza la confidencialidad según la Ley 1581/2012</li>
              <li>• El profesional debe estar registrado y habilitado</li>
              <li>• Se requiere consentimiento informado del paciente</li>
            </ul>
          </div>
        </div>

        {/* Botones de Acción */}
        <div className="flex justify-end gap-4 pt-4">
          <Button
            type="button"
            variant="secondary"
            onClick={() => navigate('/telemedicina')}
          >
            Cancelar
          </Button>

          <Button
            type="submit"
            disabled={isSubmitting}
            className="min-w-[150px]"
          >
            {isSubmitting ? (
              <>
                <span className="mr-2 inline-block w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"></span>
                Programando...
              </>
            ) : (
              <>
                <FontAwesomeIcon icon={faSave} className="mr-2" />
                Programar Teleconsulta
              </>
            )}
          </Button>
        </div>
      </form>
    </div>
  );
};
