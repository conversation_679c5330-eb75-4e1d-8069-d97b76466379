import React, { useState } from 'react';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import {
  faPlane,
  faPlus,
  faSearch,
  faEdit,
  faTrash,
  faEye,
  faCheckCircle,
  faTimesCircle,
  faClock,
  faCalendarAlt,
  faUser,
  faCalendarCheck,
  faCalendarTimes,
  faTimes
} from '@fortawesome/free-solid-svg-icons';
import { Button } from '../../../components/ui/Button';
import { Input } from '../../../components/ui/Input';
import { toast } from 'react-hot-toast';

// Datos mock de vacaciones
const mockVacaciones = [
  {
    id: 1,
    empleado_nombre: '<PERSON>',
    fecha_solicitud: '2024-01-10',
    fecha_inicio: '2024-02-15',
    fecha_fin: '2024-02-29',
    dias_solicitados: 15,
    dias_disponibles: 30,
    motivo: 'Vacaciones familiares',
    estado: 'Aprobada',
    aprobado_por: '<PERSON>. <PERSON>',
    fecha_aprobacion: '2024-01-12',
    observaciones: 'Aprobado para período solicitado'
  },
  {
    id: 2,
    empleado_nombre: '<PERSON> <PERSON>ejandra <PERSON>',
    fecha_solicitud: '2024-01-20',
    fecha_inicio: '2024-03-01',
    fecha_fin: '2024-03-10',
    dias_solicitados: 10,
    dias_disponibles: 25,
    motivo: 'Descanso médico',
    estado: 'Pendiente',
    observaciones: 'Pendiente de aprobación por jefe inmediato'
  },
  {
    id: 3,
    empleado_nombre: 'Ana María González',
    fecha_solicitud: '2024-01-25',
    fecha_inicio: '2024-04-15',
    fecha_fin: '2024-04-30',
    dias_solicitados: 16,
    dias_disponibles: 20,
    motivo: 'Viaje personal',
    estado: 'Rechazada',
    aprobado_por: 'Dr. Carlos Rodríguez',
    fecha_aprobacion: '2024-01-27',
    observaciones: 'Rechazado por alta demanda en el período'
  },
  {
    id: 4,
    empleado_nombre: 'Luis Fernando Martínez',
    fecha_solicitud: '2024-02-01',
    fecha_inicio: '2024-05-01',
    fecha_fin: '2024-05-15',
    dias_solicitados: 15,
    dias_disponibles: 30,
    motivo: 'Vacaciones anuales',
    estado: 'Programada',
    aprobado_por: 'Dr. María López',
    fecha_aprobacion: '2024-02-03',
    observaciones: 'Aprobado y programado en calendario'
  }
];

const GestionVacaciones: React.FC = () => {
  const [vacaciones, setVacaciones] = useState(mockVacaciones);
  const [searchTerm, setSearchTerm] = useState('');
  const [filtroEstado, setFiltroEstado] = useState<string>('todos');
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [editingVacacion, setEditingVacacion] = useState<any>(null);

  const filteredVacaciones = vacaciones.filter(vacacion => {
    const matchesSearch = vacacion.empleado_nombre.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         vacacion.motivo.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesEstado = filtroEstado === 'todos' || vacacion.estado === filtroEstado;

    return matchesSearch && matchesEstado;
  });

  const getEstadoColor = (estado: string) => {
    switch (estado) {
      case 'Aprobada':
        return 'bg-success/20 text-success';
      case 'Programada':
        return 'bg-info/20 text-info';
      case 'Pendiente':
        return 'bg-warning/20 text-warning';
      case 'Rechazada':
        return 'bg-error/20 text-error';
      case 'En Curso':
        return 'bg-blue/20 text-blue-600';
      case 'Finalizada':
        return 'bg-gray/20 text-gray-600';
      default:
        return 'bg-secondary/20 text-secondary';
    }
  };

  const getEstadoIcon = (estado: string) => {
    switch (estado) {
      case 'Aprobada':
        return faCheckCircle;
      case 'Programada':
        return faCalendarCheck;
      case 'Pendiente':
        return faClock;
      case 'Rechazada':
        return faTimesCircle;
      case 'En Curso':
        return faPlane;
      case 'Finalizada':
        return faCalendarTimes;
      default:
        return faCalendarAlt;
    }
  };

  const handleApprove = (id: number) => {
    setVacaciones(prev =>
      prev.map(vac =>
        vac.id === id
          ? {
              ...vac,
              estado: 'Aprobada',
              aprobado_por: 'Usuario Actual',
              fecha_aprobacion: new Date().toISOString().split('T')[0]
            }
          : vac
      )
    );
    toast.success('Vacación aprobada exitosamente');
  };

  const handleReject = (id: number) => {
    setVacaciones(prev =>
      prev.map(vac =>
        vac.id === id
          ? {
              ...vac,
              estado: 'Rechazada',
              aprobado_por: 'Usuario Actual',
              fecha_aprobacion: new Date().toISOString().split('T')[0]
            }
          : vac
      )
    );
    toast.success('Vacación rechazada');
  };

  const handleEdit = (vacacion: any) => {
    setEditingVacacion(vacacion);
    setIsModalOpen(true);
    toast.info(`Editando solicitud de ${vacacion.empleado_nombre}`);
  };

  const handleDelete = (id: number) => {
    const vacacion = vacaciones.find(v => v.id === id);
    if (window.confirm(`¿Está seguro de eliminar la solicitud de vacaciones de ${vacacion?.empleado_nombre}?`)) {
      setVacaciones(prev => prev.filter(v => v.id !== id));
      toast.success('Solicitud eliminada exitosamente');
    }
  };

  const totalSolicitudes = vacaciones.length;
  const solicitudesPendientes = vacaciones.filter(v => v.estado === 'Pendiente').length;
  const solicitudesAprobadas = vacaciones.filter(v => v.estado === 'Aprobada' || v.estado === 'Programada').length;
  const totalDiasSolicitados = vacaciones.reduce((sum, v) => sum + v.dias_solicitados, 0);

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="bg-card p-6 rounded-lg shadow-lg">
        <div className="flex justify-between items-center">
          <div>
            <h2 className="text-2xl font-bold text-primary flex items-center">
              <FontAwesomeIcon icon={faPlane} className="mr-2" />
              Gestión de Vacaciones
            </h2>
            <p className="text-muted mt-1">
              Solicitudes y programación de vacaciones
            </p>
          </div>
          <Button onClick={() => setIsModalOpen(true)}>
            <FontAwesomeIcon icon={faPlus} className="mr-2" />
            Nueva Solicitud
          </Button>
        </div>
      </div>

      {/* Estadísticas */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <div className="bg-card p-6 rounded-lg shadow">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-muted">Total Solicitudes</p>
              <p className="text-2xl font-bold text-primary">{totalSolicitudes}</p>
            </div>
            <FontAwesomeIcon icon={faCalendarAlt} className="text-primary text-2xl" />
          </div>
        </div>

        <div className="bg-card p-6 rounded-lg shadow">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-muted">Pendientes</p>
              <p className="text-2xl font-bold text-warning">{solicitudesPendientes}</p>
            </div>
            <FontAwesomeIcon icon={faClock} className="text-warning text-2xl" />
          </div>
        </div>

        <div className="bg-card p-6 rounded-lg shadow">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-muted">Aprobadas</p>
              <p className="text-2xl font-bold text-success">{solicitudesAprobadas}</p>
            </div>
            <FontAwesomeIcon icon={faCheckCircle} className="text-success text-2xl" />
          </div>
        </div>

        <div className="bg-card p-6 rounded-lg shadow">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-muted">Total Días</p>
              <p className="text-2xl font-bold text-info">{totalDiasSolicitados}</p>
            </div>
            <FontAwesomeIcon icon={faPlane} className="text-info text-2xl" />
          </div>
        </div>
      </div>

      {/* Filtros */}
      <div className="bg-card p-6 rounded-lg shadow-lg">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <label className="block text-sm font-medium text-secondary mb-1">
              Buscar
            </label>
            <div className="relative">
              <FontAwesomeIcon icon={faSearch} className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted" />
              <Input
                type="text"
                placeholder="Empleado, motivo..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10"
              />
            </div>
          </div>

          <div>
            <label className="block text-sm font-medium text-secondary mb-1">
              Estado
            </label>
            <select
              value={filtroEstado}
              onChange={(e) => setFiltroEstado(e.target.value)}
              className="w-full p-3 rounded-lg bg-secondary border border-color text-primary focus:ring-2 focus:ring-primary"
            >
              <option value="todos">Todos los estados</option>
              <option value="Pendiente">Pendiente</option>
              <option value="Aprobada">Aprobada</option>
              <option value="Programada">Programada</option>
              <option value="Rechazada">Rechazada</option>
              <option value="En Curso">En Curso</option>
              <option value="Finalizada">Finalizada</option>
            </select>
          </div>
        </div>
      </div>

      {/* Tabla de Vacaciones */}
      <div className="bg-card rounded-lg shadow-lg overflow-hidden">
        <div className="overflow-x-auto">
          <table className="w-full">
            <thead className="bg-secondary/50">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-secondary uppercase tracking-wider">
                  Empleado
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-secondary uppercase tracking-wider">
                  Período
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-secondary uppercase tracking-wider">
                  Días
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-secondary uppercase tracking-wider">
                  Motivo
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-secondary uppercase tracking-wider">
                  Estado
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-secondary uppercase tracking-wider">
                  Aprobación
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-secondary uppercase tracking-wider">
                  Acciones
                </th>
              </tr>
            </thead>
            <tbody className="bg-card divide-y divide-color">
              {filteredVacaciones.map((vacacion) => (
                <tr key={vacacion.id} className="hover:bg-secondary/20 transition-colors">
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="flex items-center">
                      <FontAwesomeIcon icon={faUser} className="mr-2 text-muted" />
                      <div>
                        <div className="text-sm font-medium text-primary">
                          {vacacion.empleado_nombre}
                        </div>
                        <div className="text-sm text-muted">
                          Solicitud: {new Date(vacacion.fecha_solicitud).toLocaleDateString()}
                        </div>
                      </div>
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="text-sm text-primary">
                      <div className="flex items-center">
                        <FontAwesomeIcon icon={faCalendarAlt} className="mr-2 text-muted" />
                        {new Date(vacacion.fecha_inicio).toLocaleDateString()}
                      </div>
                      <div className="text-sm text-muted">
                        hasta {new Date(vacacion.fecha_fin).toLocaleDateString()}
                      </div>
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="text-sm font-medium text-primary">
                      {vacacion.dias_solicitados} días
                    </div>
                    <div className="text-sm text-muted">
                      Disponibles: {vacacion.dias_disponibles}
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="text-sm text-primary">
                      {vacacion.motivo}
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getEstadoColor(vacacion.estado)}`}>
                      <FontAwesomeIcon icon={getEstadoIcon(vacacion.estado)} className="mr-1" />
                      {vacacion.estado}
                    </span>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    {vacacion.aprobado_por ? (
                      <div className="text-sm text-primary">
                        <div>{vacacion.aprobado_por}</div>
                        <div className="text-xs text-muted">
                          {vacacion.fecha_aprobacion && new Date(vacacion.fecha_aprobacion).toLocaleDateString()}
                        </div>
                      </div>
                    ) : (
                      <span className="text-sm text-muted">Pendiente</span>
                    )}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                    <div className="flex space-x-2">
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => toast.info(`Viendo detalles de vacación de ${vacacion.empleado_nombre}`)}
                        className="text-blue-600 border-blue-600 hover:bg-blue-50"
                      >
                        <FontAwesomeIcon icon={faEye} />
                      </Button>
                      {vacacion.estado === 'Pendiente' && (
                        <>
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => handleApprove(vacacion.id)}
                            className="text-green-600 border-green-600 hover:bg-green-50"
                          >
                            <FontAwesomeIcon icon={faCheckCircle} />
                          </Button>
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => handleReject(vacacion.id)}
                            className="text-red-600 border-red-600 hover:bg-red-50"
                          >
                            <FontAwesomeIcon icon={faTimesCircle} />
                          </Button>
                        </>
                      )}
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => handleEdit(vacacion)}
                        className="text-purple-600 border-purple-600 hover:bg-purple-50"
                      >
                        <FontAwesomeIcon icon={faEdit} />
                      </Button>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => handleDelete(vacacion.id)}
                        className="text-red-600 border-red-600 hover:bg-red-50"
                      >
                        <FontAwesomeIcon icon={faTrash} />
                      </Button>
                    </div>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>

        {filteredVacaciones.length === 0 && (
          <div className="text-center py-12">
            <FontAwesomeIcon icon={faPlane} className="text-muted text-4xl mb-4" />
            <p className="text-muted text-lg">No se encontraron solicitudes de vacaciones</p>
            <p className="text-muted">Intenta ajustar los filtros de búsqueda</p>
          </div>
        )}
      </div>

      {/* Modal Simple */}
      {isModalOpen && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-card rounded-lg shadow-xl max-w-md w-full mx-4">
            <div className="p-6">
              <div className="flex justify-between items-center mb-4">
                <h3 className="text-lg font-semibold text-primary">
                  {editingVacacion ? 'Editar Solicitud' : 'Nueva Solicitud de Vacaciones'}
                </h3>
                <button
                  onClick={() => {
                    setIsModalOpen(false);
                    setEditingVacacion(null);
                  }}
                  className="text-muted hover:text-primary"
                >
                  <FontAwesomeIcon icon={faTimes} className="text-xl" />
                </button>
              </div>

              <div className="text-center py-8">
                <FontAwesomeIcon icon={faPlane} className="text-muted text-4xl mb-4" />
                <p className="text-muted">Modal de vacaciones próximamente</p>
                <p className="text-sm text-muted mt-2">
                  Funcionalidad de {editingVacacion ? 'edición' : 'creación'} en desarrollo
                </p>
              </div>

              <div className="flex justify-end">
                <Button
                  variant="outline"
                  onClick={() => {
                    setIsModalOpen(false);
                    setEditingVacacion(null);
                  }}
                >
                  Cerrar
                </Button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default GestionVacaciones;
