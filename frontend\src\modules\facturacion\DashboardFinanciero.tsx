import React, { useState } from 'react';
import { Button } from '../../components/ui/Button';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { 
  faFileInvoiceDollar, 
  faChartLine, 
  faMoneyBillWave,
  faCheckCircle,
  faTimesCircle,
  faClock,
  faExclamationTriangle,
  faCalendarAlt,
  faArrowUp,
  faArrowDown,
  faDollarSign,
  faPercent,
  faRefresh,
  faTrendUp,
  faTrendDown,
  faBuilding,
  faUser,
  faCreditCard,
  faHandHoldingUsd
} from '@fortawesome/free-solid-svg-icons';

// Datos mock para el dashboard financiero
const dashboardFinanciero = {
  resumenMensual: {
    facturacionTotal: 145678900,
    recaudoTotal: 98456700,
    carteraPendiente: 47222200,
    facturasPagadas: 892,
    facturasPendientes: 234,
    facturas_vencidas: 121,
    tasaRecaudo: 67.6,
    promedioDiasPago: 18.5
  },
  tendenciasMensuales: [
    { mes: 'Ene', facturado: 12500000, recaudado: 8900000, cartera: 3600000 },
    { mes: 'Feb', facturado: 13750000, recaudado: 9800000, cartera: 3950000 },
    { mes: 'Mar', facturado: 11300000, recaudado: 8200000, cartera: 3100000 },
    { mes: 'Abr', facturado: 14900000, recaudado: 10500000, cartera: 4400000 },
    { mes: 'May', facturado: 16100000, recaudado: 11200000, cartera: 4900000 },
    { mes: 'Jun', facturado: 15800000, recaudado: 10800000, cartera: 5000000 }
  ],
  topPacientes: [
    { nombre: 'Juan Pérez', facturas: 12, monto: 2500000, estado: 'Al día' },
    { nombre: 'María González', facturas: 8, monto: 1800000, estado: 'Pendiente' },
    { nombre: 'Carlos López', facturas: 15, monto: 3200000, estado: 'Vencido' },
    { nombre: 'Ana Martínez', facturas: 6, monto: 1200000, estado: 'Al día' },
    { nombre: 'Luis Rodríguez', facturas: 10, monto: 2100000, estado: 'Pendiente' }
  ],
  metodosPago: [
    { metodo: 'Tarjeta de Crédito', cantidad: 456, porcentaje: 45.6, monto: 65400000 },
    { metodo: 'Transferencia', cantidad: 234, porcentaje: 23.4, monto: 34200000 },
    { metodo: 'Efectivo', cantidad: 189, porcentaje: 18.9, monto: 18900000 },
    { metodo: 'Cheque', cantidad: 78, porcentaje: 7.8, monto: 8900000 },
    { metodo: 'Otros', cantidad: 43, porcentaje: 4.3, monto: 4500000 }
  ],
  alertasFinancieras: [
    { tipo: 'Crítico', mensaje: 'Cartera vencida supera el 15% del total', valor: 47222200 },
    { tipo: 'Advertencia', mensaje: 'Disminución del 8% en recaudo vs mes anterior', valor: -8 },
    { tipo: 'Oportunidad', mensaje: '23 facturas próximas a vencer requieren seguimiento', valor: 23 },
    { tipo: 'Positivo', mensaje: 'Tiempo promedio de pago mejoró en 2.3 días', valor: 2.3 }
  ]
};

export const DashboardFinanciero: React.FC = () => {
  const [ultimaActualizacion, setUltimaActualizacion] = useState(new Date());
  const [periodoSeleccionado, setPeriodoSeleccionado] = useState('mes_actual');

  const actualizarDatos = () => {
    setUltimaActualizacion(new Date());
    // TODO: Implementar actualización real de datos
  };

  const formatCurrency = (value: number) => {
    return new Intl.NumberFormat('es-CO', {
      style: 'currency',
      currency: 'COP',
      minimumFractionDigits: 0
    }).format(value);
  };

  const getAlertaColor = (tipo: string) => {
    switch (tipo) {
      case 'Crítico':
        return 'border-error text-error bg-error/20';
      case 'Advertencia':
        return 'border-warning text-warning bg-warning/20';
      case 'Oportunidad':
        return 'border-info text-info bg-info/20';
      case 'Positivo':
        return 'border-success text-success bg-success/20';
      default:
        return 'border-secondary text-secondary bg-secondary/20';
    }
  };

  const getAlertaIcon = (tipo: string) => {
    switch (tipo) {
      case 'Crítico':
        return faExclamationTriangle;
      case 'Advertencia':
        return faClock;
      case 'Oportunidad':
        return faArrowUp;
      case 'Positivo':
        return faCheckCircle;
      default:
        return faFileInvoiceDollar;
    }
  };

  return (
    <div className="space-y-6">
      {/* Header del Dashboard */}
      <div className="bg-card p-6 rounded-lg shadow-lg">
        <div className="flex justify-between items-center">
          <div>
            <h1 className="text-2xl font-bold text-primary flex items-center">
              <FontAwesomeIcon icon={faChartLine} className="mr-2" />
              Dashboard Financiero
            </h1>
            <p className="text-muted mt-1">
              Vista ejecutiva del estado financiero y facturación
            </p>
          </div>
          <div className="flex items-center gap-4">
            <select
              value={periodoSeleccionado}
              onChange={(e) => setPeriodoSeleccionado(e.target.value)}
              className="p-2 rounded-lg bg-secondary border border-color text-primary focus:ring-2 focus:ring-primary"
            >
              <option value="mes_actual">Mes Actual</option>
              <option value="trimestre">Trimestre</option>
              <option value="semestre">Semestre</option>
              <option value="año">Año</option>
            </select>
            <div className="text-right">
              <div className="text-sm text-muted">Última actualización</div>
              <div className="text-primary font-medium">{ultimaActualizacion.toLocaleString()}</div>
            </div>
            <Button variant="outline" onClick={actualizarDatos}>
              <FontAwesomeIcon icon={faRefresh} className="mr-2" />
              Actualizar
            </Button>
          </div>
        </div>
      </div>

      {/* KPIs Principales */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <div className="bg-card p-6 rounded-lg shadow-lg">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-muted">Facturación Total</p>
              <p className="text-2xl font-bold text-primary">{formatCurrency(dashboardFinanciero.resumenMensual.facturacionTotal)}</p>
              <p className="text-sm text-success flex items-center mt-1">
                <FontAwesomeIcon icon={faArrowUp} className="mr-1" />
                +12.5% vs mes anterior
              </p>
            </div>
            <div className="bg-primary/20 p-3 rounded-full">
              <FontAwesomeIcon icon={faFileInvoiceDollar} className="text-primary text-2xl" />
            </div>
          </div>
        </div>

        <div className="bg-card p-6 rounded-lg shadow-lg">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-muted">Recaudo Total</p>
              <p className="text-2xl font-bold text-success">{formatCurrency(dashboardFinanciero.resumenMensual.recaudoTotal)}</p>
              <p className="text-sm text-error flex items-center mt-1">
                <FontAwesomeIcon icon={faArrowDown} className="mr-1" />
                -3.2% vs mes anterior
              </p>
            </div>
            <div className="bg-success/20 p-3 rounded-full">
              <FontAwesomeIcon icon={faMoneyBillWave} className="text-success text-2xl" />
            </div>
          </div>
        </div>

        <div className="bg-card p-6 rounded-lg shadow-lg">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-muted">Cartera Pendiente</p>
              <p className="text-2xl font-bold text-warning">{formatCurrency(dashboardFinanciero.resumenMensual.carteraPendiente)}</p>
              <p className="text-sm text-warning flex items-center mt-1">
                <FontAwesomeIcon icon={faArrowUp} className="mr-1" />
                +8.7% vs mes anterior
              </p>
            </div>
            <div className="bg-warning/20 p-3 rounded-full">
              <FontAwesomeIcon icon={faHandHoldingUsd} className="text-warning text-2xl" />
            </div>
          </div>
        </div>

        <div className="bg-card p-6 rounded-lg shadow-lg">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-muted">Tasa de Recaudo</p>
              <p className="text-3xl font-bold text-info">{dashboardFinanciero.resumenMensual.tasaRecaudo}%</p>
              <p className="text-sm text-success flex items-center mt-1">
                <FontAwesomeIcon icon={faArrowUp} className="mr-1" />
                +2.1% vs mes anterior
              </p>
            </div>
            <div className="bg-info/20 p-3 rounded-full">
              <FontAwesomeIcon icon={faPercent} className="text-info text-2xl" />
            </div>
          </div>
        </div>
      </div>

      {/* Gráfico de Tendencias y Alertas */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <div className="bg-card p-6 rounded-lg shadow-lg">
          <h3 className="text-lg font-semibold text-primary mb-4 flex items-center">
            <FontAwesomeIcon icon={faTrendUp} className="mr-2" />
            📈 Tendencias Mensuales
          </h3>
          
          <div className="space-y-4">
            <div className="grid grid-cols-3 gap-4 text-center">
              <div>
                <p className="text-sm text-muted">Facturado</p>
                <p className="text-lg font-bold text-primary">📊</p>
              </div>
              <div>
                <p className="text-sm text-muted">Recaudado</p>
                <p className="text-lg font-bold text-success">💰</p>
              </div>
              <div>
                <p className="text-sm text-muted">Cartera</p>
                <p className="text-lg font-bold text-warning">📋</p>
              </div>
            </div>
            
            {dashboardFinanciero.tendenciasMensuales.map((mes, index) => (
              <div key={index} className="flex items-center justify-between p-3 bg-secondary/20 rounded-lg">
                <div className="font-medium text-primary">{mes.mes}</div>
                <div className="flex gap-4 text-sm">
                  <span className="text-primary">{formatCurrency(mes.facturado)}</span>
                  <span className="text-success">{formatCurrency(mes.recaudado)}</span>
                  <span className="text-warning">{formatCurrency(mes.cartera)}</span>
                </div>
              </div>
            ))}
          </div>
        </div>

        <div className="bg-card p-6 rounded-lg shadow-lg">
          <h3 className="text-lg font-semibold text-primary mb-4 flex items-center">
            <FontAwesomeIcon icon={faExclamationTriangle} className="mr-2" />
            🚨 Alertas Financieras
          </h3>
          
          <div className="space-y-3">
            {dashboardFinanciero.alertasFinancieras.map((alerta, index) => (
              <div key={index} className={`p-3 rounded-lg border-l-4 ${getAlertaColor(alerta.tipo)}`}>
                <div className="flex items-start justify-between">
                  <div className="flex items-center">
                    <FontAwesomeIcon icon={getAlertaIcon(alerta.tipo)} className="mr-2" />
                    <div>
                      <div className="font-medium">{alerta.tipo}</div>
                      <div className="text-sm opacity-80">{alerta.mensaje}</div>
                    </div>
                  </div>
                  <div className="font-bold">
                    {typeof alerta.valor === 'number' && alerta.valor > 1000 
                      ? formatCurrency(alerta.valor)
                      : alerta.valor
                    }
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* Top Pacientes y Métodos de Pago */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <div className="bg-card p-6 rounded-lg shadow-lg">
          <h3 className="text-lg font-semibold text-primary mb-4 flex items-center">
            <FontAwesomeIcon icon={faUser} className="mr-2" />
            👥 Top Pacientes por Facturación
          </h3>
          
          <div className="space-y-3">
            {dashboardFinanciero.topPacientes.map((paciente, index) => (
              <div key={index} className="flex items-center justify-between p-3 bg-secondary/20 rounded-lg">
                <div className="flex items-center">
                  <div className="bg-primary text-white rounded-full w-8 h-8 flex items-center justify-center text-sm font-bold mr-3">
                    {index + 1}
                  </div>
                  <div>
                    <div className="font-medium text-primary">{paciente.nombre}</div>
                    <div className="text-sm text-muted">{paciente.facturas} facturas</div>
                  </div>
                </div>
                <div className="text-right">
                  <div className="font-bold text-primary">{formatCurrency(paciente.monto)}</div>
                  <div className={`text-xs ${
                    paciente.estado === 'Al día' ? 'text-success' :
                    paciente.estado === 'Pendiente' ? 'text-warning' :
                    'text-error'
                  }`}>
                    {paciente.estado}
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>

        <div className="bg-card p-6 rounded-lg shadow-lg">
          <h3 className="text-lg font-semibold text-primary mb-4 flex items-center">
            <FontAwesomeIcon icon={faCreditCard} className="mr-2" />
            💳 Métodos de Pago
          </h3>
          
          <div className="space-y-3">
            {dashboardFinanciero.metodosPago.map((metodo, index) => (
              <div key={index} className="flex items-center justify-between p-3 bg-secondary/20 rounded-lg">
                <div className="flex items-center">
                  <div className="w-3 h-3 rounded-full mr-3" style={{
                    backgroundColor: ['#3B82F6', '#10B981', '#F59E0B', '#EF4444', '#8B5CF6'][index]
                  }}></div>
                  <div>
                    <div className="font-medium text-primary">{metodo.metodo}</div>
                    <div className="text-sm text-muted">{metodo.cantidad} transacciones</div>
                  </div>
                </div>
                <div className="text-right">
                  <div className="font-bold text-primary">{formatCurrency(metodo.monto)}</div>
                  <div className="text-sm text-muted">{metodo.porcentaje}%</div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* Indicadores Adicionales */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <div className="bg-card p-6 rounded-lg shadow-lg">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-muted">Facturas Pagadas</p>
              <p className="text-2xl font-bold text-success">{dashboardFinanciero.resumenMensual.facturasPagadas}</p>
              <p className="text-sm text-muted">Este mes</p>
            </div>
            <FontAwesomeIcon icon={faCheckCircle} className="text-success text-3xl" />
          </div>
        </div>

        <div className="bg-card p-6 rounded-lg shadow-lg">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-muted">Facturas Pendientes</p>
              <p className="text-2xl font-bold text-warning">{dashboardFinanciero.resumenMensual.facturasPendientes}</p>
              <p className="text-sm text-muted">Requieren seguimiento</p>
            </div>
            <FontAwesomeIcon icon={faClock} className="text-warning text-3xl" />
          </div>
        </div>

        <div className="bg-card p-6 rounded-lg shadow-lg">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-muted">Facturas Vencidas</p>
              <p className="text-2xl font-bold text-error">{dashboardFinanciero.resumenMensual.facturas_vencidas}</p>
              <p className="text-sm text-muted">Gestión urgente</p>
            </div>
            <FontAwesomeIcon icon={faExclamationTriangle} className="text-error text-3xl" />
          </div>
        </div>

        <div className="bg-card p-6 rounded-lg shadow-lg">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-muted">Días Pago Promedio</p>
              <p className="text-2xl font-bold text-info">{dashboardFinanciero.resumenMensual.promedioDiasPago}</p>
              <p className="text-sm text-muted">Mejoró 2.3 días</p>
            </div>
            <FontAwesomeIcon icon={faCalendarAlt} className="text-info text-3xl" />
          </div>
        </div>
      </div>
    </div>
  );
};
