export interface Diagnosis {
  id: string;
  code: string;
  title: string;
  description?: string;
  source: 'CIE-10' | 'CIE-11';
  language: string;
  category?: string;
  parent?: string;
  children?: string[];
  mappings?: {
    cie10?: string;
    cie11?: string;
  };
  metadata?: {
    lastUpdated?: string;
    version?: string;
    status?: 'active' | 'deprecated' | 'draft';
  };
}

export interface DiagnosisSearchParams {
  query: string;
  version?: 'CIE-10' | 'CIE-11' | 'BOTH';
  language?: 'es' | 'en';
  limit?: number;
  category?: string;
  includeDeprecated?: boolean;
}

export interface DiagnosisSearchResponse {
  results: Diagnosis[];
  total: number;
  hasMore: boolean;
  suggestions?: string[];
  searchTime?: number;
  filters?: {
    categories: string[];
    sources: string[];
  };
}

export interface TransitionMapping {
  cie10Code: string;
  cie10Title: string;
  cie11Code?: string;
  cie11Title?: string;
  mappingType: 'exact' | 'approximate' | 'multiple' | 'none';
  confidence: number;
  notes?: string;
  alternatives?: {
    code: string;
    title: string;
    confidence: number;
  }[];
}

export interface DiagnosisCategory {
  id: string;
  name: string;
  description?: string;
  parent?: string;
  children?: string[];
  count?: number;
}

export interface DiagnosisFilter {
  categories?: string[];
  sources?: ('CIE-10' | 'CIE-11')[];
  languages?: string[];
  status?: ('active' | 'deprecated' | 'draft')[];
}

export interface DiagnosisHistory {
  id: string;
  diagnosisId: string;
  action: 'created' | 'updated' | 'deprecated' | 'mapped';
  timestamp: string;
  userId?: string;
  changes?: {
    field: string;
    oldValue: any;
    newValue: any;
  }[];
}

export interface DiagnosisValidation {
  isValid: boolean;
  errors: string[];
  warnings: string[];
  suggestions: string[];
}

export interface DiagnosisUsageStats {
  diagnosisId: string;
  usageCount: number;
  lastUsed: string;
  popularityRank?: number;
  trendDirection?: 'up' | 'down' | 'stable';
}

export interface TransitionReport {
  totalMappings: number;
  exactMappings: number;
  approximateMappings: number;
  noMappings: number;
  recommendations: string[];
  progress: {
    completed: number;
    pending: number;
    reviewed: number;
  };
  timeline?: {
    phase: string;
    startDate: string;
    endDate: string;
    status: 'pending' | 'in-progress' | 'completed';
  }[];
}

// Hooks types
export interface UseDiagnosisReturn {
  // Search functionality
  searchDiagnosis: (params: DiagnosisSearchParams) => Promise<void>;
  results: Diagnosis[];
  isLoading: boolean;
  error: string | null;
  
  // Single diagnosis
  getDiagnosis: (id: string, version?: 'CIE-10' | 'CIE-11') => Promise<void>;
  currentDiagnosis: Diagnosis | null;
  
  // Mapping functionality
  mapCie10ToCie11: (cie10Code: string) => Promise<TransitionMapping | null>;
  
  // Cache management
  clearCache: () => void;
  
  // Filters and categories
  categories: DiagnosisCategory[];
  filters: DiagnosisFilter;
  setFilters: (filters: DiagnosisFilter) => void;
  
  // History and stats
  getUsageStats: (diagnosisId: string) => Promise<DiagnosisUsageStats | null>;
  
  // Validation
  validateDiagnosis: (diagnosis: Partial<Diagnosis>) => DiagnosisValidation;
}

// Component props types
export interface DiagnosticSearchProps {
  onSelect: (diagnosis: Diagnosis) => void;
  version?: 'CIE-10' | 'CIE-11' | 'BOTH';
  placeholder?: string;
  multiple?: boolean;
  disabled?: boolean;
  className?: string;
  showFilters?: boolean;
  showSuggestions?: boolean;
  maxResults?: number;
  autoFocus?: boolean;
  clearOnSelect?: boolean;
  value?: Diagnosis | Diagnosis[];
}

export interface DiagnosticSelectorProps extends DiagnosticSearchProps {
  label?: string;
  error?: string;
  helper?: string;
  required?: boolean;
  variant?: 'default' | 'compact' | 'detailed';
}

export interface TransitionHelperProps {
  cie10Code?: string;
  onMappingSelect?: (mapping: TransitionMapping) => void;
  showAlternatives?: boolean;
  showConfidence?: boolean;
  className?: string;
}

export interface DiagnosisComparisonProps {
  cie10Diagnosis?: Diagnosis;
  cie11Diagnosis?: Diagnosis;
  mapping?: TransitionMapping;
  showDetails?: boolean;
  onSelectPreferred?: (diagnosis: Diagnosis) => void;
}

// API Response types
export interface WHOAPIResponse<T> {
  data: T;
  status: number;
  message?: string;
  timestamp: string;
  requestId?: string;
}

export interface WHOSearchResponse {
  destinationEntities: any[];
  error?: boolean;
  errorMessage?: string;
  guessType?: {
    candidatesFound: boolean;
  };
}

// Error types
export interface DiagnosisError {
  code: string;
  message: string;
  details?: any;
  timestamp: string;
  retryable: boolean;
}

// Cache types
export interface DiagnosisCacheEntry<T> {
  data: T;
  timestamp: number;
  expiresAt: number;
  key: string;
}

export interface DiagnosisCacheConfig {
  maxSize: number;
  defaultTTL: number;
  cleanupInterval: number;
}

// Event types
export interface DiagnosisEvent {
  type: 'search' | 'select' | 'map' | 'validate';
  payload: any;
  timestamp: string;
  userId?: string;
}

// Configuration types
export interface DiagnosisConfig {
  apiBaseUrl: string;
  defaultLanguage: string;
  defaultVersion: 'CIE-10' | 'CIE-11' | 'BOTH';
  cacheConfig: DiagnosisCacheConfig;
  searchConfig: {
    minQueryLength: number;
    debounceMs: number;
    maxResults: number;
  };
  features: {
    enableMapping: boolean;
    enableValidation: boolean;
    enableStats: boolean;
    enableHistory: boolean;
  };
}
