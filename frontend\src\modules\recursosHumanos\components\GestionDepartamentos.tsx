import React, { useState } from 'react';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import {
  faSitemap,
  faPlus,
  faEdit,
  faTrash,
  faUsers,
  faSearch,
  faEye,
  faBuilding,
  faUserTie,
  faChartBar,
  faTimes,
  faFileAlt
} from '@fortawesome/free-solid-svg-icons';
import { Button } from '../../../components/ui/Button';
import { Input } from '../../../components/ui/Input';
import { toast } from 'react-hot-toast';

// Datos mock de departamentos
const mockDepartamentos = [
  {
    id: 1,
    nombre: 'Medicina Interna',
    descripcion: 'Departamento de medicina interna y especialidades médicas',
    jefe_departamento: 'Dr. <PERSON>',
    total_empleados: 15,
    presupuesto_anual: 2500000000,
    estado: 'Activo',
    fecha_creacion: '2020-01-15'
  },
  {
    id: 2,
    nombre: 'Enfermería',
    descripcion: 'Departamento de enfermería y cuidados especializados',
    jefe_departamento: 'Enf. <PERSON>',
    total_empleados: 45,
    presupuesto_anual: 1800000000,
    estado: 'Activo',
    fecha_creacion: '2019-03-20'
  },
  {
    id: 3,
    nombre: 'Urgencias',
    descripcion: 'Departamento de urgencias y emergencias médicas',
    jefe_departamento: 'Dr. Ana María González',
    total_empleados: 25,
    presupuesto_anual: 3200000000,
    estado: 'Activo',
    fecha_creacion: '2018-06-10'
  },
  {
    id: 4,
    nombre: 'Cirugía',
    descripcion: 'Departamento de cirugía general y especializada',
    jefe_departamento: 'Dr. Luis Fernando Martínez',
    total_empleados: 20,
    presupuesto_anual: 4500000000,
    estado: 'Activo',
    fecha_creacion: '2017-09-05'
  },
  {
    id: 5,
    nombre: 'Administración',
    descripcion: 'Departamento administrativo y recursos humanos',
    jefe_departamento: 'Adm. Patricia Hernández',
    total_empleados: 12,
    presupuesto_anual: 800000000,
    estado: 'Activo',
    fecha_creacion: '2016-11-12'
  },
  {
    id: 6,
    nombre: 'Laboratorio Clínico',
    descripcion: 'Departamento de análisis clínicos y patología',
    jefe_departamento: 'Dr. Roberto Silva',
    total_empleados: 8,
    presupuesto_anual: 1200000000,
    estado: 'Activo',
    fecha_creacion: '2019-02-28'
  }
];

const GestionDepartamentos: React.FC = () => {
  const [departamentos, setDepartamentos] = useState(mockDepartamentos);
  const [searchTerm, setSearchTerm] = useState('');
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [editingDepartamento, setEditingDepartamento] = useState<any>(null);

  const filteredDepartamentos = departamentos.filter(dept =>
    dept.nombre.toLowerCase().includes(searchTerm.toLowerCase()) ||
    dept.jefe_departamento.toLowerCase().includes(searchTerm.toLowerCase()) ||
    dept.descripcion.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const formatCurrency = (value: number) => {
    return new Intl.NumberFormat('es-CO', {
      style: 'currency',
      currency: 'COP',
      minimumFractionDigits: 0
    }).format(value);
  };

  const handleEdit = (departamento: any) => {
    setEditingDepartamento(departamento);
    setIsModalOpen(true);
    toast.info(`Editando departamento: ${departamento.nombre}`);
  };

  const handleDelete = (id: number) => {
    const dept = departamentos.find(d => d.id === id);
    if (window.confirm(`¿Está seguro de eliminar el departamento "${dept?.nombre}"?`)) {
      setDepartamentos(prev => prev.filter(d => d.id !== id));
      toast.success('Departamento eliminado exitosamente');
    }
  };

  const totalEmpleados = departamentos.reduce((sum, d) => sum + d.total_empleados, 0);
  const totalPresupuesto = departamentos.reduce((sum, d) => sum + d.presupuesto_anual, 0);
  const departamentosActivos = departamentos.filter(d => d.estado === 'Activo').length;

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="bg-card p-6 rounded-lg shadow-lg">
        <div className="flex justify-between items-center">
          <div>
            <h2 className="text-2xl font-bold text-primary flex items-center">
              <FontAwesomeIcon icon={faSitemap} className="mr-2" />
              Gestión de Departamentos
            </h2>
            <p className="text-muted mt-1">
              Administración de la estructura organizacional
            </p>
          </div>
          <Button onClick={() => setIsModalOpen(true)}>
            <FontAwesomeIcon icon={faPlus} className="mr-2" />
            Nuevo Departamento
          </Button>
        </div>
      </div>

      {/* Estadísticas */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <div className="bg-card p-6 rounded-lg shadow">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-muted">Total Departamentos</p>
              <p className="text-2xl font-bold text-primary">{departamentos.length}</p>
            </div>
            <FontAwesomeIcon icon={faSitemap} className="text-primary text-2xl" />
          </div>
        </div>

        <div className="bg-card p-6 rounded-lg shadow">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-muted">Departamentos Activos</p>
              <p className="text-2xl font-bold text-success">{departamentosActivos}</p>
            </div>
            <FontAwesomeIcon icon={faBuilding} className="text-success text-2xl" />
          </div>
        </div>

        <div className="bg-card p-6 rounded-lg shadow">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-muted">Total Empleados</p>
              <p className="text-2xl font-bold text-info">{totalEmpleados}</p>
            </div>
            <FontAwesomeIcon icon={faUsers} className="text-info text-2xl" />
          </div>
        </div>

        <div className="bg-card p-6 rounded-lg shadow">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-muted">Presupuesto Total</p>
              <p className="text-lg font-bold text-warning">{formatCurrency(totalPresupuesto)}</p>
            </div>
            <FontAwesomeIcon icon={faChartBar} className="text-warning text-2xl" />
          </div>
        </div>
      </div>

      {/* Búsqueda */}
      <div className="bg-card p-6 rounded-lg shadow-lg">
        <div className="relative">
          <FontAwesomeIcon icon={faSearch} className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted" />
          <Input
            type="text"
            placeholder="Buscar departamentos..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="pl-10"
          />
        </div>
      </div>

      {/* Tabla de Departamentos */}
      <div className="bg-card rounded-lg shadow-lg overflow-hidden">
        <div className="overflow-x-auto">
          <table className="w-full">
            <thead className="bg-secondary/50">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-secondary uppercase tracking-wider">
                  Departamento
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-secondary uppercase tracking-wider">
                  Jefe de Departamento
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-secondary uppercase tracking-wider">
                  Empleados
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-secondary uppercase tracking-wider">
                  Presupuesto Anual
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-secondary uppercase tracking-wider">
                  Estado
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-secondary uppercase tracking-wider">
                  Acciones
                </th>
              </tr>
            </thead>
            <tbody className="bg-card divide-y divide-color">
              {filteredDepartamentos.map((departamento) => (
                <tr key={departamento.id} className="hover:bg-secondary/20 transition-colors">
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div>
                      <div className="text-sm font-medium text-primary flex items-center">
                        <FontAwesomeIcon icon={faBuilding} className="mr-2 text-muted" />
                        {departamento.nombre}
                      </div>
                      <div className="text-sm text-muted">
                        {departamento.descripcion}
                      </div>
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="text-sm text-primary flex items-center">
                      <FontAwesomeIcon icon={faUserTie} className="mr-2 text-muted" />
                      {departamento.jefe_departamento}
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="text-sm font-medium text-primary flex items-center">
                      <FontAwesomeIcon icon={faUsers} className="mr-2 text-muted" />
                      {departamento.total_empleados}
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="text-sm font-medium text-primary">
                      {formatCurrency(departamento.presupuesto_anual)}
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-success/20 text-success">
                      {departamento.estado}
                    </span>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                    <div className="flex space-x-2">
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => toast.info(`Viendo detalles de ${departamento.nombre}`)}
                        className="text-blue-600 border-blue-600 hover:bg-blue-50"
                      >
                        <FontAwesomeIcon icon={faEye} />
                      </Button>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => handleEdit(departamento)}
                        className="text-green-600 border-green-600 hover:bg-green-50"
                      >
                        <FontAwesomeIcon icon={faEdit} />
                      </Button>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => handleDelete(departamento.id)}
                        className="text-red-600 border-red-600 hover:bg-red-50"
                      >
                        <FontAwesomeIcon icon={faTrash} />
                      </Button>
                    </div>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>

        {filteredDepartamentos.length === 0 && (
          <div className="text-center py-12">
            <FontAwesomeIcon icon={faSitemap} className="text-muted text-4xl mb-4" />
            <p className="text-muted text-lg">No se encontraron departamentos</p>
            <p className="text-muted">Intenta ajustar la búsqueda</p>
          </div>
        )}
      </div>

      {/* Modal Funcional */}
      {isModalOpen && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-card rounded-lg shadow-xl max-w-2xl w-full mx-4 max-h-[90vh] overflow-y-auto">
            <div className="p-6">
              <div className="flex justify-between items-center mb-6">
                <h3 className="text-xl font-semibold text-primary flex items-center">
                  <FontAwesomeIcon icon={faSitemap} className="mr-2" />
                  {editingDepartamento ? 'Editar Departamento' : 'Nuevo Departamento'}
                </h3>
                <button
                  onClick={() => {
                    setIsModalOpen(false);
                    setEditingDepartamento(null);
                  }}
                  className="text-muted hover:text-primary"
                >
                  <FontAwesomeIcon icon={faTimes} className="text-xl" />
                </button>
              </div>

              <form onSubmit={(e) => {
                e.preventDefault();
                const formData = new FormData(e.target as HTMLFormElement);
                const nuevoDepartamento = {
                  id: editingDepartamento ? editingDepartamento.id : Date.now(),
                  nombre: formData.get('nombre') as string,
                  descripcion: formData.get('descripcion') as string,
                  jefe_departamento: formData.get('jefe_departamento') as string,
                  presupuesto_anual: parseInt(formData.get('presupuesto_anual') as string),
                  total_empleados: editingDepartamento ? editingDepartamento.total_empleados : 0,
                  estado: 'Activo',
                  fecha_creacion: editingDepartamento ? editingDepartamento.fecha_creacion : new Date().toISOString().split('T')[0]
                };

                if (editingDepartamento) {
                  setDepartamentos(prev => prev.map(d => d.id === editingDepartamento.id ? nuevoDepartamento : d));
                  toast.success('Departamento actualizado exitosamente');
                } else {
                  setDepartamentos(prev => [...prev, nuevoDepartamento]);
                  toast.success('Departamento creado exitosamente');
                }

                setIsModalOpen(false);
                setEditingDepartamento(null);
              }}>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  {/* Nombre del Departamento */}
                  <div className="md:col-span-2">
                    <label className="block text-sm font-medium text-secondary mb-2">
                      <FontAwesomeIcon icon={faBuilding} className="mr-2" />
                      Nombre del Departamento *
                    </label>
                    <Input
                      name="nombre"
                      type="text"
                      required
                      defaultValue={editingDepartamento?.nombre || ''}
                      placeholder="Ej: Medicina Interna"
                      className="w-full"
                    />
                  </div>

                  {/* Jefe de Departamento */}
                  <div>
                    <label className="block text-sm font-medium text-secondary mb-2">
                      <FontAwesomeIcon icon={faUserTie} className="mr-2" />
                      Jefe de Departamento *
                    </label>
                    <Input
                      name="jefe_departamento"
                      type="text"
                      required
                      defaultValue={editingDepartamento?.jefe_departamento || ''}
                      placeholder="Ej: Dr. Carlos Rodríguez"
                      className="w-full"
                    />
                  </div>

                  {/* Presupuesto Anual */}
                  <div>
                    <label className="block text-sm font-medium text-secondary mb-2">
                      <FontAwesomeIcon icon={faChartBar} className="mr-2" />
                      Presupuesto Anual *
                    </label>
                    <Input
                      name="presupuesto_anual"
                      type="number"
                      required
                      min="0"
                      step="1000000"
                      defaultValue={editingDepartamento?.presupuesto_anual || ''}
                      placeholder="Ej: 2500000000"
                      className="w-full"
                    />
                    <p className="text-xs text-muted mt-1">Valor en pesos colombianos</p>
                  </div>

                  {/* Descripción */}
                  <div className="md:col-span-2">
                    <label className="block text-sm font-medium text-secondary mb-2">
                      <FontAwesomeIcon icon={faFileAlt} className="mr-2" />
                      Descripción del Departamento *
                    </label>
                    <textarea
                      name="descripcion"
                      required
                      rows={4}
                      defaultValue={editingDepartamento?.descripcion || ''}
                      placeholder="Descripción detallada de las funciones y responsabilidades del departamento..."
                      className="w-full p-3 rounded-lg bg-secondary border border-color text-primary focus:ring-2 focus:ring-primary resize-none"
                    />
                  </div>
                </div>

                <div className="flex justify-end gap-3 mt-6">
                  <Button
                    type="button"
                    variant="outline"
                    onClick={() => {
                      setIsModalOpen(false);
                      setEditingDepartamento(null);
                    }}
                  >
                    Cancelar
                  </Button>
                  <Button type="submit">
                    <FontAwesomeIcon icon={editingDepartamento ? faEdit : faPlus} className="mr-2" />
                    {editingDepartamento ? 'Actualizar' : 'Crear'} Departamento
                  </Button>
                </div>
              </form>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default GestionDepartamentos;
