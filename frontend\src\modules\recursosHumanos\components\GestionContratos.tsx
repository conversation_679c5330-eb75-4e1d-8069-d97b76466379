import React, { useState } from 'react';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import {
  faFileContract,
  faPlus,
  faSearch,
  faEdit,
  faTrash,
  faEye,
  faCalendarAlt,
  faUser,
  faMoneyBillWave,
  faCheckCircle,
  faExclamationTriangle,
  faTimesCircle,
  faClock,
  faFileAlt,
  faTimes,
  faBriefcase,
  faBuilding
} from '@fortawesome/free-solid-svg-icons';
import { Button } from '../../../components/ui/Button';
import { Input } from '../../../components/ui/Input';
import { toast } from 'react-hot-toast';

// Datos mock de contratos
const mockContratos = [
  {
    id: 1,
    empleado_nombre: '<PERSON>',
    tipo_contrato: 'Indefinido',
    fecha_inicio: '2020-01-15',
    fecha_fin: null,
    salario: 8500000,
    cargo: 'Médico Especialista',
    departamento: 'Medicina Interna',
    estado: 'Activo',
    renovaciones: 0,
    clausulas_especiales: 'Disponibilidad 24/7 para emergencias',
    observaciones: 'Contrato principal con beneficios completos'
  },
  {
    id: 2,
    empleado_nombre: 'María Alejandra López',
    tipo_contrato: 'Indefinido',
    fecha_inicio: '2019-03-20',
    fecha_fin: null,
    salario: 4500000,
    cargo: 'Jefe de Enfermería',
    departamento: 'Enfermería',
    estado: 'Activo',
    renovaciones: 1,
    clausulas_especiales: 'Supervisión de personal de enfermería',
    observaciones: 'Renovado en 2022 con aumento salarial'
  },
  {
    id: 3,
    empleado_nombre: 'Ana María González',
    tipo_contrato: 'Fijo',
    fecha_inicio: '2021-06-10',
    fecha_fin: '2024-06-10',
    salario: 6500000,
    cargo: 'Médico General',
    departamento: 'Urgencias',
    estado: 'Activo',
    renovaciones: 0,
    clausulas_especiales: 'Rotación en turnos nocturnos',
    observaciones: 'Contrato a término fijo por 3 años'
  },
  {
    id: 4,
    empleado_nombre: 'Luis Fernando Martínez',
    tipo_contrato: 'Indefinido',
    fecha_inicio: '2018-09-05',
    fecha_fin: null,
    salario: 12000000,
    cargo: 'Cirujano',
    departamento: 'Cirugía',
    estado: 'Activo',
    renovaciones: 2,
    clausulas_especiales: 'Cirugías de alta complejidad',
    observaciones: 'Contrato senior con incentivos por productividad'
  },
  {
    id: 5,
    empleado_nombre: 'Patricia Hernández',
    tipo_contrato: 'Indefinido',
    fecha_inicio: '2017-11-12',
    fecha_fin: null,
    salario: 5500000,
    cargo: 'Coordinadora RRHH',
    departamento: 'Administración',
    estado: 'Activo',
    renovaciones: 1,
    clausulas_especiales: 'Manejo confidencial de información',
    observaciones: 'Contrato administrativo con responsabilidades especiales'
  },
  {
    id: 6,
    empleado_nombre: 'Roberto Silva',
    tipo_contrato: 'Temporal',
    fecha_inicio: '2023-01-15',
    fecha_fin: '2024-01-15',
    salario: 3800000,
    cargo: 'Auxiliar de Enfermería',
    departamento: 'Enfermería',
    estado: 'Por Vencer',
    renovaciones: 0,
    clausulas_especiales: 'Reemplazo por licencia de maternidad',
    observaciones: 'Contrato temporal por cobertura'
  }
];

const GestionContratos: React.FC = () => {
  const [contratos, setContratos] = useState(mockContratos);
  const [searchTerm, setSearchTerm] = useState('');
  const [filtroTipo, setFiltroTipo] = useState<string>('todos');
  const [filtroEstado, setFiltroEstado] = useState<string>('todos');
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [editingContrato, setEditingContrato] = useState<any>(null);

  const filteredContratos = contratos.filter(contrato => {
    const matchesSearch = contrato.empleado_nombre.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         contrato.cargo.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         contrato.departamento.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesTipo = filtroTipo === 'todos' || contrato.tipo_contrato === filtroTipo;
    const matchesEstado = filtroEstado === 'todos' || contrato.estado === filtroEstado;

    return matchesSearch && matchesTipo && matchesEstado;
  });

  const getEstadoColor = (estado: string) => {
    switch (estado) {
      case 'Activo':
        return 'bg-success/20 text-success';
      case 'Por Vencer':
        return 'bg-warning/20 text-warning';
      case 'Vencido':
        return 'bg-error/20 text-error';
      case 'Suspendido':
        return 'bg-secondary/20 text-secondary';
      case 'Terminado':
        return 'bg-gray/20 text-gray-600';
      default:
        return 'bg-secondary/20 text-secondary';
    }
  };

  const getEstadoIcon = (estado: string) => {
    switch (estado) {
      case 'Activo':
        return faCheckCircle;
      case 'Por Vencer':
        return faExclamationTriangle;
      case 'Vencido':
        return faTimesCircle;
      case 'Suspendido':
        return faClock;
      case 'Terminado':
        return faFileAlt;
      default:
        return faFileContract;
    }
  };

  const getTipoColor = (tipo: string) => {
    switch (tipo) {
      case 'Indefinido':
        return 'text-green-600';
      case 'Fijo':
        return 'text-blue-600';
      case 'Temporal':
        return 'text-orange-600';
      case 'Prestación de Servicios':
        return 'text-purple-600';
      default:
        return 'text-gray-600';
    }
  };

  const formatCurrency = (value: number) => {
    return new Intl.NumberFormat('es-CO', {
      style: 'currency',
      currency: 'COP',
      minimumFractionDigits: 0
    }).format(value);
  };

  const calcularDiasRestantes = (fechaFin: string | null) => {
    if (!fechaFin) return null;
    const hoy = new Date();
    const fin = new Date(fechaFin);
    const diferencia = fin.getTime() - hoy.getTime();
    return Math.ceil(diferencia / (1000 * 3600 * 24));
  };

  const handleEdit = (contrato: any) => {
    setEditingContrato(contrato);
    setIsModalOpen(true);
    toast.success(`Editando contrato de ${contrato.empleado_nombre}`);
  };

  const handleDelete = (id: number) => {
    const contrato = contratos.find(c => c.id === id);
    if (window.confirm(`¿Está seguro de eliminar el contrato de ${contrato?.empleado_nombre}?`)) {
      setContratos(prev => prev.filter(c => c.id !== id));
      toast.success('Contrato eliminado exitosamente');
    }
  };

  const handleRenovar = (id: number) => {
    setContratos(prev =>
      prev.map(contrato =>
        contrato.id === id
          ? {
              ...contrato,
              renovaciones: contrato.renovaciones + 1,
              estado: 'Activo',
              fecha_fin: contrato.tipo_contrato === 'Fijo' ?
                new Date(Date.now() + 365 * 24 * 60 * 60 * 1000).toISOString().split('T')[0] :
                contrato.fecha_fin
            }
          : contrato
      )
    );
    toast.success('Contrato renovado exitosamente');
  };

  const totalContratos = contratos.length;
  const contratosActivos = contratos.filter(c => c.estado === 'Activo').length;
  const contratosPorVencer = contratos.filter(c => c.estado === 'Por Vencer').length;
  const salarioPromedio = contratos.reduce((sum, c) => sum + c.salario, 0) / contratos.length;

  const tiposContrato = [...new Set(contratos.map(c => c.tipo_contrato))];
  const estados = [...new Set(contratos.map(c => c.estado))];

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="bg-card p-6 rounded-lg shadow-lg">
        <div className="flex justify-between items-center">
          <div>
            <h2 className="text-2xl font-bold text-primary flex items-center">
              <FontAwesomeIcon icon={faFileContract} className="mr-2" />
              Gestión de Contratos
            </h2>
            <p className="text-muted mt-1">
              Administración de contratos laborales
            </p>
          </div>
          <Button onClick={() => setIsModalOpen(true)}>
            <FontAwesomeIcon icon={faPlus} className="mr-2" />
            Nuevo Contrato
          </Button>
        </div>
      </div>

      {/* Estadísticas */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <div className="bg-card p-6 rounded-lg shadow">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-muted">Total Contratos</p>
              <p className="text-2xl font-bold text-primary">{totalContratos}</p>
            </div>
            <FontAwesomeIcon icon={faFileContract} className="text-primary text-2xl" />
          </div>
        </div>

        <div className="bg-card p-6 rounded-lg shadow">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-muted">Activos</p>
              <p className="text-2xl font-bold text-success">{contratosActivos}</p>
            </div>
            <FontAwesomeIcon icon={faCheckCircle} className="text-success text-2xl" />
          </div>
        </div>

        <div className="bg-card p-6 rounded-lg shadow">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-muted">Por Vencer</p>
              <p className="text-2xl font-bold text-warning">{contratosPorVencer}</p>
            </div>
            <FontAwesomeIcon icon={faExclamationTriangle} className="text-warning text-2xl" />
          </div>
        </div>

        <div className="bg-card p-6 rounded-lg shadow">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-muted">Salario Promedio</p>
              <p className="text-lg font-bold text-info">{formatCurrency(salarioPromedio)}</p>
            </div>
            <FontAwesomeIcon icon={faMoneyBillWave} className="text-info text-2xl" />
          </div>
        </div>
      </div>

      {/* Filtros */}
      <div className="bg-card p-6 rounded-lg shadow-lg">
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div>
            <label className="block text-sm font-medium text-secondary mb-1">
              Buscar
            </label>
            <div className="relative">
              <FontAwesomeIcon icon={faSearch} className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted" />
              <Input
                type="text"
                placeholder="Empleado, cargo, departamento..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10"
              />
            </div>
          </div>

          <div>
            <label className="block text-sm font-medium text-secondary mb-1">
              Tipo de Contrato
            </label>
            <select
              value={filtroTipo}
              onChange={(e) => setFiltroTipo(e.target.value)}
              className="w-full p-3 rounded-lg bg-secondary border border-color text-primary focus:ring-2 focus:ring-primary"
            >
              <option value="todos">Todos los tipos</option>
              {tiposContrato.map((tipo) => (
                <option key={tipo} value={tipo}>
                  {tipo}
                </option>
              ))}
            </select>
          </div>

          <div>
            <label className="block text-sm font-medium text-secondary mb-1">
              Estado
            </label>
            <select
              value={filtroEstado}
              onChange={(e) => setFiltroEstado(e.target.value)}
              className="w-full p-3 rounded-lg bg-secondary border border-color text-primary focus:ring-2 focus:ring-primary"
            >
              <option value="todos">Todos los estados</option>
              {estados.map((estado) => (
                <option key={estado} value={estado}>
                  {estado}
                </option>
              ))}
            </select>
          </div>
        </div>
      </div>

      {/* Tabla de Contratos */}
      <div className="glass-card rounded-xl shadow-lg overflow-hidden">
        <div className="table-container">
          <table className="table-responsive">
            <thead className="bg-secondary/50">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-secondary uppercase tracking-wider">
                  Empleado
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-secondary uppercase tracking-wider">
                  Tipo
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-secondary uppercase tracking-wider">
                  Vigencia
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-secondary uppercase tracking-wider">
                  Salario
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-secondary uppercase tracking-wider">
                  Estado
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-secondary uppercase tracking-wider">
                  Renovaciones
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-secondary uppercase tracking-wider">
                  Acciones
                </th>
              </tr>
            </thead>
            <tbody className="bg-card divide-y divide-color">
              {filteredContratos.map((contrato) => {
                const diasRestantes = calcularDiasRestantes(contrato.fecha_fin);
                return (
                  <tr key={contrato.id} className="hover:bg-secondary/20 transition-colors">
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="flex items-center">
                        <FontAwesomeIcon icon={faUser} className="mr-2 text-muted" />
                        <div>
                          <div className="text-sm font-medium text-primary">
                            {contrato.empleado_nombre}
                          </div>
                          <div className="text-sm text-muted">
                            {contrato.cargo} - {contrato.departamento}
                          </div>
                        </div>
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <span className={`text-sm font-medium ${getTipoColor(contrato.tipo_contrato)}`}>
                        {contrato.tipo_contrato}
                      </span>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm text-primary">
                        <div className="flex items-center">
                          <FontAwesomeIcon icon={faCalendarAlt} className="mr-2 text-muted" />
                          {new Date(contrato.fecha_inicio).toLocaleDateString()}
                        </div>
                        {contrato.fecha_fin ? (
                          <div className="text-sm text-muted">
                            hasta {new Date(contrato.fecha_fin).toLocaleDateString()}
                            {diasRestantes !== null && (
                              <span className={`ml-2 ${diasRestantes < 30 ? 'text-warning' : 'text-muted'}`}>
                                ({diasRestantes} días)
                              </span>
                            )}
                          </div>
                        ) : (
                          <div className="text-sm text-success">Indefinido</div>
                        )}
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm font-medium text-primary">
                        {formatCurrency(contrato.salario)}
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getEstadoColor(contrato.estado)}`}>
                        <FontAwesomeIcon icon={getEstadoIcon(contrato.estado)} className="mr-1" />
                        {contrato.estado}
                      </span>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm text-primary">
                        {contrato.renovaciones} renovaciones
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                      <div className="flex space-x-2">
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => toast.success(`Viendo detalles del contrato de ${contrato.empleado_nombre}`)}
                          className="text-blue-600 border-blue-600 hover:bg-blue-50"
                        >
                          <FontAwesomeIcon icon={faEye} />
                        </Button>
                        {(contrato.estado === 'Por Vencer' || contrato.tipo_contrato === 'Fijo') && (
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => handleRenovar(contrato.id)}
                            className="text-green-600 border-green-600 hover:bg-green-50"
                          >
                            Renovar
                          </Button>
                        )}
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => handleEdit(contrato)}
                          className="text-purple-600 border-purple-600 hover:bg-purple-50"
                        >
                          <FontAwesomeIcon icon={faEdit} />
                        </Button>
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => handleDelete(contrato.id)}
                          className="text-red-600 border-red-600 hover:bg-red-50"
                        >
                          <FontAwesomeIcon icon={faTrash} />
                        </Button>
                      </div>
                    </td>
                  </tr>
                );
              })}
            </tbody>
          </table>
        </div>

        {filteredContratos.length === 0 && (
          <div className="text-center py-12">
            <FontAwesomeIcon icon={faFileContract} className="text-muted text-4xl mb-4" />
            <p className="text-muted text-lg">No se encontraron contratos</p>
            <p className="text-muted">Intenta ajustar los filtros de búsqueda</p>
          </div>
        )}
      </div>

      {/* Modal Funcional con Glassmorphism */}
      {isModalOpen && (
        <div className="fixed inset-0 bg-black/60 backdrop-blur-sm flex items-center justify-center z-50 p-4 animate-in fade-in duration-300">
          <div className="glass-modal modal-container max-h-[90vh] overflow-y-auto custom-scrollbar animate-in slide-in-from-bottom-4 duration-300">
            <div className="p-6">
              <div className="flex justify-between items-center mb-6">
                <h3 className="text-xl font-semibold text-primary flex items-center">
                  <FontAwesomeIcon icon={faFileContract} className="mr-2" />
                  {editingContrato ? 'Editar Contrato' : 'Nuevo Contrato'}
                </h3>
                <button
                  onClick={() => {
                    setIsModalOpen(false);
                    setEditingContrato(null);
                  }}
                  className="text-muted hover:text-primary"
                >
                  <FontAwesomeIcon icon={faTimes} className="text-xl" />
                </button>
              </div>

              <form onSubmit={(e) => {
                e.preventDefault();
                const formData = new FormData(e.target as HTMLFormElement);
                const tipoContrato = formData.get('tipo_contrato') as string;

                const nuevoContrato = {
                  id: editingContrato ? editingContrato.id : Date.now(),
                  empleado_nombre: formData.get('empleado_nombre') as string,
                  tipo_contrato: tipoContrato,
                  fecha_inicio: formData.get('fecha_inicio') as string,
                  fecha_fin: tipoContrato === 'Indefinido' ? null : formData.get('fecha_fin') as string,
                  salario: parseInt(formData.get('salario') as string),
                  cargo: formData.get('cargo') as string,
                  departamento: formData.get('departamento') as string,
                  estado: editingContrato ? editingContrato.estado : 'Activo',
                  renovaciones: editingContrato ? editingContrato.renovaciones : 0,
                  clausulas_especiales: formData.get('clausulas_especiales') as string,
                  observaciones: formData.get('observaciones') as string
                };

                if (editingContrato) {
                  setContratos(prev => prev.map(c => c.id === editingContrato.id ? nuevoContrato : c));
                  toast.success('Contrato actualizado exitosamente');
                } else {
                  setContratos(prev => [...prev, nuevoContrato]);
                  toast.success('Contrato creado exitosamente');
                }

                setIsModalOpen(false);
                setEditingContrato(null);
              }}>
                <div className="form-grid">
                  {/* Empleado */}
                  <div className="form-grid-full">
                    <label className="block text-sm font-medium text-secondary mb-2">
                      <FontAwesomeIcon icon={faUser} className="mr-2" />
                      Empleado *
                    </label>
                    <Input
                      name="empleado_nombre"
                      type="text"
                      required
                      defaultValue={editingContrato?.empleado_nombre || ''}
                      placeholder="Nombre completo del empleado"
                      className="w-full"
                    />
                  </div>

                  {/* Tipo de Contrato */}
                  <div>
                    <label className="block text-sm font-medium text-secondary mb-2">
                      <FontAwesomeIcon icon={faFileContract} className="mr-2" />
                      Tipo de Contrato *
                    </label>
                    <select
                      name="tipo_contrato"
                      required
                      defaultValue={editingContrato?.tipo_contrato || ''}
                      className="w-full p-3 rounded-lg bg-secondary border border-color text-primary focus:ring-2 focus:ring-primary"
                      onChange={(e) => {
                        const fechaFinInput = document.querySelector('input[name="fecha_fin"]') as HTMLInputElement;
                        if (fechaFinInput) {
                          fechaFinInput.disabled = e.target.value === 'Indefinido';
                          fechaFinInput.required = e.target.value !== 'Indefinido';
                        }
                      }}
                    >
                      <option value="">Seleccionar tipo</option>
                      <option value="Indefinido">Indefinido</option>
                      <option value="Fijo">Fijo</option>
                      <option value="Temporal">Temporal</option>
                      <option value="Prestación de Servicios">Prestación de Servicios</option>
                    </select>
                  </div>

                  {/* Cargo */}
                  <div>
                    <label className="block text-sm font-medium text-secondary mb-2">
                      <FontAwesomeIcon icon={faBriefcase} className="mr-2" />
                      Cargo *
                    </label>
                    <Input
                      name="cargo"
                      type="text"
                      required
                      defaultValue={editingContrato?.cargo || ''}
                      placeholder="Ej: Médico Especialista"
                      className="w-full"
                    />
                  </div>

                  {/* Departamento */}
                  <div>
                    <label className="block text-sm font-medium text-secondary mb-2">
                      <FontAwesomeIcon icon={faBuilding} className="mr-2" />
                      Departamento *
                    </label>
                    <select
                      name="departamento"
                      required
                      defaultValue={editingContrato?.departamento || ''}
                      className="w-full p-3 rounded-lg bg-secondary border border-color text-primary focus:ring-2 focus:ring-primary"
                    >
                      <option value="">Seleccionar departamento</option>
                      <option value="Medicina Interna">Medicina Interna</option>
                      <option value="Enfermería">Enfermería</option>
                      <option value="Urgencias">Urgencias</option>
                      <option value="Cirugía">Cirugía</option>
                      <option value="Administración">Administración</option>
                      <option value="Laboratorio Clínico">Laboratorio Clínico</option>
                    </select>
                  </div>

                  {/* Salario */}
                  <div>
                    <label className="block text-sm font-medium text-secondary mb-2">
                      <FontAwesomeIcon icon={faMoneyBillWave} className="mr-2" />
                      Salario *
                    </label>
                    <Input
                      name="salario"
                      type="number"
                      required
                      min="0"
                      step="1000"
                      defaultValue={editingContrato?.salario || ''}
                      placeholder="Ej: 5000000"
                      className="w-full"
                    />
                  </div>

                  {/* Fecha de Inicio */}
                  <div>
                    <label className="block text-sm font-medium text-secondary mb-2">
                      <FontAwesomeIcon icon={faCalendarAlt} className="mr-2" />
                      Fecha de Inicio *
                    </label>
                    <Input
                      name="fecha_inicio"
                      type="date"
                      required
                      defaultValue={editingContrato?.fecha_inicio || ''}
                      className="w-full"
                    />
                  </div>

                  {/* Fecha de Fin */}
                  <div>
                    <label className="block text-sm font-medium text-secondary mb-2">
                      <FontAwesomeIcon icon={faCalendarAlt} className="mr-2" />
                      Fecha de Fin
                    </label>
                    <Input
                      name="fecha_fin"
                      type="date"
                      defaultValue={editingContrato?.fecha_fin || ''}
                      disabled={editingContrato?.tipo_contrato === 'Indefinido'}
                      className="w-full"
                    />
                    <p className="text-xs text-muted mt-1">Solo para contratos a término fijo</p>
                  </div>

                  {/* Cláusulas Especiales */}
                  <div className="form-grid-full">
                    <label className="block text-sm font-medium text-secondary mb-2">
                      <FontAwesomeIcon icon={faFileAlt} className="mr-2" />
                      Cláusulas Especiales
                    </label>
                    <Input
                      name="clausulas_especiales"
                      type="text"
                      defaultValue={editingContrato?.clausulas_especiales || ''}
                      placeholder="Ej: Disponibilidad 24/7 para emergencias"
                      className="w-full"
                    />
                  </div>

                  {/* Observaciones */}
                  <div className="form-grid-full">
                    <label className="block text-sm font-medium text-secondary mb-2">
                      <FontAwesomeIcon icon={faFileAlt} className="mr-2" />
                      Observaciones
                    </label>
                    <textarea
                      name="observaciones"
                      rows={3}
                      defaultValue={editingContrato?.observaciones || ''}
                      placeholder="Observaciones adicionales sobre el contrato..."
                      className="w-full p-3 rounded-lg bg-secondary border border-color text-primary focus:ring-2 focus:ring-primary resize-none"
                    />
                  </div>
                </div>

                <div className="flex justify-end gap-3 mt-6">
                  <Button
                    type="button"
                    variant="outline"
                    onClick={() => {
                      setIsModalOpen(false);
                      setEditingContrato(null);
                    }}
                  >
                    Cancelar
                  </Button>
                  <Button type="submit">
                    <FontAwesomeIcon icon={editingContrato ? faEdit : faPlus} className="mr-2" />
                    {editingContrato ? 'Actualizar' : 'Crear'} Contrato
                  </Button>
                </div>
              </form>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default GestionContratos;
